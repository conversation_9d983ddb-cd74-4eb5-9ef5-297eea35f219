{"name": "@moxo/identity", "type": "module", "version": "3.10.2", "packageManager": "pnpm@10.12.1", "description": "An mobile web apps template based on the Vue 3 ecosystem", "license": "MIT", "scripts": {"dev": "cross-env MOCK_SERVER_PORT=8086 vite", "build:dev": "vue-tsc --noEmit && vite build --mode development", "build:pro": "vue-tsc --noEmit && vite build --mode production", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "release": "bumpp --commit --push --tag", "typecheck": "vue-tsc --noEmit", "commitlint": "commitlint --edit", "prepare": "simple-git-hooks"}, "dependencies": {"@unhead/vue": "2.0.10", "@vant/touch-emulator": "^1.4.0", "@vant/use": "^1.6.0", "@vueuse/core": "^13.3.0", "axios": "^1.9.0", "echarts": "^5.6.0", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "resize-detector": "^0.3.0", "vant": "^4.9.20", "vconsole": "^3.15.1", "vue": "^3.5.16", "vue-i18n": "^11.1.5", "vue-router": "^4.5.1"}, "devDependencies": {"@antfu/eslint-config": "4.14.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/types": "^19.8.1", "@iconify-json/carbon": "^1.2.9", "@intlify/unplugin-vue-i18n": "^6.0.8", "@types/lodash-es": "^4.17.12", "@types/node": "^24.0.1", "@types/nprogress": "^0.2.3", "@unocss/eslint-config": "66.2.0", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "bumpp": "^10.1.1", "consola": "^3.4.2", "cross-env": "^7.0.3", "eslint": "^9.29.0", "eslint-plugin-format": "^1.0.1", "less": "^4.3.0", "lint-staged": "^16.1.0", "mockjs": "^1.1.0", "postcss-mobile-forever": "^5.0.0", "rollup": "^4.43.0", "simple-git-hooks": "^2.13.0", "terser": "^5.42.0", "typescript": "^5.8.3", "unocss": "66.2.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "unplugin-vue-router": "^0.12.0", "vite": "^6.3.5", "vite-plugin-mock-dev-server": "^1.8.7", "vite-plugin-pwa": "^1.0.0", "vite-plugin-sitemap": "^0.8.2", "vite-plugin-vconsole": "^2.1.1", "vite-plugin-vue-devtools": "^7.7.6", "vue-tsc": "^2.2.10", "@moxo/tsconfig": "workspace:*"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged", "commit-msg": "pnpm commitlint $1"}, "lint-staged": {"*": "eslint --fix"}}