# SFC `<route>` custom block

We used SFC <route> [`<route>`](https://uvr.esm.is/guide/extending-routes.html#sfc-route-custom-block) custom block to define the route name and meta information for each page, making it easy to control the transition animations for each route.

我们使用 SFC [`<route>`](https://uvr.esm.is/guide/extending-routes.html#sfc-route-custom-block) 自定义块定义每个页面的路由名称和元信息，可以轻松控制每个路由的过渡动画。
