import { SysStorage } from './storage';
import { Logger } from './logger';

export class LocalStorage<T> implements SysStorage<T> {
  /**
   * 从 localStorage 获取指定 key 的值
   * @param key 存储的键名
   * @returns Promise<T | null> 返回解析后的值或 null
   */
  async getItem(key: string): Promise<T | null> {
    try {
      // 检查 localStorage 是否可用
      if (typeof window === 'undefined' || !window.localStorage) {
        Logger.warn('localStorage is not available');
        return null;
      }

      const item = window.localStorage.getItem(key);
      if (item === null) {
        return null;
      }

      // 尝试解析 JSON，如果失败则返回原始字符串
      try {
        return JSON.parse(item) as T;
      } catch (parseError) {
        // 如果不是有效的 JSON，直接返回字符串值（适用于 T 为 string 的情况）
        return item as unknown as T;
      }
    } catch (error) {
      Logger.error('Error getting item from localStorage:', error);
      return null;
    }
  }

  /**
   * 向 localStorage 存储指定 key 的值
   * @param key 存储的键名
   * @param value 要存储的值
   */
  setItem(key: string, value: T): void {
    try {
      // 检查 localStorage 是否可用
      if (typeof window === 'undefined' || !window.localStorage) {
        Logger.warn('localStorage is not available');
        return;
      }

      // 如果值是字符串类型，直接存储；否则序列化为 JSON
      const serializedValue = typeof value === 'string' ? (value as string) : JSON.stringify(value);

      window.localStorage.setItem(key, serializedValue);
    } catch (error) {
      Logger.error('Error setting item to localStorage:', error);
    }
  }

  /**
   * 从 localStorage 删除指定 key 的值
   * @param key 要删除的键名
   */
  removeItem(key: string): void {
    try {
      // 检查 localStorage 是否可用
      if (typeof window === 'undefined' || !window.localStorage) {
        Logger.warn('localStorage is not available');
        return;
      }

      window.localStorage.removeItem(key);
    } catch (error) {
      Logger.error('Error removing item from localStorage:', error);
    }
  }

  /**
   * 清空 localStorage 中的所有数据
   */
  clear(): void {
    try {
      // 检查 localStorage 是否可用
      if (typeof window === 'undefined' || !window.localStorage) {
        Logger.warn('localStorage is not available');
        return;
      }

      window.localStorage.clear();
    } catch (error) {
      Logger.error('Error clearing localStorage:', error);
    }
  }
}
