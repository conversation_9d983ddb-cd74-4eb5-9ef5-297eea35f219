"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const localStorage_1 = require("./localStorage");
// Mock localStorage
const localStorageMock = {
    getItem: vitest_1.vi.fn(),
    setItem: vitest_1.vi.fn(),
    removeItem: vitest_1.vi.fn(),
    clear: vitest_1.vi.fn(),
};
// Mock window object
Object.defineProperty(globalThis, 'window', {
    value: {
        localStorage: localStorageMock,
    },
    writable: true,
});
(0, vitest_1.describe)('LocalStorage', () => {
    let storage;
    (0, vitest_1.beforeEach)(() => {
        storage = new localStorage_1.LocalStorage();
        vitest_1.vi.clearAllMocks();
    });
    (0, vitest_1.describe)('getItem', () => {
        (0, vitest_1.it)('should return null when item does not exist', () => __awaiter(void 0, void 0, void 0, function* () {
            localStorageMock.getItem.mockReturnValue(null);
            const result = yield storage.getItem('nonexistent');
            (0, vitest_1.expect)(result).toBeNull();
            (0, vitest_1.expect)(localStorageMock.getItem).toHaveBeenCalledWith('nonexistent');
        }));
        (0, vitest_1.it)('should return parsed JSON object', () => __awaiter(void 0, void 0, void 0, function* () {
            const testObject = { name: 'test', value: 123 };
            localStorageMock.getItem.mockReturnValue(JSON.stringify(testObject));
            const result = yield storage.getItem('test-key');
            (0, vitest_1.expect)(result).toEqual(testObject);
            (0, vitest_1.expect)(localStorageMock.getItem).toHaveBeenCalledWith('test-key');
        }));
        (0, vitest_1.it)('should return string value when JSON parsing fails', () => __awaiter(void 0, void 0, void 0, function* () {
            const testString = 'simple string';
            localStorageMock.getItem.mockReturnValue(testString);
            const result = yield storage.getItem('string-key');
            (0, vitest_1.expect)(result).toBe(testString);
        }));
        (0, vitest_1.it)('should return null when localStorage throws error', () => __awaiter(void 0, void 0, void 0, function* () {
            localStorageMock.getItem.mockImplementation(() => {
                throw new Error('localStorage error');
            });
            const consoleSpy = vitest_1.vi.spyOn(console, 'error').mockImplementation(() => { });
            const result = yield storage.getItem('error-key');
            (0, vitest_1.expect)(result).toBeNull();
            (0, vitest_1.expect)(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        }));
    });
    (0, vitest_1.describe)('setItem', () => {
        (0, vitest_1.it)('should store string value directly', () => {
            const testString = 'test string';
            storage.setItem('string-key', testString);
            (0, vitest_1.expect)(localStorageMock.setItem).toHaveBeenCalledWith('string-key', testString);
        });
        (0, vitest_1.it)('should store object as JSON string', () => {
            const testObject = { name: 'test', value: 123 };
            storage.setItem('object-key', testObject);
            (0, vitest_1.expect)(localStorageMock.setItem).toHaveBeenCalledWith('object-key', JSON.stringify(testObject));
        });
        (0, vitest_1.it)('should handle localStorage errors gracefully', () => {
            localStorageMock.setItem.mockImplementation(() => {
                throw new Error('localStorage error');
            });
            const consoleSpy = vitest_1.vi.spyOn(console, 'error').mockImplementation(() => { });
            (0, vitest_1.expect)(() => storage.setItem('error-key', 'value')).not.toThrow();
            (0, vitest_1.expect)(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });
    (0, vitest_1.describe)('removeItem', () => {
        (0, vitest_1.it)('should remove item from localStorage', () => {
            storage.removeItem('test-key');
            (0, vitest_1.expect)(localStorageMock.removeItem).toHaveBeenCalledWith('test-key');
        });
        (0, vitest_1.it)('should handle localStorage errors gracefully', () => {
            localStorageMock.removeItem.mockImplementation(() => {
                throw new Error('localStorage error');
            });
            const consoleSpy = vitest_1.vi.spyOn(console, 'error').mockImplementation(() => { });
            (0, vitest_1.expect)(() => storage.removeItem('error-key')).not.toThrow();
            (0, vitest_1.expect)(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });
    (0, vitest_1.describe)('clear', () => {
        (0, vitest_1.it)('should clear all items from localStorage', () => {
            storage.clear();
            (0, vitest_1.expect)(localStorageMock.clear).toHaveBeenCalled();
        });
        (0, vitest_1.it)('should handle localStorage errors gracefully', () => {
            localStorageMock.clear.mockImplementation(() => {
                throw new Error('localStorage error');
            });
            const consoleSpy = vitest_1.vi.spyOn(console, 'error').mockImplementation(() => { });
            (0, vitest_1.expect)(() => storage.clear()).not.toThrow();
            (0, vitest_1.expect)(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });
    (0, vitest_1.describe)('when localStorage is not available', () => {
        (0, vitest_1.beforeEach)(() => {
            // Mock window.localStorage as undefined
            Object.defineProperty(globalThis, 'window', {
                value: {
                    localStorage: undefined,
                },
                writable: true,
            });
            storage = new localStorage_1.LocalStorage();
        });
        (0, vitest_1.it)('should return null for getItem', () => __awaiter(void 0, void 0, void 0, function* () {
            const consoleSpy = vitest_1.vi.spyOn(console, 'warn').mockImplementation(() => { });
            const result = yield storage.getItem('test');
            (0, vitest_1.expect)(result).toBeNull();
            (0, vitest_1.expect)(consoleSpy).toHaveBeenCalledWith('localStorage is not available');
            consoleSpy.mockRestore();
        }));
        (0, vitest_1.it)('should do nothing for setItem', () => {
            const consoleSpy = vitest_1.vi.spyOn(console, 'warn').mockImplementation(() => { });
            (0, vitest_1.expect)(() => storage.setItem('test', 'value')).not.toThrow();
            (0, vitest_1.expect)(consoleSpy).toHaveBeenCalledWith('localStorage is not available');
            consoleSpy.mockRestore();
        });
        (0, vitest_1.it)('should do nothing for removeItem', () => {
            const consoleSpy = vitest_1.vi.spyOn(console, 'warn').mockImplementation(() => { });
            (0, vitest_1.expect)(() => storage.removeItem('test')).not.toThrow();
            (0, vitest_1.expect)(consoleSpy).toHaveBeenCalledWith('localStorage is not available');
            consoleSpy.mockRestore();
        });
        (0, vitest_1.it)('should do nothing for clear', () => {
            const consoleSpy = vitest_1.vi.spyOn(console, 'warn').mockImplementation(() => { });
            (0, vitest_1.expect)(() => storage.clear()).not.toThrow();
            (0, vitest_1.expect)(consoleSpy).toHaveBeenCalledWith('localStorage is not available');
            consoleSpy.mockRestore();
        });
    });
});
