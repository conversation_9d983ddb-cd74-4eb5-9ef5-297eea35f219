"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = void 0;
exports.warn = warn;
exports.error = error;
exports.debug = debug;
exports.log = log;
exports.info = info;
/**
 * 输出警告信息到控制台
 * @param args - 要输出的参数
 */
function warn(...args) {
    console.warn(...args);
}
/**
 * 输出错误信息到控制台
 * @param args - 要输出的参数
 */
function error(...args) {
    console.error(...args);
}
/**
 * 输出调试信息到控制台
 * @param args - 要输出的参数
 */
function debug(...args) {
    console.debug(...args);
}
function log(...args) {
    console.log(...args);
}
function info(...args) {
    console.info(...args);
}
exports.Logger = {
    warn,
    error,
    info,
    log,
    debug,
};
