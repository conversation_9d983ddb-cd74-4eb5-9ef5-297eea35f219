"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexedDBStorage = void 0;
const logger_1 = require("./logger");
class IndexedDBStorage {
    constructor(dbName = 'DefaultDB', storeName = 'keyValueStore', version = 1) {
        Object.defineProperty(this, "dbName", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "storeName", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "version", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "db", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
        this.dbName = dbName;
        this.storeName = storeName;
        this.version = version;
    }
    /**
     * 初始化 IndexedDB 数据库
     * @returns Promise<IDBDatabase>
     */
    initDB() {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.db) {
                return this.db;
            }
            // 检查 IndexedDB 是否可用
            if (typeof window === 'undefined' || !window.indexedDB) {
                throw new Error('IndexedDB is not available');
            }
            return new Promise((resolve, reject) => {
                const request = window.indexedDB.open(this.dbName, this.version);
                request.onerror = () => {
                    var _a;
                    reject(new Error(`Failed to open IndexedDB: ${(_a = request.error) === null || _a === void 0 ? void 0 : _a.message}`));
                };
                request.onsuccess = () => {
                    this.db = request.result;
                    resolve(request.result);
                };
                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    // 创建对象存储（如果不存在）
                    if (!db.objectStoreNames.contains(this.storeName)) {
                        db.createObjectStore(this.storeName, { keyPath: 'key' });
                    }
                };
            });
        });
    }
    /**
     * 从 IndexedDB 获取指定 key 的值
     * @param key 存储的键名
     * @returns Promise<T | null> 返回存储的值或 null
     */
    getItem(key) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const db = yield this.initDB();
                return new Promise((resolve, reject) => {
                    const transaction = db.transaction([this.storeName], 'readonly');
                    const store = transaction.objectStore(this.storeName);
                    const request = store.get(key);
                    request.onerror = () => {
                        var _a;
                        reject(new Error(`Failed to get item: ${(_a = request.error) === null || _a === void 0 ? void 0 : _a.message}`));
                    };
                    request.onsuccess = () => {
                        const result = request.result;
                        resolve(result ? result.value : null);
                    };
                });
            }
            catch (error) {
                logger_1.Logger.error('Error getting item from IndexedDB:', error);
                return null;
            }
        });
    }
    /**
     * 向 IndexedDB 存储指定 key 的值
     * @param key 存储的键名
     * @param value 要存储的值
     * @returns Promise<void>
     */
    setItem(key, value) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const db = yield this.initDB();
                return new Promise((resolve, reject) => {
                    const transaction = db.transaction([this.storeName], 'readwrite');
                    const store = transaction.objectStore(this.storeName);
                    const request = store.put({ key, value });
                    request.onerror = () => {
                        var _a;
                        reject(new Error(`Failed to set item: ${(_a = request.error) === null || _a === void 0 ? void 0 : _a.message}`));
                    };
                    request.onsuccess = () => {
                        resolve();
                    };
                });
            }
            catch (error) {
                logger_1.Logger.error('Error setting item to IndexedDB:', error);
                throw error;
            }
        });
    }
    /**
     * 从 IndexedDB 删除指定 key 的值
     * @param key 要删除的键名
     * @returns Promise<void>
     */
    removeItem(key) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const db = yield this.initDB();
                return new Promise((resolve, reject) => {
                    const transaction = db.transaction([this.storeName], 'readwrite');
                    const store = transaction.objectStore(this.storeName);
                    const request = store.delete(key);
                    request.onerror = () => {
                        var _a;
                        reject(new Error(`Failed to remove item: ${(_a = request.error) === null || _a === void 0 ? void 0 : _a.message}`));
                    };
                    request.onsuccess = () => {
                        resolve();
                    };
                });
            }
            catch (error) {
                logger_1.Logger.error('Error removing item from IndexedDB:', error);
                throw error;
            }
        });
    }
    /**
     * 清空 IndexedDB 中的所有数据
     * @returns Promise<void>
     */
    clear() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const db = yield this.initDB();
                return new Promise((resolve, reject) => {
                    const transaction = db.transaction([this.storeName], 'readwrite');
                    const store = transaction.objectStore(this.storeName);
                    const request = store.clear();
                    request.onerror = () => {
                        var _a;
                        reject(new Error(`Failed to clear store: ${(_a = request.error) === null || _a === void 0 ? void 0 : _a.message}`));
                    };
                    request.onsuccess = () => {
                        resolve();
                    };
                });
            }
            catch (error) {
                logger_1.Logger.error('Error clearing IndexedDB:', error);
                throw error;
            }
        });
    }
    /**
     * 关闭数据库连接
     */
    close() {
        if (this.db) {
            this.db.close();
            this.db = null;
        }
    }
}
exports.IndexedDBStorage = IndexedDBStorage;
