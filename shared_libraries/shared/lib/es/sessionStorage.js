var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { Logger } from './logger';
export class SessionStorage {
    /**
     * 从 sessionStorage 获取指定 key 的值
     * @param key 存储的键名
     * @returns Promise<T | null> 返回解析后的值或 null
     */
    getItem(key) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // 检查 sessionStorage 是否可用
                if (typeof window === 'undefined' || !window.sessionStorage) {
                    Logger.warn('sessionStorage is not available');
                    return null;
                }
                const item = window.sessionStorage.getItem(key);
                if (item === null) {
                    return null;
                }
                // 尝试解析 JSON，如果失败则返回原始字符串
                try {
                    return JSON.parse(item);
                }
                catch (parseError) {
                    // 如果不是有效的 JSON，直接返回字符串值（适用于 T 为 string 的情况）
                    return item;
                }
            }
            catch (error) {
                Logger.error('Error getting item from sessionStorage:', error);
                return null;
            }
        });
    }
    /**
     * 向 sessionStorage 存储指定 key 的值
     * @param key 存储的键名
     * @param value 要存储的值
     */
    setItem(key, value) {
        try {
            // 检查 sessionStorage 是否可用
            if (typeof window === 'undefined' || !window.sessionStorage) {
                Logger.warn('sessionStorage is not available');
                return;
            }
            // 如果值是字符串类型，直接存储；否则序列化为 JSON
            const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
            window.sessionStorage.setItem(key, serializedValue);
        }
        catch (error) {
            Logger.error('Error setting item to sessionStorage:', error);
        }
    }
    /**
     * 从 sessionStorage 删除指定 key 的值
     * @param key 要删除的键名
     */
    removeItem(key) {
        try {
            // 检查 sessionStorage 是否可用
            if (typeof window === 'undefined' || !window.sessionStorage) {
                Logger.warn('sessionStorage is not available');
                return;
            }
            window.sessionStorage.removeItem(key);
        }
        catch (error) {
            Logger.error('Error removing item from sessionStorage:', error);
        }
    }
    /**
     * 清空 sessionStorage 中的所有数据
     */
    clear() {
        try {
            // 检查 sessionStorage 是否可用
            if (typeof window === 'undefined' || !window.sessionStorage) {
                Logger.warn('sessionStorage is not available');
                return;
            }
            window.sessionStorage.clear();
        }
        catch (error) {
            Logger.error('Error clearing sessionStorage:', error);
        }
    }
}
