import { SysStorage } from './storage';
export declare class LocalStorage<T> implements SysStorage<T> {
    /**
     * 从 localStorage 获取指定 key 的值
     * @param key 存储的键名
     * @returns Promise<T | null> 返回解析后的值或 null
     */
    getItem(key: string): Promise<T | null>;
    /**
     * 向 localStorage 存储指定 key 的值
     * @param key 存储的键名
     * @param value 要存储的值
     */
    setItem(key: string, value: T): void;
    /**
     * 从 localStorage 删除指定 key 的值
     * @param key 要删除的键名
     */
    removeItem(key: string): void;
    /**
     * 清空 localStorage 中的所有数据
     */
    clear(): void;
}
//# sourceMappingURL=localStorage.d.ts.map