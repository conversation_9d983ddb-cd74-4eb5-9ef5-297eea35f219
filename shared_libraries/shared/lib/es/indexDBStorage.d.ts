import { SysStorage } from './storage';
export declare class IndexedDBStorage<T> implements SysStorage<T> {
    private dbName;
    private storeName;
    private version;
    private db;
    constructor(dbName?: string, storeName?: string, version?: number);
    /**
     * 初始化 IndexedDB 数据库
     * @returns Promise<IDBDatabase>
     */
    private initDB;
    /**
     * 从 IndexedDB 获取指定 key 的值
     * @param key 存储的键名
     * @returns Promise<T | null> 返回存储的值或 null
     */
    getItem(key: string): Promise<T | null>;
    /**
     * 向 IndexedDB 存储指定 key 的值
     * @param key 存储的键名
     * @param value 要存储的值
     * @returns Promise<void>
     */
    setItem(key: string, value: T): Promise<void>;
    /**
     * 从 IndexedDB 删除指定 key 的值
     * @param key 要删除的键名
     * @returns Promise<void>
     */
    removeItem(key: string): Promise<void>;
    /**
     * 清空 IndexedDB 中的所有数据
     * @returns Promise<void>
     */
    clear(): Promise<void>;
    /**
     * 关闭数据库连接
     */
    close(): void;
}
//# sourceMappingURL=indexDBStorage.d.ts.map