var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { LocalStorage } from './localStorage';
// Mock localStorage
const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
};
// Mock window object
Object.defineProperty(globalThis, 'window', {
    value: {
        localStorage: localStorageMock,
    },
    writable: true,
});
describe('LocalStorage', () => {
    let storage;
    beforeEach(() => {
        storage = new LocalStorage();
        vi.clearAllMocks();
    });
    describe('getItem', () => {
        it('should return null when item does not exist', () => __awaiter(void 0, void 0, void 0, function* () {
            localStorageMock.getItem.mockReturnValue(null);
            const result = yield storage.getItem('nonexistent');
            expect(result).toBeNull();
            expect(localStorageMock.getItem).toHaveBeenCalledWith('nonexistent');
        }));
        it('should return parsed JSON object', () => __awaiter(void 0, void 0, void 0, function* () {
            const testObject = { name: 'test', value: 123 };
            localStorageMock.getItem.mockReturnValue(JSON.stringify(testObject));
            const result = yield storage.getItem('test-key');
            expect(result).toEqual(testObject);
            expect(localStorageMock.getItem).toHaveBeenCalledWith('test-key');
        }));
        it('should return string value when JSON parsing fails', () => __awaiter(void 0, void 0, void 0, function* () {
            const testString = 'simple string';
            localStorageMock.getItem.mockReturnValue(testString);
            const result = yield storage.getItem('string-key');
            expect(result).toBe(testString);
        }));
        it('should return null when localStorage throws error', () => __awaiter(void 0, void 0, void 0, function* () {
            localStorageMock.getItem.mockImplementation(() => {
                throw new Error('localStorage error');
            });
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });
            const result = yield storage.getItem('error-key');
            expect(result).toBeNull();
            expect(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        }));
    });
    describe('setItem', () => {
        it('should store string value directly', () => {
            const testString = 'test string';
            storage.setItem('string-key', testString);
            expect(localStorageMock.setItem).toHaveBeenCalledWith('string-key', testString);
        });
        it('should store object as JSON string', () => {
            const testObject = { name: 'test', value: 123 };
            storage.setItem('object-key', testObject);
            expect(localStorageMock.setItem).toHaveBeenCalledWith('object-key', JSON.stringify(testObject));
        });
        it('should handle localStorage errors gracefully', () => {
            localStorageMock.setItem.mockImplementation(() => {
                throw new Error('localStorage error');
            });
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });
            expect(() => storage.setItem('error-key', 'value')).not.toThrow();
            expect(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });
    describe('removeItem', () => {
        it('should remove item from localStorage', () => {
            storage.removeItem('test-key');
            expect(localStorageMock.removeItem).toHaveBeenCalledWith('test-key');
        });
        it('should handle localStorage errors gracefully', () => {
            localStorageMock.removeItem.mockImplementation(() => {
                throw new Error('localStorage error');
            });
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });
            expect(() => storage.removeItem('error-key')).not.toThrow();
            expect(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });
    describe('clear', () => {
        it('should clear all items from localStorage', () => {
            storage.clear();
            expect(localStorageMock.clear).toHaveBeenCalled();
        });
        it('should handle localStorage errors gracefully', () => {
            localStorageMock.clear.mockImplementation(() => {
                throw new Error('localStorage error');
            });
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });
            expect(() => storage.clear()).not.toThrow();
            expect(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });
    describe('when localStorage is not available', () => {
        beforeEach(() => {
            // Mock window.localStorage as undefined
            Object.defineProperty(globalThis, 'window', {
                value: {
                    localStorage: undefined,
                },
                writable: true,
            });
            storage = new LocalStorage();
        });
        it('should return null for getItem', () => __awaiter(void 0, void 0, void 0, function* () {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
            const result = yield storage.getItem('test');
            expect(result).toBeNull();
            expect(consoleSpy).toHaveBeenCalledWith('localStorage is not available');
            consoleSpy.mockRestore();
        }));
        it('should do nothing for setItem', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
            expect(() => storage.setItem('test', 'value')).not.toThrow();
            expect(consoleSpy).toHaveBeenCalledWith('localStorage is not available');
            consoleSpy.mockRestore();
        });
        it('should do nothing for removeItem', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
            expect(() => storage.removeItem('test')).not.toThrow();
            expect(consoleSpy).toHaveBeenCalledWith('localStorage is not available');
            consoleSpy.mockRestore();
        });
        it('should do nothing for clear', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
            expect(() => storage.clear()).not.toThrow();
            expect(consoleSpy).toHaveBeenCalledWith('localStorage is not available');
            consoleSpy.mockRestore();
        });
    });
});
