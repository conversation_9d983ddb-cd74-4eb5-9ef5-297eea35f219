/**
 * 输出警告信息到控制台
 * @param args - 要输出的参数
 */
export function warn(...args) {
    console.warn(...args);
}
/**
 * 输出错误信息到控制台
 * @param args - 要输出的参数
 */
export function error(...args) {
    console.error(...args);
}
/**
 * 输出调试信息到控制台
 * @param args - 要输出的参数
 */
export function debug(...args) {
    console.debug(...args);
}
export function log(...args) {
    console.log(...args);
}
export function info(...args) {
    console.info(...args);
}
export const Logger = {
    warn,
    error,
    info,
    log,
    debug,
};
