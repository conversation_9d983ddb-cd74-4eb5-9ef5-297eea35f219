<template>
  <component
    :is="iconComponent"
    :class="iconClass"
    :style="iconStyle"
    v-bind="$attrs"
  />
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, type Component } from 'vue'
import type { MoxoIconName, IconProps } from './types'

interface Props extends IconProps {
  /**
   * 图标名称
   */
  name: MoxoIconName
}

const props = withDefaults(defineProps<Props>(), {
  size: 24,
  color: 'currentColor'
})

// 动态导入图标组件
const iconComponent = computed<Component>(() => {
  return defineAsyncComponent(() => import(`~icons/moxo/${props.name}`))
})

// 计算图标类名
const iconClass = computed(() => {
  const classes = ['moxo-icon']
  if (props.class) {
    classes.push(props.class)
  }
  return classes.join(' ')
})

// 计算图标样式
const iconStyle = computed(() => {
  const styles: Record<string, any> = {}
  
  // 设置大小
  if (props.size) {
    const size = typeof props.size === 'number' ? `${props.size}px` : props.size
    styles.width = size
    styles.height = size
  }
  
  // 设置颜色
  if (props.color) {
    styles.color = props.color
  }
  
  // 设置旋转
  if (props.rotate) {
    styles.transform = `rotate(${props.rotate}deg)`
  }
  
  // 设置翻转
  if (props.flip) {
    const transforms = []
    if (props.flip === 'horizontal' || props.flip === 'both') {
      transforms.push('scaleX(-1)')
    }
    if (props.flip === 'vertical' || props.flip === 'both') {
      transforms.push('scaleY(-1)')
    }
    if (transforms.length > 0) {
      styles.transform = transforms.join(' ')
    }
  }
  
  // 合并用户自定义样式
  if (props.style) {
    if (typeof props.style === 'string') {
      return [styles, props.style]
    } else {
      Object.assign(styles, props.style)
    }
  }
  
  return styles
})
</script>

<style scoped>
.moxo-icon {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
}
</style>
