<template>
  <svg :class="iconClass" :style="iconStyle" :width="size" :height="size" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 47.1 (45422) - http://www.bohemiancoding.com/sketch -->
    <title>Icon/Default/Smile</title>
    <desc>Created with <PERSON>ketch.</desc>
    <defs>
        <path d="M12,2 C6.477,2 2,6.477 2,12 C2,17.523 6.477,22 12,22 C17.523,22 22,17.523 22,12 C22,6.477 17.523,2 12,2 M12,4 C16.411,4 20,7.589 20,12 C20,16.411 16.411,20 12,20 C7.589,20 4,16.411 4,12 C4,7.589 7.589,4 12,4 Z M14,14 C14,15.1047153 13.1047153,16 12,16 C10.8952847,16 10,15.1047153 10,14 L8,14 C8,16.2092847 9.79071525,18 12,18 C14.2092847,18 16,16.2092847 16,14 <PERSON>14,14 <PERSON> <PERSON>0,9.5 C10,8.671 9.329,8 8.5,8 C7.671,8 7,8.671 7,9.5 C7,10.329 7.671,11 8.5,11 C9.329,11 10,10.329 10,9.5 Z M17,9.5 C17,8.671 16.329,8 15.5,8 C14.671,8 14,8.671 14,9.5 C14,10.329 14.671,11 15.5,11 C16.329,11 17,10.329 17,9.5 Z" id="path-1"></path>
    </defs>
    <g id="Default" stroke="none" stroke-:width="size" fill="currentColor" fill-rule="evenodd">
        <g id="Icon/Default/Smile">
            <mask id="mask-2" fill="currentColor">
                <use xlink:href="#path-1"></use>
            </mask>
            <use id="Mask" fill="currentColor" xlink:href="#path-1"></use>
        </g>
    </g>
</svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: string | number
  color?: string
  class?: string
  style?: string | Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  size: 24,
  color: 'currentColor'
})

const iconClass = computed(() => {
  const classes = ['moxo-icon', 'moxo-icon-smile']
  if (props.class) {
    classes.push(props.class)
  }
  return classes.join(' ')
})

const iconStyle = computed(() => {
  const styles: Record<string, any> = {}
  
  if (props.color) {
    styles.color = props.color
  }
  
  if (props.style) {
    if (typeof props.style === 'string') {
      return [styles, props.style]
    } else {
      Object.assign(styles, props.style)
    }
  }
  
  return styles
})
</script>

<style scoped>
.moxo-icon {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
}
</style>