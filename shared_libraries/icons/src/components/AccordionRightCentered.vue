<template>
  <svg :class="iconClass" :style="iconStyle" :width="size" :height="size" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_354_16715)">
<path d="M11.4853 7.94971C11.4853 8.20563 11.3876 8.46155 11.1924 8.65681L7.65685 12.1923C7.26633 12.5829 6.63316 12.5829 6.24264 12.1923C5.85212 11.8018 5.85212 11.1687 6.24264 10.7781L9.07107 7.94971L6.24264 5.12128C5.85212 4.73076 5.85212 4.09759 6.24264 3.70707C6.63316 3.31654 7.26633 3.31654 7.65685 3.70707L11.1924 7.2426C11.3876 7.43786 11.4853 7.69378 11.4853 7.94971Z" fill="currentColor"/>
</g>
<defs>
<clipPath id="clip0_354_16715">
<rect :width="size" :height="size" fill="currentColor"/>
</clipPath>
</defs>
</svg>

</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: string | number
  color?: string
  class?: string
  style?: string | Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  size: 24,
  color: 'currentColor'
})

const iconClass = computed(() => {
  const classes = ['moxo-icon', 'moxo-icon-accordion-right-centered']
  if (props.class) {
    classes.push(props.class)
  }
  return classes.join(' ')
})

const iconStyle = computed(() => {
  const styles: Record<string, any> = {}
  
  if (props.color) {
    styles.color = props.color
  }
  
  if (props.style) {
    if (typeof props.style === 'string') {
      return [styles, props.style]
    } else {
      Object.assign(styles, props.style)
    }
  }
  
  return styles
})
</script>

<style scoped>
.moxo-icon {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
}
</style>