<template>
  <svg :class="iconClass" :style="iconStyle" :width="size" :height="size" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<path d="M20.958 16.7979L16.7256 21.0302C16.5304 21.2255 16.2138 21.2255 16.0185 21.0302L13.3721 18.3838L14.7861 16.9697L16.3711 18.5547L19.543 15.3838L20.958 16.7979Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.5 3C14.5376 3 17 5.46244 17 8.5C17 10.1951 16.2321 11.7098 15.0264 12.7188C15.3124 12.8407 15.5913 12.9762 15.8613 13.126C16.1023 13.2597 16.3368 13.4041 16.5635 13.5586C16.8746 13.7707 17.1717 14.0017 17.4531 14.25L16.1299 15.75C15.9106 15.5564 15.6788 15.3761 15.4365 15.2109C15.2602 15.0907 15.0779 14.978 14.8906 14.874C13.8871 14.3172 12.7321 14 11.5 14C7.634 14 4.5 17.134 4.5 21H2.5C2.5 17.2817 4.75478 14.09 7.97168 12.7178C6.76691 11.7089 6 10.1944 6 8.5C6 5.46244 8.46243 3 11.5 3ZM11.5 5C9.567 5 8 6.567 8 8.5C8 10.433 9.567 12 11.5 12C13.433 12 15 10.433 15 8.5C15 6.567 13.433 5 11.5 5Z" fill="currentColor"/>
</svg>

</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: string | number
  color?: string
  class?: string
  style?: string | Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  size: 24,
  color: 'currentColor'
})

const iconClass = computed(() => {
  const classes = ['moxo-icon', 'moxo-icon-completed-by-you-b']
  if (props.class) {
    classes.push(props.class)
  }
  return classes.join(' ')
})

const iconStyle = computed(() => {
  const styles: Record<string, any> = {}
  
  if (props.color) {
    styles.color = props.color
  }
  
  if (props.style) {
    if (typeof props.style === 'string') {
      return [styles, props.style]
    } else {
      Object.assign(styles, props.style)
    }
  }
  
  return styles
})
</script>

<style scoped>
.moxo-icon {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
}
</style>