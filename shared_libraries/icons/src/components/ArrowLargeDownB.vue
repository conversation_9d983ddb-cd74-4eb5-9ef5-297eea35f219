<template>
  <svg :class="iconClass" :style="iconStyle" :width="size" :height="size" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<path d="M12.9961 14.5898L16.793 10.793L18.207 12.207L12.3536 18.0605C12.1583 18.2558 11.8417 18.2558 11.6464 18.0605L5.79297 12.207L7.20703 10.793L10.9961 14.582V5H12.9961V14.5898Z" fill="currentColor"/>
</svg>

</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: string | number
  color?: string
  class?: string
  style?: string | Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  size: 24,
  color: 'currentColor'
})

const iconClass = computed(() => {
  const classes = ['moxo-icon', 'moxo-icon-arrow-large-down-b']
  if (props.class) {
    classes.push(props.class)
  }
  return classes.join(' ')
})

const iconStyle = computed(() => {
  const styles: Record<string, any> = {}
  
  if (props.color) {
    styles.color = props.color
  }
  
  if (props.style) {
    if (typeof props.style === 'string') {
      return [styles, props.style]
    } else {
      Object.assign(styles, props.style)
    }
  }
  
  return styles
})
</script>

<style scoped>
.moxo-icon {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
}
</style>