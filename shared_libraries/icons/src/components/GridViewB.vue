<template>
  <svg :class="iconClass" :style="iconStyle" :width="size" :height="size" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M11 20C11 20.5523 10.5523 21 10 21H4C3.44772 21 3 20.5523 3 20V14C3 13.4477 3.44772 13 4 13H10C10.5523 13 11 13.4477 11 14V20ZM5 19H9V15H5V19Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M21 20C21 20.5523 20.5523 21 20 21H14C13.4477 21 13 20.5523 13 20V14C13 13.4477 13.4477 13 14 13H20C20.5523 13 21 13.4477 21 14V20ZM15 19H19V15H15V19Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11 10C11 10.5523 10.5523 11 10 11H4C3.44772 11 3 10.5523 3 10V4C3 3.44772 3.44772 3 4 3H10C10.5523 3 11 3.44772 11 4V10ZM5 9H9V5H5V9Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M21 10C21 10.5523 20.5523 11 20 11H14C13.4477 11 13 10.5523 13 10V4C13 3.44772 13.4477 3 14 3H20C20.5523 3 21 3.44772 21 4V10ZM15 9H19V5H15V9Z" fill="currentColor"/>
</svg>

</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: string | number
  color?: string
  class?: string
  style?: string | Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  size: 24,
  color: 'currentColor'
})

const iconClass = computed(() => {
  const classes = ['moxo-icon', 'moxo-icon-grid-view-b']
  if (props.class) {
    classes.push(props.class)
  }
  return classes.join(' ')
})

const iconStyle = computed(() => {
  const styles: Record<string, any> = {}
  
  if (props.color) {
    styles.color = props.color
  }
  
  if (props.style) {
    if (typeof props.style === 'string') {
      return [styles, props.style]
    } else {
      Object.assign(styles, props.style)
    }
  }
  
  return styles
})
</script>

<style scoped>
.moxo-icon {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
}
</style>