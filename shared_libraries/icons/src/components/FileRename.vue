<template>
  <svg :class="iconClass" :style="iconStyle" :width="size" :height="size" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 53.2 (72643) - https://sketchapp.com -->
    <title>Icon/04 - Flat/File-Rename</title>
    <desc>Created with Sketch.</desc>
    <g id="Icon/04---Flat/File-Rename" stroke="none" stroke-:width="size" fill="currentColor" fill-rule="evenodd">
        <path d="M15,6 L23,6 L23,8 L21,8 L21,16 L23,16 L23,18 L15,18 L15,21 L17,21 L17,23 L11,23 L11,21 L13,21 L13,18 L3,18 C1.8954305,18 1,17.1045695 1,16 L1,8 C1,6.8954305 1.8954305,6 3,6 L13,6 L13,3 L11,3 L11,1 L17,1 L17,3 L15,3 L15,6 Z M15,8 L15,16 L21,16 L21,8 L15,8 Z M13,8 L3,8 L3,16 L13,16 L13,8 Z M5,10 L11,10 L11,14 L5,14 L5,10 Z" id="Color" fill="currentColor" fill-rule="nonzero"></path>
    </g>
</svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: string | number
  color?: string
  class?: string
  style?: string | Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  size: 24,
  color: 'currentColor'
})

const iconClass = computed(() => {
  const classes = ['moxo-icon', 'moxo-icon-file-rename']
  if (props.class) {
    classes.push(props.class)
  }
  return classes.join(' ')
})

const iconStyle = computed(() => {
  const styles: Record<string, any> = {}
  
  if (props.color) {
    styles.color = props.color
  }
  
  if (props.style) {
    if (typeof props.style === 'string') {
      return [styles, props.style]
    } else {
      Object.assign(styles, props.style)
    }
  }
  
  return styles
})
</script>

<style scoped>
.moxo-icon {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
}
</style>