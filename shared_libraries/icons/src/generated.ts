// 自动生成的图标组件索引文件
export { default as AccordionLeftCentered } from './components/AccordionLeftCentered.vue'
export { default as AccordionRightCentered } from './components/AccordionRightCentered.vue'

// 导出图标名称类型
export type MoxoIconName = 'accordion-left-centered' | 'accordion-right-centered'

// 导出图标名称列表
export const iconNames: MoxoIconName[] = [
  'accordion-left-centered',
  'accordion-right-centered'
]

// 导出图标组件映射
export const iconComponents = {
  'accordion-left-centered': () => import('./components/AccordionLeftCentered.vue'),
  'accordion-right-centered': () => import('./components/AccordionRightCentered.vue')
} as const
