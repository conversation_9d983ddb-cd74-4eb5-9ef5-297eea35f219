import type { Component } from 'vue'

// 图标组件基础属性
export interface IconProps {
  /**
   * 图标大小，可以是数字（px）或字符串（如 '24px', '1.5em'）
   */
  size?: string | number
  /**
   * 图标颜色
   */
  color?: string
  /**
   * CSS 类名
   */
  class?: string
  /**
   * 内联样式
   */
  style?: string | Record<string, any>
  /**
   * 图标旋转角度
   */
  rotate?: number
  /**
   * 是否水平翻转
   */
  flip?: 'horizontal' | 'vertical' | 'both'
}

// 图标组件类型
export type IconComponent = Component<IconProps>

// 图标组件映射类型
export type IconComponentMap = Record<string, () => Promise<{ default: IconComponent }>>

// 图标注册表类型
export interface IconRegistry {
  /**
   * 注册图标组件
   */
  register(name: string, component: IconComponent): void

  /**
   * 获取图标组件
   */
  get(name: string): IconComponent | undefined

  /**
   * 获取所有已注册的图标名称
   */
  getNames(): string[]

  /**
   * 检查图标是否已注册
   */
  has(name: string): boolean
}
