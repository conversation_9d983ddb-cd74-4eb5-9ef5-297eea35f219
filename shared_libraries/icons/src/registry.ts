import type { IconComponent, IconRegistry, MoxoIconName } from './types'

/**
 * 图标注册表实现
 */
class IconRegistryImpl implements IconRegistry {
  private icons = new Map<MoxoIconName, IconComponent>()

  register(name: MoxoIconName, component: IconComponent): void {
    this.icons.set(name, component)
  }

  get(name: MoxoIconName): IconComponent | undefined {
    return this.icons.get(name)
  }

  getNames(): MoxoIconName[] {
    return Array.from(this.icons.keys())
  }

  has(name: MoxoIconName): boolean {
    return this.icons.has(name)
  }

  clear(): void {
    this.icons.clear()
  }
}

// 创建全局图标注册表实例
export const iconRegistry = new IconRegistryImpl()

// 导出注册表类型
export type { IconRegistry } from './types'

// 便捷函数：批量注册图标
export function registerIcons(icons: Record<MoxoIconName, IconComponent>): void {
  Object.entries(icons).forEach(([name, component]) => {
    iconRegistry.register(name as MoxoIconName, component)
  })
}

// 便捷函数：动态导入并注册图标
export async function loadAndRegisterIcon(name: MoxoIconName): Promise<IconComponent | undefined> {
  if (iconRegistry.has(name)) {
    return iconRegistry.get(name)
  }

  try {
    const iconModule = await import(`~icons/moxo/${name}`)
    const component = iconModule.default
    iconRegistry.register(name, component)
    return component
  } catch (error) {
    console.warn(`Failed to load icon: ${name}`, error)
    return undefined
  }
}
