import type { IconComponent, IconRegistry } from './types'

/**
 * 图标注册表实现
 */
class IconRegistryImpl implements IconRegistry {
  private icons = new Map<string, IconComponent>()

  register(name: string, component: IconComponent): void {
    this.icons.set(name, component)
  }

  get(name: string): IconComponent | undefined {
    return this.icons.get(name)
  }

  getNames(): string[] {
    return Array.from(this.icons.keys())
  }

  has(name: string): boolean {
    return this.icons.has(name)
  }

  clear(): void {
    this.icons.clear()
  }
}

// 创建全局图标注册表实例
export const iconRegistry = new IconRegistryImpl()

// 导出注册表类型
export type { IconRegistry } from './types'

// 便捷函数：批量注册图标
export function registerIcons(icons: Record<string, IconComponent>): void {
  Object.entries(icons).forEach(([name, component]) => {
    iconRegistry.register(name, component)
  })
}

// 便捷函数：动态导入并注册图标
export async function loadAndRegisterIcon(name: string): Promise<IconComponent | undefined> {
  if (iconRegistry.has(name)) {
    return iconRegistry.get(name)
  }

  try {
    // 这里可以根据需要实现动态导入逻辑
    console.warn(`Dynamic loading not implemented for icon: ${name}`)
    return undefined
  } catch (error) {
    console.warn(`Failed to load icon: ${name}`, error)
    return undefined
  }
}
