// 导出所有 Moxo 自定义图标
export { default as MoxoAccordionLeftCentered } from '~icons/moxo/accordion-left-centered'
export { default as MoxoAccordionRightCentered } from '~icons/moxo/accordion-right-centered'

// 导出 Vue 组件
export { default as Icon } from './Icon.vue'

// 导出类型
export * from './types'

// 导出注册表
export * from './registry'

// 导出图标组件映射
export const iconComponents = {
  'accordion-left-centered': () => import('~icons/moxo/accordion-left-centered'),
  'accordion-right-centered': () => import('~icons/moxo/accordion-right-centered'),
} as const

// 导出图标列表
export const iconNames = [
  'accordion-left-centered',
  'accordion-right-centered',
] as const

// 默认导出
export default {
  MoxoAccordionLeftCentered: () => import('~icons/moxo/accordion-left-centered'),
  MoxoAccordionRightCentered: () => import('~icons/moxo/accordion-right-centered'),
}
