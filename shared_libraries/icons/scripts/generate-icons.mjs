import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 获取项目根目录
const projectRoot = path.resolve(__dirname, '..')
const svgDir = path.join(projectRoot, 'src/svg')
const outputDir = path.join(projectRoot, 'src/components')

// 确保输出目录存在
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true })
}

// 读取所有 SVG 文件
const svgFiles = fs.readdirSync(svgDir).filter(file => file.endsWith('.svg'))

// 生成图标组件
const iconComponents = []
const iconNames = []

svgFiles.forEach(file => {
  const iconName = path.basename(file, '.svg')
  const componentName = iconName
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('')
  
  // 读取 SVG 内容
  const svgContent = fs.readFileSync(path.join(svgDir, file), 'utf-8')
  
  // 处理 SVG 内容，添加动态属性
  const processedSvg = svgContent
    .replace(/fill="[^"]*"/g, 'fill="currentColor"')
    .replace(/width="[^"]*"/g, ':width="size"')
    .replace(/height="[^"]*"/g, ':height="size"')
    .replace(/<svg/, '<svg :class="iconClass" :style="iconStyle"')
  
  // 生成 Vue 组件
  const componentContent = `<template>
  ${processedSvg}
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: string | number
  color?: string
  class?: string
  style?: string | Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  size: 24,
  color: 'currentColor'
})

const iconClass = computed(() => {
  const classes = ['moxo-icon', 'moxo-icon-${iconName}']
  if (props.class) {
    classes.push(props.class)
  }
  return classes.join(' ')
})

const iconStyle = computed(() => {
  const styles: Record<string, any> = {}
  
  if (props.color) {
    styles.color = props.color
  }
  
  if (props.style) {
    if (typeof props.style === 'string') {
      return [styles, props.style]
    } else {
      Object.assign(styles, props.style)
    }
  }
  
  return styles
})
</script>

<style scoped>
.moxo-icon {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
}
</style>`

  // 写入组件文件
  const componentPath = path.join(outputDir, `${componentName}.vue`)
  fs.writeFileSync(componentPath, componentContent)
  
  iconComponents.push({
    name: componentName,
    iconName,
    file: `${componentName}.vue`
  })
  
  iconNames.push(iconName)
})

// 生成索引文件
const indexContent = `// 自动生成的图标组件索引文件
${iconComponents.map(({ name, file }) => 
  `export { default as ${name} } from './components/${file.replace('.vue', '.vue')}'`
).join('\n')}

// 导出图标名称类型
export type MoxoIconName = ${iconNames.map(name => `'${name}'`).join(' | ')}

// 导出图标名称列表
export const iconNames: MoxoIconName[] = [
  ${iconNames.map(name => `'${name}'`).join(',\n  ')}
]

// 导出图标组件映射
export const iconComponents = {
  ${iconComponents.map(({ iconName, name }) => 
    `'${iconName}': () => import('./components/${name}.vue')`
  ).join(',\n  ')}
} as const
`

fs.writeFileSync(path.join(projectRoot, 'src/generated.ts'), indexContent)

console.log(`✅ 成功生成 ${iconComponents.length} 个图标组件`)
console.log('📁 组件文件:', iconComponents.map(c => c.file).join(', '))
console.log('📝 索引文件: src/generated.ts')
