{"name": "@moxo/icons", "version": "1.0.0", "description": "Moxo Icons Library based on unplugin-icons", "main": "lib/cjs/index.cjs", "module": "lib/es/index.js", "types": "lib/types/index.d.ts", "files": ["lib", "src"], "exports": {".": {"types": "./lib/types/index.d.ts", "import": "./lib/es/index.js", "require": "./lib/cjs/index.cjs"}}, "scripts": {"build": "pnpm run build:lib && pnpm run build:types", "build:lib": "vite build", "build:types": "tsc --emitDeclarationOnly --declaration --outDir lib/types", "build:es": "vite build --mode es", "dev": "vite", "preview": "vite preview", "clean": "rm -rf lib dist", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"@moxo/tsconfig": "workspace:*", "@types/node": "^22.10.2", "@vitejs/plugin-vue": "^5.2.1", "typescript": "^5.8.3", "unplugin-icons": "^0.16.5", "vite": "^6.3.5", "vite-plugin-dts": "^4.3.0", "vue": "^3.5.16"}, "peerDependencies": {"vue": "^3.0.0"}, "private": true}