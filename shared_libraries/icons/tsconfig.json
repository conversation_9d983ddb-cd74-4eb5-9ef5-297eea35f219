{"extends": "@moxo/tsconfig/web", "compilerOptions": {"declaration": true, "declarationMap": true, "emitDeclarationOnly": false, "outDir": "lib", "rootDir": "src", "types": ["node", "vite/client"], "moduleResolution": "bundler", "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "preserve"}, "include": ["src/**/*"], "exclude": ["node_modules", "lib", "dist", "test"]}