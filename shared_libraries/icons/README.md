# @moxo/icons

基于 SVG 的 Vue 3 图标库，支持 TypeScript 类型定义。

## 特性

- 🎨 基于 SVG 的矢量图标
- 📦 支持 ES 和 CJS 模块格式
- 🔧 完整的 TypeScript 支持
- ⚡ 自动生成图标组件
- 🎯 Vue 3 组合式 API
- 📱 响应式设计支持

## 安装

```bash
pnpm install @moxo/icons
```

## 使用方法

### 1. 直接使用图标组件

```vue
<template>
  <div>
    <!-- 使用具体的图标组件 -->
    <AccordionLeftCentered :size="24" color="#007bff" />
    <AccordionRightCentered :size="32" color="red" />
  </div>
</template>

<script setup>
import { AccordionLeftCentered, AccordionRightCentered } from '@moxo/icons'
</script>
```

### 2. 使用通用 Icon 组件

```vue
<template>
  <div>
    <!-- 使用通用 Icon 组件 -->
    <Icon name="accordion-left-centered" :size="24" />
    <Icon name="accordion-right-centered" :size="32" color="#007bff" />
  </div>
</template>

<script setup>
import { Icon } from '@moxo/icons'
</script>
```

### 3. 动态导入

```vue
<script setup>
import { iconComponents } from '@moxo/icons'

// 动态导入图标组件
const loadIcon = async (iconName) => {
  const iconComponent = await iconComponents[iconName]()
  return iconComponent.default
}
</script>
```

## 图标属性

所有图标组件都支持以下属性：

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `size` | `string \| number` | `24` | 图标大小（px） |
| `color` | `string` | `'currentColor'` | 图标颜色 |
| `class` | `string` | - | CSS 类名 |
| `style` | `string \| object` | - | 内联样式 |

## 可用图标

当前可用的图标：

- `accordion-left-centered` - 左箭头图标
- `accordion-right-centered` - 右箭头图标

## 开发

### 添加新图标

1. 将 SVG 文件放入 `src/svg/` 目录
2. 运行生成脚本：

```bash
pnpm run generate
```

3. 构建项目：

```bash
pnpm run build
```

### 开发服务器

启动开发服务器查看图标预览：

```bash
pnpm run dev
```

访问 http://localhost:3000 查看图标展示页面。

### 构建

```bash
# 完整构建
pnpm run build

# 仅构建库文件
pnpm run build:lib

# 仅生成类型文件
pnpm run build:types
```

## 项目结构

```
src/
├── svg/                    # SVG 源文件
├── components/             # 自动生成的 Vue 组件
├── generated.ts           # 自动生成的导出文件
├── index.ts              # 主入口文件
├── types.ts              # 类型定义
├── registry.ts           # 图标注册表
└── Icon.vue              # 通用图标组件

lib/
├── es/                   # ES 模块输出
├── cjs/                  # CommonJS 模块输出
└── types/                # TypeScript 类型声明
```

## 许可证

MIT
