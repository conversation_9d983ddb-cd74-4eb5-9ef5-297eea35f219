import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import Icons from 'unplugin-icons/vite'
import { FileSystemIconLoader } from 'unplugin-icons/loaders'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue(),
    Icons({
      compiler: 'vue3',
      customCollections: {
        'moxo': FileSystemIconLoader('./src/svg', svg => svg.replace(/^<svg /, '<svg fill="currentColor" '))
      },
      autoInstall: true,
    }),
    dts({
      insertTypesEntry: true,
      outDir: 'lib/types',
      include: ['src/**/*'],
      exclude: ['**/*.test.*', '**/*.spec.*', 'test/**/*']
    })
  ],
  
  root: './test',
  
  server: {
    port: 3000,
    open: true,
  },
  
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'MoxoIcons',
      formats: ['es', 'cjs'],
    },
    rollupOptions: {
      external: ['vue'],
      output: [
        {
          format: 'es',
          dir: 'lib/es',
          entryFileNames: '[name].js',
          chunkFileNames: 'chunks/[name]-[hash].js',
          preserveModules: true,
          preserveModulesRoot: 'src',
        },
        {
          format: 'cjs',
          dir: 'lib/cjs',
          entryFileNames: '[name].cjs',
          chunkFileNames: 'chunks/[name]-[hash].cjs',
          preserveModules: true,
          preserveModulesRoot: 'src',
        },
      ],
    },
    outDir: 'lib',
    emptyOutDir: true,
  },
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
})
