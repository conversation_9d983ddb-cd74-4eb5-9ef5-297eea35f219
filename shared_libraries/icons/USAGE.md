# 使用示例

## 在 Vue 3 项目中使用

### 1. 安装依赖

```bash
# 在项目根目录
pnpm add @moxo/icons
```

### 2. 基本使用

```vue
<!-- App.vue -->
<template>
  <div class="app">
    <h1>图标展示</h1>
    
    <!-- 方式1: 直接使用具体图标组件 -->
    <div class="icon-section">
      <h2>直接使用图标组件</h2>
      <AccordionLeftCentered :size="24" />
      <AccordionRightCentered :size="32" color="#007bff" />
    </div>
    
    <!-- 方式2: 使用通用 Icon 组件 -->
    <div class="icon-section">
      <h2>使用通用 Icon 组件</h2>
      <Icon name="accordion-left-centered" :size="24" />
      <Icon name="accordion-right-centered" :size="32" color="red" />
    </div>
    
    <!-- 方式3: 动态使用 -->
    <div class="icon-section">
      <h2>动态图标</h2>
      <button @click="toggleIcon">切换图标</button>
      <Icon :name="currentIcon" :size="48" color="green" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  Icon, 
  AccordionLeftCentered, 
  AccordionRightCentered,
  type MoxoIconName 
} from '@moxo/icons'

const currentIcon = ref<MoxoIconName>('accordion-left-centered')

const toggleIcon = () => {
  currentIcon.value = currentIcon.value === 'accordion-left-centered' 
    ? 'accordion-right-centered' 
    : 'accordion-left-centered'
}
</script>

<style scoped>
.app {
  padding: 20px;
}

.icon-section {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.icon-section h2 {
  margin-top: 0;
}
</style>
```

### 3. 在组合式函数中使用

```typescript
// composables/useIcons.ts
import { ref, computed } from 'vue'
import { iconNames, iconComponents, type MoxoIconName } from '@moxo/icons'

export function useIcons() {
  const selectedIcon = ref<MoxoIconName>('accordion-left-centered')
  
  // 获取所有可用图标
  const availableIcons = computed(() => iconNames)
  
  // 动态加载图标组件
  const loadIcon = async (iconName: MoxoIconName) => {
    try {
      const iconModule = await iconComponents[iconName]()
      return iconModule.default
    } catch (error) {
      console.error(`Failed to load icon: ${iconName}`, error)
      return null
    }
  }
  
  return {
    selectedIcon,
    availableIcons,
    loadIcon
  }
}
```

### 4. 在 Vite 项目中配置类型

```typescript
// vite-env.d.ts
/// <reference types="vite/client" />

declare module '@moxo/icons' {
  export * from '@moxo/icons/lib/types'
}
```

### 5. 样式自定义

```vue
<template>
  <div class="custom-icons">
    <!-- 自定义样式的图标 -->
    <AccordionLeftCentered 
      :size="40" 
      color="#007bff"
      class="rotating-icon"
    />
    
    <!-- 使用 CSS 变量 -->
    <AccordionRightCentered 
      :size="40"
      style="color: var(--primary-color)"
    />
  </div>
</template>

<style scoped>
.custom-icons {
  --primary-color: #28a745;
}

.rotating-icon {
  transition: transform 0.3s ease;
}

.rotating-icon:hover {
  transform: rotate(180deg);
}
</style>
```

## 在其他框架中使用

### React 项目中使用（需要适配器）

```typescript
// 注意：这个图标库是为 Vue 3 设计的
// 如果要在 React 中使用，需要创建适配器或使用 SVG 源文件
```

### 直接使用 SVG

如果需要在非 Vue 项目中使用，可以直接使用 SVG 文件：

```html
<!-- 直接复制 SVG 内容 -->
<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
  <path d="M4.51472 7.94971C4.51472 7.69378..."/>
</svg>
```

## 最佳实践

1. **按需导入**: 只导入需要的图标组件以减少包大小
2. **类型安全**: 使用 TypeScript 获得更好的开发体验
3. **样式一致性**: 使用 CSS 变量统一管理图标颜色
4. **响应式设计**: 根据屏幕尺寸调整图标大小
5. **无障碍访问**: 为图标添加适当的 aria-label 属性
