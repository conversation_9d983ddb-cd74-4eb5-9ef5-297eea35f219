import { defineComponent as c, computed as s, defineAsyncComponent as a, createBlock as f, openBlock as p, resolveDynamicComponent as m, mergeProps as u } from "vue";
import { iconComponents as y } from "./generated.js";
const z = /* @__PURE__ */ c({
  __name: "Icon",
  props: {
    name: {},
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {},
    rotate: {},
    flip: {}
  },
  setup(n) {
    const e = n, r = s(() => a(() => y[e.name]())), l = s(() => {
      const o = ["moxo-icon"];
      return e.class && o.push(e.class), o.join(" ");
    }), i = s(() => {
      const o = {};
      if (e.size) {
        const t = typeof e.size == "number" ? `${e.size}px` : e.size;
        o.width = t, o.height = t;
      }
      if (e.color && (o.color = e.color), e.rotate && (o.transform = `rotate(${e.rotate}deg)`), e.flip) {
        const t = [];
        (e.flip === "horizontal" || e.flip === "both") && t.push("scaleX(-1)"), (e.flip === "vertical" || e.flip === "both") && t.push("scaleY(-1)"), t.length > 0 && (o.transform = t.join(" "));
      }
      if (e.style) {
        if (typeof e.style == "string")
          return [o, e.style];
        Object.assign(o, e.style);
      }
      return o;
    });
    return (o, t) => (p(), f(m(r.value), u({
      class: l.value,
      style: i.value
    }, o.$attrs), null, 16, ["class", "style"]));
  }
});
export {
  z as default
};
