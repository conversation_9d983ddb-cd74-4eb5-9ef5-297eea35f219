import { defineComponent as c, computed as t, defineAsyncComponent as a, createBlock as f, openBlock as p, resolveDynamicComponent as m, mergeProps as u } from "vue";
const h = /* @__PURE__ */ c({
  __name: "Icon",
  props: {
    name: {},
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {},
    rotate: {},
    flip: {}
  },
  setup(n) {
    const e = n, l = t(() => a(() => import(`~icons/moxo/${e.name}`))), r = t(() => {
      const o = ["moxo-icon"];
      return e.class && o.push(e.class), o.join(" ");
    }), i = t(() => {
      const o = {};
      if (e.size) {
        const s = typeof e.size == "number" ? `${e.size}px` : e.size;
        o.width = s, o.height = s;
      }
      if (e.color && (o.color = e.color), e.rotate && (o.transform = `rotate(${e.rotate}deg)`), e.flip) {
        const s = [];
        (e.flip === "horizontal" || e.flip === "both") && s.push("scaleX(-1)"), (e.flip === "vertical" || e.flip === "both") && s.push("scaleY(-1)"), s.length > 0 && (o.transform = s.join(" "));
      }
      if (e.style) {
        if (typeof e.style == "string")
          return [o, e.style];
        Object.assign(o, e.style);
      }
      return o;
    });
    return (o, s) => (p(), f(m(l.value), u({
      class: r.value,
      style: i.value
    }, o.$attrs), null, 16, ["class", "style"]));
  }
});
export {
  h as default
};
