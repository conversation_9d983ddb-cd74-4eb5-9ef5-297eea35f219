import { defineComponent as c, computed as l, createElementBlock as a, openBlock as p, normalizeStyle as u, normalizeClass as L, createElementVNode as t } from "vue";
const d = ["width", "height"], m = /* @__PURE__ */ c({
  __name: "Reopen",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(n) {
    const o = n, r = l(() => {
      const e = ["moxo-icon", "moxo-icon-reopen"];
      return o.class && e.push(o.class), e.join(" ");
    }), i = l(() => {
      const e = {};
      if (o.color && (e.color = o.color), o.style) {
        if (typeof o.style == "string")
          return [e, o.style];
        Object.assign(e, o.style);
      }
      return e;
    });
    return (e, s) => (p(), a("svg", {
      class: L(r.value),
      style: u(i.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 16 16",
      version: "1.1",
      xmlns: "http://www.w3.org/2000/svg",
      "xmlns:xlink": "http://www.w3.org/1999/xlink"
    }, s[0] || (s[0] = [
      t("title", null, "⠁ Icon/⠃ XS/ Reopen", -1),
      t("g", {
        id: "⠁-Icon/⠃-XS/-Reopen",
        stroke: "none",
        "stroke-:width": "size",
        fill: "currentColor",
        "fill-rule": "evenodd",
        "fill-opacity": "0.6"
      }, [
        t("path", {
          d: "M7,9 L7,11 L5.353,11 C6.07,11.632 7.006,12 8,12 C9.859,12 11.411,10.721 11.858,9 L11.858,9 L13.91,9 C13.431,11.833 10.967,14 8,14 C6.493,14 5.079,13.435 4,12.469 L4,12.469 L4,14 L2,14 L2,9 L7,9 Z M14.0001,2 L14.0001,7 L9.0001,7 L9.0001,5 L10.6461,5 C9.9291,4.368 8.9941,4 8.0001,4 C6.1411,4 4.5891,5.279 4.1421,7 L4.1421,7 L2.0901,7 C2.5691,4.167 5.0331,2 8.0001,2 C9.5061,2 10.9201,2.566 12.0001,3.531 L12.0001,3.531 L12.0001,2 L14.0001,2 Z",
          id: "Icon",
          fill: "currentColor"
        })
      ], -1)
    ]), 14, d));
  }
});
export {
  m as default
};
