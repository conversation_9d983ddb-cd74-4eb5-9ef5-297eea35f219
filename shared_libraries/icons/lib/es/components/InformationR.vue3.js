import { defineComponent as c, computed as s, createElementBlock as a, openBlock as u, normalizeStyle as C, normalizeClass as f, createElementVNode as l } from "vue";
const p = ["width", "height"], m = /* @__PURE__ */ c({
  __name: "InformationR",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(r) {
    const o = r, n = s(() => {
      const e = ["moxo-icon", "moxo-icon-information-r"];
      return o.class && e.push(o.class), e.join(" ");
    }), i = s(() => {
      const e = {};
      if (o.color && (e.color = o.color), o.style) {
        if (typeof o.style == "string")
          return [e, o.style];
        Object.assign(e, o.style);
      }
      return e;
    });
    return (e, t) => (u(), a("svg", {
      class: f(n.value),
      style: C(i.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, t[0] || (t[0] = [
      l("path", {
        d: "M12.75 17H11.25V10.5H12.75V17Z",
        fill: "currentColor"
      }, null, -1),
      l("path", {
        d: "M12 7C12.4142 7 12.75 7.33579 12.75 7.75C12.75 8.16421 12.4142 8.5 12 8.5C11.5858 8.5 11.25 8.16421 11.25 7.75C11.25 7.33579 11.5858 7 12 7Z",
        fill: "currentColor"
      }, null, -1),
      l("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2ZM12 3.5C7.30558 3.5 3.5 7.30558 3.5 12C3.5 16.6944 7.30558 20.5 12 20.5C16.6944 20.5 20.5 16.6944 20.5 12C20.5 7.30558 16.6944 3.5 12 3.5Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, p));
  }
});
export {
  m as default
};
