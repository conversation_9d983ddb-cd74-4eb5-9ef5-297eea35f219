import { defineComponent as i, computed as s, createElementBlock as c, openBlock as C, normalizeStyle as a, normalizeClass as u, createElementVNode as p } from "vue";
const d = ["width", "height"], m = /* @__PURE__ */ i({
  __name: "SequentialR",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(t) {
    const o = t, n = s(() => {
      const e = ["moxo-icon", "moxo-icon-sequential-r"];
      return o.class && e.push(o.class), e.join(" ");
    }), r = s(() => {
      const e = {};
      if (o.color && (e.color = o.color), o.style) {
        if (typeof o.style == "string")
          return [e, o.style];
        Object.assign(e, o.style);
      }
      return e;
    });
    return (e, l) => (C(), c("svg", {
      class: u(n.value),
      style: a(r.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, l[0] || (l[0] = [
      p("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M7 2.25C8.2585 2.25 9.31684 3.09614 9.64355 4.25H14.5C16.8472 4.25 18.75 6.15279 18.75 8.5C18.75 10.8472 16.8472 12.75 14.5 12.75H9.5C7.98122 12.75 6.75 13.9812 6.75 15.5C6.75 17.0188 7.98122 18.25 9.5 18.25H17.1895L15.4697 16.5303L16.5303 15.4697L19.707 18.6464C19.9023 18.8417 19.9023 19.1583 19.707 19.3536L16.5303 22.5303L15.4697 21.4697L17.1895 19.75H9.5C7.15279 19.75 5.25 17.8472 5.25 15.5C5.25 13.1528 7.15279 11.25 9.5 11.25H14.5C16.0188 11.25 17.25 10.0188 17.25 8.5C17.25 6.98122 16.0188 5.75 14.5 5.75H9.64355C9.31684 6.90386 8.2585 7.75 7 7.75C5.48122 7.75 4.25 6.51878 4.25 5C4.25 3.48122 5.48122 2.25 7 2.25ZM7 3.75C6.30964 3.75 5.75 4.30964 5.75 5C5.75 5.69036 6.30964 6.25 7 6.25C7.69036 6.25 8.25 5.69036 8.25 5C8.25 4.30964 7.69036 3.75 7 3.75Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, d));
  }
});
export {
  m as default
};
