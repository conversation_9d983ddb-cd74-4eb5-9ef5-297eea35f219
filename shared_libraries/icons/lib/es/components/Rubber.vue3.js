import { defineComponent as c, computed as o, createElementBlock as a, openBlock as u, normalizeStyle as d, normalizeClass as f, createElementVNode as t } from "vue";
const L = ["width", "height"], p = /* @__PURE__ */ c({
  __name: "Rubber",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(r) {
    const l = r, n = o(() => {
      const e = ["moxo-icon", "moxo-icon-rubber"];
      return l.class && e.push(l.class), e.join(" ");
    }), i = o(() => {
      const e = {};
      if (l.color && (e.color = l.color), l.style) {
        if (typeof l.style == "string")
          return [e, l.style];
        Object.assign(e, l.style);
      }
      return e;
    });
    return (e, s) => (u(), a("svg", {
      class: f(n.value),
      style: d(i.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      version: "1.1",
      xmlns: "http://www.w3.org/2000/svg",
      "xmlns:xlink": "http://www.w3.org/1999/xlink"
    }, s[0] || (s[0] = [
      t("title", null, "Icon/Default/Rubber", -1),
      t("desc", null, "Created with Sketch.", -1),
      t("defs", null, [
        t("path", {
          d: "M8,14 L8,16 L16,16 L16,14 L8,14 Z M8,12 L16,12 L16,4 L8,4 L8,12 Z M17,17.999 L17,22 L7,22 L7,17.999 L6,17.999 L6,2 L18,2 L18,17.999 L17,17.999 Z M9,20 L15,20 L15,17.999 L9,17.999 L9,20 Z",
          id: "path-1"
        })
      ], -1),
      t("g", {
        id: "Default",
        stroke: "none",
        "stroke-:width": "size",
        fill: "currentColor",
        "fill-rule": "evenodd"
      }, [
        t("g", { id: "Icon/Default/Rubber" }, [
          t("mask", {
            id: "mask-2",
            fill: "currentColor"
          }, [
            t("use", { "xlink:href": "#path-1" })
          ]),
          t("use", {
            id: "Mask",
            fill: "currentColor",
            "xlink:href": "#path-1"
          })
        ])
      ], -1)
    ]), 14, L));
  }
});
export {
  p as default
};
