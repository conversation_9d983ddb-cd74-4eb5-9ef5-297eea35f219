import { defineComponent as c, computed as s, createElementBlock as C, openBlock as u, normalizeStyle as a, normalizeClass as d, createElementVNode as o } from "vue";
const p = ["width", "height"], m = /* @__PURE__ */ c({
  __name: "AdminUserR",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(r) {
    const l = r, n = s(() => {
      const e = ["moxo-icon", "moxo-icon-admin-user-r"];
      return l.class && e.push(l.class), e.join(" ");
    }), i = s(() => {
      const e = {};
      if (l.color && (e.color = l.color), l.style) {
        if (typeof l.style == "string")
          return [e, l.style];
        Object.assign(e, l.style);
      }
      return e;
    });
    return (e, t) => (u(), C("svg", {
      class: d(n.value),
      style: a(i.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, t[0] || (t[0] = [
      o("path", {
        d: "M12 8.5C13.3807 8.50003 14.5 9.61931 14.5 11C14.5 12.3807 13.3807 13.5 12 13.5C10.6193 13.5 9.5 12.3807 9.5 11C9.5 9.61929 10.6193 8.5 12 8.5Z",
        fill: "currentColor"
      }, null, -1),
      o("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M10.8496 4.09961C10.8496 4.54144 11.2086 4.90039 11.6504 4.90039H12.3496C12.7914 4.90039 13.1504 4.54144 13.1504 4.09961V4H19.5C20.0523 4 20.5 4.44772 20.5 5V20C20.5 20.5523 20.0523 21 19.5 21H4.5C3.94772 21 3.5 20.5523 3.5 20V5C3.5 4.44772 3.94772 4 4.5 4H10.8496V4.09961ZM5 19.5H7.02441C7.27526 16.9733 9.40732 15 12 15C14.5927 15 16.7247 16.9733 16.9756 19.5H19V5.5H15.2363C15.0812 5.5 14.9279 5.53611 14.7891 5.60547L14.2109 5.89453C14.0721 5.96389 13.9188 6 13.7637 6H10.2363C10.0812 6 9.92786 5.96389 9.78906 5.89453L9.21094 5.60547C9.07214 5.53611 8.91884 5.5 8.76367 5.5H5V19.5Z",
        fill: "currentColor"
      }, null, -1),
      o("path", {
        d: "M12.75 4.09961C12.75 4.32052 12.5705 4.5 12.3496 4.5H11.6504C11.4295 4.5 11.25 4.32052 11.25 4.09961V2H12.75V4.09961Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, p));
  }
});
export {
  m as default
};
