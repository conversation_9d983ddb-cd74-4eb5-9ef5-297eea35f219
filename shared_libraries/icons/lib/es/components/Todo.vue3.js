import { defineComponent as c, computed as s, createElementBlock as a, openBlock as d, normalizeStyle as u, normalizeClass as f, createElementVNode as o } from "vue";
const h = ["width", "height"], m = /* @__PURE__ */ c({
  __name: "Todo",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(n) {
    const t = n, r = s(() => {
      const e = ["moxo-icon", "moxo-icon-todo"];
      return t.class && e.push(t.class), e.join(" ");
    }), i = s(() => {
      const e = {};
      if (t.color && (e.color = t.color), t.style) {
        if (typeof t.style == "string")
          return [e, t.style];
        Object.assign(e, t.style);
      }
      return e;
    });
    return (e, l) => (d(), a("svg", {
      class: f(r.value),
      style: u(i.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      version: "1.1",
      xmlns: "http://www.w3.org/2000/svg",
      "xmlns:xlink": "http://www.w3.org/1999/xlink"
    }, l[0] || (l[0] = [
      o("title", null, "Icon/Default/Todo", -1),
      o("desc", null, "Created with Sketch.", -1),
      o("defs", null, [
        o("path", {
          d: "M3,21 L3,3 L21,3 L21,21 L3,21 Z M5,19 L19,19 L19,5 L5,5 L5,19 Z M11.444,15.748 L7.908,12.212 L9.322,10.798 L11.444,12.92 L15.687,8.677 L17.101,10.091 L11.445,15.748 L11.444,15.747 L11.444,15.748 Z",
          id: "path-1"
        })
      ], -1),
      o("g", {
        id: "Default",
        stroke: "none",
        "stroke-:width": "size",
        fill: "currentColor",
        "fill-rule": "evenodd"
      }, [
        o("g", { id: "Icon/Default/Todo" }, [
          o("mask", {
            id: "mask-2",
            fill: "currentColor"
          }, [
            o("use", { "xlink:href": "#path-1" })
          ]),
          o("use", {
            id: "Combined-Shape",
            fill: "currentColor",
            "xlink:href": "#path-1"
          })
        ])
      ], -1)
    ]), 14, h));
  }
});
export {
  m as default
};
