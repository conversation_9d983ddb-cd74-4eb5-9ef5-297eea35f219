import { defineComponent as c, computed as s, createElementBlock as i, openBlock as a, normalizeStyle as C, normalizeClass as u, createElementVNode as m } from "vue";
const p = ["width", "height"], L = /* @__PURE__ */ c({
  __name: "AttachmentB",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(l) {
    const t = l, n = s(() => {
      const e = ["moxo-icon", "moxo-icon-attachment-b"];
      return t.class && e.push(t.class), e.join(" ");
    }), r = s(() => {
      const e = {};
      if (t.color && (e.color = t.color), t.style) {
        if (typeof t.style == "string")
          return [e, t.style];
        Object.assign(e, t.style);
      }
      return e;
    });
    return (e, o) => (a(), i("svg", {
      class: u(n.value),
      style: C(r.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, o[0] || (o[0] = [
      m("path", {
        d: "M12.8836 4.77734L6.16683 11.4951C4.5071 13.1548 4.5071 15.8452 6.16683 17.5049C7.82657 19.1645 10.5169 19.1646 12.1766 17.5049L19.0711 10.6113C20.0474 9.63502 20.0474 8.05151 19.0711 7.0752C18.0949 6.09905 16.5123 6.09921 15.536 7.0752L8.86605 13.7441C8.56529 14.0449 8.5747 14.536 8.88656 14.8252L9.00472 14.915C9.29441 15.0926 9.67732 15.0545 9.9266 14.8057L15.0995 9.63281L16.5145 11.0469L11.3416 16.2197C10.3619 17.1995 8.81937 17.2891 7.73715 16.4688L7.52816 16.293C6.38366 15.2329 6.34888 13.4332 7.45199 12.3301L14.1209 5.66113C15.8783 3.90379 18.7278 3.90384 20.4852 5.66113C22.2426 7.41849 22.2426 10.268 20.4852 12.0254L13.5907 18.9189C11.1499 21.3597 7.19257 21.3597 4.75179 18.9189C2.31158 16.4783 2.31158 12.5217 4.75179 10.0811L11.4696 3.36328L12.8836 4.77734Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, p));
  }
});
export {
  L as default
};
