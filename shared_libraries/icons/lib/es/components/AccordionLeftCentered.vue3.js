import { defineComponent as c, computed as l, createElementBlock as a, openBlock as d, normalizeStyle as u, normalizeClass as h, createElementVNode as o } from "vue";
const p = ["width", "height"], f = { id: "clip0_2069_1658" }, C = ["width", "height"], g = /* @__PURE__ */ c({
  __name: "AccordionLeftCentered",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(i) {
    const t = i, r = l(() => {
      const e = ["moxo-icon", "moxo-icon-accordion-left-centered"];
      return t.class && e.push(t.class), e.join(" ");
    }), n = l(() => {
      const e = {};
      if (t.color && (e.color = t.color), t.style) {
        if (typeof t.style == "string")
          return [e, t.style];
        Object.assign(e, t.style);
      }
      return e;
    });
    return (e, s) => (d(), a("svg", {
      class: h(r.value),
      style: u(n.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 16 16",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, [
      s[0] || (s[0] = o("g", { "clip-path": "url(#clip0_2069_1658)" }, [
        o("path", {
          d: "M4.51472 7.94971C4.51472 7.69378 4.61235 7.43786 4.80761 7.2426L8.34315 3.70707C8.73367 3.31654 9.36684 3.31654 9.75736 3.70707C10.1479 4.09759 10.1479 4.73076 9.75736 5.12128L6.92893 7.94971L9.75736 10.7781C10.1479 11.1687 10.1479 11.8018 9.75736 12.1923C9.36684 12.5829 8.73367 12.5829 8.34315 12.1923L4.80761 8.65681C4.61235 8.46155 4.51472 8.20563 4.51472 7.94971Z",
          fill: "currentColor"
        })
      ], -1)),
      o("defs", null, [
        o("clipPath", f, [
          o("rect", {
            width: e.size,
            height: e.size,
            fill: "currentColor"
          }, null, 8, C)
        ])
      ])
    ], 14, p));
  }
});
export {
  g as default
};
