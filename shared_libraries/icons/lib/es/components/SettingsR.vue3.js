import { defineComponent as i, computed as C, createElementBlock as c, openBlock as u, normalizeStyle as a, normalizeClass as d, createElementVNode as t } from "vue";
const p = ["width", "height"], m = /* @__PURE__ */ i({
  __name: "SettingsR",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(s) {
    const l = s, n = C(() => {
      const e = ["moxo-icon", "moxo-icon-settings-r"];
      return l.class && e.push(l.class), e.join(" ");
    }), r = C(() => {
      const e = {};
      if (l.color && (e.color = l.color), l.style) {
        if (typeof l.style == "string")
          return [e, l.style];
        Object.assign(e, l.style);
      }
      return e;
    });
    return (e, o) => (u(), c("svg", {
      class: d(n.value),
      style: a(r.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, o[0] || (o[0] = [
      t("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M12 8.2002C14.0987 8.2002 15.7998 9.90132 15.7998 12C15.7998 14.0987 14.0987 15.7998 12 15.7998C9.90132 15.7998 8.2002 14.0987 8.2002 12C8.2002 9.90132 9.90132 8.2002 12 8.2002ZM12 9.79981C10.785 9.79981 9.79981 10.785 9.79981 12C9.79981 13.215 10.785 14.2002 12 14.2002C13.215 14.2002 14.2002 13.215 14.2002 12C14.2002 10.785 13.215 9.79981 12 9.79981Z",
        fill: "currentColor"
      }, null, -1),
      t("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M14.6035 2.77637L14.8877 2.8584C16.3003 3.28886 17.5759 4.02664 18.6328 4.99219C18.8805 5.21852 18.9476 5.58208 18.7969 5.88184C18.1884 7.09475 19.1553 8.63989 20.5771 8.62012C20.9071 8.61548 21.2014 8.82723 21.3018 9.14161C21.7974 10.692 21.8832 12.366 21.5518 13.958C21.4734 14.3325 21.1259 14.5885 20.7451 14.5518C19.9554 14.4756 19.2533 14.9172 18.8828 15.5898C18.5167 16.2548 18.5246 17.0495 19.0537 17.666C19.3071 17.9613 19.2929 18.4018 19.0205 18.6797C17.8691 19.8551 16.4151 20.7421 14.7822 21.2197C14.3932 21.3334 13.9841 21.1177 13.8584 20.7324C13.2999 19.0159 10.6991 19.0159 10.1406 20.7324C10.0149 21.1177 9.60578 21.3334 9.2168 21.2197C7.58392 20.742 6.12991 19.8552 4.97852 18.6797C4.70616 18.4018 4.69195 17.9613 4.94531 17.666L5.03809 17.5488C5.93181 16.3147 4.90494 14.3927 3.25488 14.5518C2.8741 14.5884 2.52604 14.3326 2.44824 13.958C2.11686 12.366 2.20164 10.692 2.69727 9.14161C2.79761 8.82723 3.0919 8.61548 3.42188 8.62012L3.55469 8.61719C4.9034 8.54328 5.79169 7.05682 5.20215 5.88184C5.05193 5.58199 5.1196 5.21842 5.36719 4.99219C6.49454 3.96226 7.87052 3.19146 9.39648 2.77637C9.73698 2.68356 10.0964 2.84095 10.2588 3.1543C10.9642 4.51292 13.0347 4.51278 13.7402 3.1543C13.9032 2.8409 14.2627 2.68347 14.6035 2.77637ZM14.7236 4.38184C13.3703 6.10169 10.6286 6.10191 9.27539 4.38184C8.3516 4.69879 7.50252 5.17254 6.76172 5.77051C7.32783 7.70821 5.97096 9.75316 3.99023 10.0742C3.74555 11.0479 3.68731 12.0683 3.81738 13.0645C6.15229 13.3217 7.61688 15.9766 6.49219 18.0479C7.22984 18.6973 8.0875 19.2177 9.02832 19.5742C10.316 17.4028 13.6815 17.4033 14.9697 19.5742C15.9116 19.2175 16.7695 18.6961 17.5078 18.0459C16.9436 16.996 17.0534 15.8015 17.5684 14.8662C18.0792 13.9386 19.0223 13.2015 20.1807 13.0664C20.3111 12.0696 20.2537 11.0487 20.0088 10.0742C18.0286 9.75284 16.6706 7.70784 17.2363 5.77051C16.4956 5.17279 15.6472 4.69864 14.7236 4.38184Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, p));
  }
});
export {
  m as default
};
