import { defineComponent as n, computed as l, createElementBlock as c, openBlock as i, normalizeStyle as p, normalizeClass as d, createStaticVNode as u } from "vue";
const f = ["width", "height"], C = /* @__PURE__ */ n({
  __name: "WorkspaceR",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(r) {
    const t = r, s = l(() => {
      const e = ["moxo-icon", "moxo-icon-workspace-r"];
      return t.class && e.push(t.class), e.join(" ");
    }), a = l(() => {
      const e = {};
      if (t.color && (e.color = t.color), t.style) {
        if (typeof t.style == "string")
          return [e, t.style];
        Object.assign(e, t.style);
      }
      return e;
    });
    return (e, o) => (i(), c("svg", {
      class: d(s.value),
      style: p(a.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, o[0] || (o[0] = [
      u('<path d="M11.5303 13.5303L8.35355 16.707C8.15829 16.9023 7.84171 16.9023 7.64645 16.707L5.96973 15.0303L7.03027 13.9697L8 14.9395L10.4697 12.4697L11.5303 13.5303Z" fill="currentColor" data-v-99171e35></path><path d="M18 15.75H13V14.25H18V15.75Z" fill="currentColor" data-v-99171e35></path><path d="M11.5303 8.03027L8.35355 11.207C8.15829 11.4023 7.84171 11.4023 7.64645 11.207L5.96973 9.53027L7.03027 8.46973L8 9.43945L10.4697 6.96973L11.5303 8.03027Z" fill="currentColor" data-v-99171e35></path><path d="M18 10.25H13V8.75H18V10.25Z" fill="currentColor" data-v-99171e35></path><path fill-rule="evenodd" clip-rule="evenodd" d="M21 20C21 20.5523 20.5523 21 20 21H4C3.44772 21 3 20.5523 3 20V4C3 3.44772 3.44772 3 4 3H20C20.5523 3 21 3.44772 21 4V20ZM4.5 19.5H19.5V4.5H4.5V19.5Z" fill="currentColor" data-v-99171e35></path>', 5)
    ]), 14, f));
  }
});
export {
  C as default
};
