import { defineComponent as c, computed as t, createElementBlock as i, openBlock as a, normalizeStyle as u, normalizeClass as d, createElementVNode as s } from "vue";
const p = ["width", "height"], m = /* @__PURE__ */ c({
  __name: "SavedToCloudR",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(r) {
    const o = r, C = t(() => {
      const e = ["moxo-icon", "moxo-icon-saved-to-cloud-r"];
      return o.class && e.push(o.class), e.join(" ");
    }), n = t(() => {
      const e = {};
      if (o.color && (e.color = o.color), o.style) {
        if (typeof o.style == "string")
          return [e, o.style];
        Object.assign(e, o.style);
      }
      return e;
    });
    return (e, l) => (a(), i("svg", {
      class: d(C.value),
      style: u(n.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, l[0] || (l[0] = [
      s("path", {
        d: "M15.4092 10.9248L11.3545 14.9804C11.1593 15.1757 10.8427 15.1757 10.6474 14.9804L8.53418 12.8672L9.59473 11.8066L11 13.2119L14.3486 9.86426L15.4092 10.9248Z",
        fill: "currentColor"
      }, null, -1),
      s("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M12 5.25C13.7389 5.25 15.0445 5.78466 15.9814 6.60547C16.7598 7.28744 17.2453 8.13542 17.5312 8.9502C17.6099 8.96917 17.6964 8.99104 17.7891 9.01758C18.2153 9.13965 18.7903 9.35157 19.3711 9.71289C20.5599 10.4526 21.75 11.8135 21.75 14.1602C21.75 15.7643 21.2421 16.9482 20.373 17.7207C19.5211 18.4779 18.4225 18.7499 17.4004 18.75H6.59961C5.74896 18.7499 4.66941 18.5488 3.78418 17.8779C2.86318 17.1799 2.2501 16.0464 2.25 14.4004C2.25 12.4378 3.19081 11.196 4.27148 10.4756C5.02948 9.97031 5.85367 9.72036 6.46875 9.62207C6.70671 8.83285 7.07977 7.86861 7.76953 7.04785C8.64779 6.00294 9.98563 5.25 12 5.25ZM12 6.75C10.4144 6.75 9.50221 7.3176 8.91797 8.0127C8.30122 8.74649 7.99652 9.68454 7.77441 10.5137C7.68688 10.8419 7.38952 11.0703 7.0498 11.0703C6.72962 11.0704 5.86585 11.2155 5.10352 11.7236C4.38419 12.2032 3.75 13.003 3.75 14.4004C3.75009 15.6338 4.18661 16.3008 4.69043 16.6826C5.22994 17.0915 5.95046 17.2499 6.59961 17.25H17.4004C18.1779 17.2499 18.8792 17.0421 19.377 16.5996C19.8579 16.1721 20.25 15.4358 20.25 14.1602C20.25 12.4269 19.4152 11.5066 18.5791 10.9863C18.1474 10.7177 17.7091 10.5554 17.376 10.46C17.2108 10.4127 17.0749 10.3827 16.9834 10.3652C16.938 10.3566 16.9038 10.3509 16.8828 10.3477C16.8723 10.3461 16.8649 10.3452 16.8613 10.3447C16.557 10.3088 16.305 10.0899 16.2256 9.79395C16.0289 9.05986 15.6429 8.30368 14.9932 7.73438C14.3552 7.17538 13.4109 6.75 12 6.75Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, p));
  }
});
export {
  m as default
};
