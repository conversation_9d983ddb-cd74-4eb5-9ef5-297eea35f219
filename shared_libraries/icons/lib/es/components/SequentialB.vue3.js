import { defineComponent as i, computed as s, createElementBlock as c, openBlock as C, normalizeStyle as a, normalizeClass as u, createElementVNode as p } from "vue";
const d = ["width", "height"], m = /* @__PURE__ */ i({
  __name: "SequentialB",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(t) {
    const o = t, n = s(() => {
      const e = ["moxo-icon", "moxo-icon-sequential-b"];
      return o.class && e.push(o.class), e.join(" ");
    }), r = s(() => {
      const e = {};
      if (o.color && (e.color = o.color), o.style) {
        if (typeof o.style == "string")
          return [e, o.style];
        Object.assign(e, o.style);
      }
      return e;
    });
    return (e, l) => (C(), c("svg", {
      class: u(n.value),
      style: a(r.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, l[0] || (l[0] = [
      p("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M7 2C8.30585 2 9.41406 2.83532 9.82617 4H14.5C16.9853 4 19 6.01472 19 8.5C19 10.9853 16.9853 13 14.5 13H9.5C8.11929 13 7 14.1193 7 15.5C7 16.8807 8.11929 18 9.5 18H16.5859L15.293 16.707L16.707 15.293L20.0605 18.6464C20.2558 18.8417 20.2558 19.1583 20.0605 19.3536L16.707 22.707L15.293 21.293L16.5859 20H9.5C7.01472 20 5 17.9853 5 15.5C5 13.0147 7.01472 11 9.5 11H14.5C15.8807 11 17 9.88071 17 8.5C17 7.11929 15.8807 6 14.5 6H9.82617C9.41406 7.16468 8.30585 8 7 8C5.34315 8 4 6.65685 4 5C4 3.34315 5.34315 2 7 2ZM7 4C6.44772 4 6 4.44772 6 5C6 5.55228 6.44772 6 7 6C7.55228 6 8 5.55228 8 5C8 4.44772 7.55228 4 7 4Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, d));
  }
});
export {
  m as default
};
