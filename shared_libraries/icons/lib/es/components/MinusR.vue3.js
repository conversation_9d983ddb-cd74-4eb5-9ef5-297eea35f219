import { defineComponent as i, computed as t, createElementBlock as c, openBlock as a, normalizeStyle as u, normalizeClass as m, createElementVNode as p } from "vue";
const f = ["width", "height"], y = /* @__PURE__ */ i({
  __name: "MinusR",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(l) {
    const s = l, n = t(() => {
      const e = ["moxo-icon", "moxo-icon-minus-r"];
      return s.class && e.push(s.class), e.join(" ");
    }), r = t(() => {
      const e = {};
      if (s.color && (e.color = s.color), s.style) {
        if (typeof s.style == "string")
          return [e, s.style];
        Object.assign(e, s.style);
      }
      return e;
    });
    return (e, o) => (a(), c("svg", {
      class: m(n.value),
      style: u(r.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, o[0] || (o[0] = [
      p("path", {
        d: "M18 12.75H6V11.25H18V12.75Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, f));
  }
});
export {
  y as default
};
