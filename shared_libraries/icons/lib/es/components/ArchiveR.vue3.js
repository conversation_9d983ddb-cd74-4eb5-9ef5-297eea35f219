import { defineComponent as c, computed as t, createElementBlock as a, openBlock as u, normalizeStyle as p, normalizeClass as d, createElementVNode as s } from "vue";
const f = ["width", "height"], C = /* @__PURE__ */ c({
  __name: "ArchiveR",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(r) {
    const l = r, n = t(() => {
      const e = ["moxo-icon", "moxo-icon-archive-r"];
      return l.class && e.push(l.class), e.join(" ");
    }), i = t(() => {
      const e = {};
      if (l.color && (e.color = l.color), l.style) {
        if (typeof l.style == "string")
          return [e, l.style];
        Object.assign(e, l.style);
      }
      return e;
    });
    return (e, o) => (u(), a("svg", {
      class: d(n.value),
      style: p(i.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, o[0] || (o[0] = [
      s("path", {
        d: "M15 12.5H9V11H15V12.5Z",
        fill: "currentColor"
      }, null, -1),
      s("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M20.4652 6.45619C20.649 6.64324 20.7519 6.89499 20.7519 7.15723L20.7501 20.2501C20.7501 20.8024 20.3024 21.25 19.7501 21.25H4.25C3.69772 21.25 3.25 20.8023 3.25 20.25V7.1587C3.25 6.89675 3.35278 6.64526 3.53625 6.45829L6.39058 3.5496C6.57862 3.35796 6.83583 3.25 7.10432 3.25H16.8951C17.1634 3.25 17.4204 3.35777 17.6084 3.54909L20.4652 6.45619ZM4.75 19.75H19.25V8.25H4.75V19.75ZM5.35449 6.75H18.6514L16.6855 4.75H7.31543L5.35449 6.75Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, f));
  }
});
export {
  C as default
};
