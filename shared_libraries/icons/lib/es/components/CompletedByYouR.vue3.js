import { defineComponent as i, computed as t, createElementBlock as C, openBlock as u, normalizeStyle as a, normalizeClass as p, createElementVNode as s } from "vue";
const d = ["width", "height"], m = /* @__PURE__ */ i({
  __name: "CompletedByYouR",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(r) {
    const o = r, n = t(() => {
      const e = ["moxo-icon", "moxo-icon-completed-by-you-r"];
      return o.class && e.push(o.class), e.join(" ");
    }), c = t(() => {
      const e = {};
      if (o.color && (e.color = o.color), o.style) {
        if (typeof o.style == "string")
          return [e, o.style];
        Object.assign(e, o.style);
      }
      return e;
    });
    return (e, l) => (u(), C("svg", {
      class: p(n.value),
      style: a(c.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, l[0] || (l[0] = [
      s("path", {
        d: "M20.7812 16.6211L16.7256 20.6767C16.5304 20.872 16.2138 20.872 16.0185 20.6767L13.5488 18.207L14.6094 17.1465L16.3711 18.9082L19.7197 15.5605L20.7812 16.6211Z",
        fill: "currentColor"
      }, null, -1),
      s("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M11.5 3.25C14.3995 3.25 16.75 5.60051 16.75 8.5C16.75 10.2717 15.8716 11.8373 14.5273 12.7881C14.9477 12.943 15.353 13.1289 15.7402 13.3438C15.9746 13.4738 16.2024 13.6144 16.4229 13.7646C16.7255 13.9709 17.0144 14.1959 17.2881 14.4375L16.2949 15.5625C16.0679 15.3621 15.828 15.1759 15.5771 15.0049C15.3945 14.8804 15.2058 14.7639 15.0117 14.6562C13.972 14.0793 12.7757 13.75 11.5 13.75C7.49593 13.75 4.25 16.9959 4.25 21H2.75C2.75 17.2317 5.13215 14.02 8.47266 12.7881C7.12842 11.8373 6.25 10.2717 6.25 8.5C6.25 5.60051 8.6005 3.25 11.5 3.25ZM11.5 4.75C9.42893 4.75 7.75 6.42893 7.75 8.5C7.75 10.5711 9.42893 12.25 11.5 12.25C13.5711 12.25 15.25 10.5711 15.25 8.5C15.25 6.42893 13.5711 4.75 11.5 4.75Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, d));
  }
});
export {
  m as default
};
