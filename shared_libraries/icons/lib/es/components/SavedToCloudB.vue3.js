import { defineComponent as c, computed as t, createElementBlock as i, openBlock as a, normalizeStyle as u, normalizeClass as d, createElementVNode as s } from "vue";
const p = ["width", "height"], m = /* @__PURE__ */ c({
  __name: "SavedToCloudB",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(r) {
    const o = r, C = t(() => {
      const e = ["moxo-icon", "moxo-icon-saved-to-cloud-b"];
      return o.class && e.push(o.class), e.join(" ");
    }), n = t(() => {
      const e = {};
      if (o.color && (e.color = o.color), o.style) {
        if (typeof o.style == "string")
          return [e, o.style];
        Object.assign(e, o.style);
      }
      return e;
    });
    return (e, l) => (a(), i("svg", {
      class: d(C.value),
      style: u(n.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, l[0] || (l[0] = [
      s("path", {
        d: "M15.5859 11.1016L11.3545 15.3339C11.1593 15.5292 10.8427 15.5292 10.6474 15.3339L8.35742 13.0439L9.77148 11.6299L11 12.8584L14.1719 9.6875L15.5859 11.1016Z",
        fill: "currentColor"
      }, null, -1),
      s("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M12 5C13.7939 5 15.1596 5.5533 16.1465 6.41797C16.9227 7.09811 17.4179 7.93288 17.7207 8.74121C17.7647 8.75281 17.8111 8.76379 17.8584 8.77734C18.3001 8.90387 18.8974 9.12425 19.5029 9.50098C20.7506 10.2773 22 11.7112 22 14.1602C22 15.819 21.4728 17.0773 20.5391 17.9072C19.628 18.717 18.4633 18.9999 17.4004 19H6.59961C5.71548 18.9999 4.57659 18.7916 3.63379 18.0771C2.6432 17.3264 2.0001 16.1153 2 14.4004C2 12.3436 2.99192 11.0282 4.13281 10.2676C4.861 9.78216 5.64728 9.52332 6.27344 9.4043C6.51809 8.62726 6.90042 7.69307 7.57812 6.88672C8.50539 5.78351 9.91417 5 12 5ZM12 7C10.4858 7 9.64461 7.53704 9.10938 8.17383C8.53075 8.86233 8.23676 9.75354 8.01562 10.5791C7.8987 11.0163 7.50233 11.3203 7.0498 11.3203C6.7729 11.3204 5.95869 11.454 5.24219 11.9316C4.58308 12.371 4 13.0972 4 14.4004C4.00009 15.565 4.40751 16.1543 4.8418 16.4834C5.32367 16.8484 5.98414 16.9999 6.59961 17H17.4004C18.1371 16.9999 18.7722 16.8021 19.2109 16.4121C19.627 16.042 20 15.3807 20 14.1602C20 12.5291 19.2237 11.6829 18.4463 11.1992C18.0396 10.9462 17.6251 10.7912 17.3076 10.7002C17.1506 10.6552 17.0216 10.6275 16.9365 10.6113C16.8943 10.6033 16.8628 10.5975 16.8447 10.5947C16.836 10.5934 16.8304 10.5931 16.8281 10.5928H16.8311C16.4255 10.545 16.0896 10.253 15.9844 9.8584C15.7972 9.15962 15.4319 8.45093 14.8281 7.92188C14.24 7.40669 13.3559 7 12 7Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, p));
  }
});
export {
  m as default
};
