import { defineComponent as c, computed as s, createElementBlock as i, openBlock as a, normalizeStyle as u, normalizeClass as d, createElementVNode as f } from "vue";
const p = ["width", "height"], C = /* @__PURE__ */ c({
  __name: "CalendarF",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(t) {
    const o = t, n = s(() => {
      const e = ["moxo-icon", "moxo-icon-calendar-f"];
      return o.class && e.push(o.class), e.join(" ");
    }), r = s(() => {
      const e = {};
      if (o.color && (e.color = o.color), o.style) {
        if (typeof o.style == "string")
          return [e, o.style];
        Object.assign(e, o.style);
      }
      return e;
    });
    return (e, l) => (a(), i("svg", {
      class: d(n.value),
      style: u(r.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, l[0] || (l[0] = [
      f("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M9 4H15V2H17V4H20C20.5523 4 21 4.44772 21 5V20C21 20.5523 20.5523 21 20 21H4C3.44772 21 3 20.5523 3 20V5C3 4.44772 3.44772 4 4 4H7V2H9V4ZM10 12.75H12V16.5H14V11.25C14 10.9739 13.7761 10.75 13.5 10.75H10V12.75Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, p));
  }
});
export {
  C as default
};
