import { defineComponent as i, computed as l, createElementBlock as a, openBlock as u, normalizeStyle as p, normalizeClass as f, createElementVNode as s } from "vue";
const m = ["width", "height"], h = /* @__PURE__ */ i({
  __name: "LogOutR",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(r) {
    const o = r, n = l(() => {
      const e = ["moxo-icon", "moxo-icon-log-out-r"];
      return o.class && e.push(o.class), e.join(" ");
    }), c = l(() => {
      const e = {};
      if (o.color && (e.color = o.color), o.style) {
        if (typeof o.style == "string")
          return [e, o.style];
        Object.assign(e, o.style);
      }
      return e;
    });
    return (e, t) => (u(), a("svg", {
      class: f(n.value),
      style: p(c.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, t[0] || (t[0] = [
      s("path", {
        d: "M11 4.75H4.75V19.25H11V20.75H4.25C3.69772 20.75 3.25 20.3023 3.25 19.75V4.25C3.25 3.69772 3.69772 3.25 4.25 3.25H11V4.75Z",
        fill: "currentColor"
      }, null, -1),
      s("path", {
        d: "M21.1464 11.6464C21.3417 11.8417 21.3417 12.1583 21.1464 12.3536L16.4697 17.0303L15.4092 15.9697L18.6289 12.75H8.93945V11.25H18.6289L15.4092 8.03027L16.4697 6.96973L21.1464 11.6464Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, m));
  }
});
export {
  h as default
};
