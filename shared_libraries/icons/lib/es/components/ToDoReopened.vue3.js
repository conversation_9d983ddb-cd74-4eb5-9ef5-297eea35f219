import { defineComponent as c, computed as s, createElementBlock as a, openBlock as d, normalizeStyle as L, normalizeClass as p, createElementVNode as t } from "vue";
const u = ["width", "height"], m = /* @__PURE__ */ c({
  __name: "ToDoReopened",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(n) {
    const o = n, r = s(() => {
      const e = ["moxo-icon", "moxo-icon-to-do-reopened"];
      return o.class && e.push(o.class), e.join(" ");
    }), i = s(() => {
      const e = {};
      if (o.color && (e.color = o.color), o.style) {
        if (typeof o.style == "string")
          return [e, o.style];
        Object.assign(e, o.style);
      }
      return e;
    });
    return (e, l) => (d(), a("svg", {
      class: p(r.value),
      style: L(i.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      version: "1.1",
      xmlns: "http://www.w3.org/2000/svg",
      "xmlns:xlink": "http://www.w3.org/1999/xlink"
    }, l[0] || (l[0] = [
      t("title", null, "Icon/04 - Flat/To-Do-Reopened", -1),
      t("desc", null, "Created with Sketch.", -1),
      t("g", {
        id: "Icon/04---Flat/To-Do-Reopened",
        stroke: "none",
        "stroke-:width": "size",
        fill: "currentColor",
        "fill-rule": "evenodd"
      }, [
        t("path", {
          d: "M7,13.4142136 L8.41421356,12 L9.82842712,13.4142136 L8.41421356,14.8284271 L7,13.4142136 Z M15,9.41421356 L16.4142136,8 L17.8284271,9.41421356 L16.4142136,10.8284271 L15,9.41421356 Z M13,11.4142136 L14.4142136,10 L15.8284271,11.4142136 L14.4142136,12.8284271 L13,11.4142136 Z M11,13.4142136 L12.4142136,12 L13.8284271,13.4142136 L12.4142136,14.8284271 L11,13.4142136 Z M9,15.4142136 L10.4142136,14 L11.8284271,15.4142136 L10.4142136,16.8284271 L9,15.4142136 Z",
          id: "Color",
          fill: "currentColor"
        })
      ], -1)
    ]), 14, u));
  }
});
export {
  m as default
};
