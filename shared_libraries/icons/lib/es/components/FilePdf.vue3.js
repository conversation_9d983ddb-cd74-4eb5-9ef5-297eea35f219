import { defineComponent as a, computed as s, createElementBlock as i, openBlock as p, normalizeStyle as u, normalizeClass as m, createElementVNode as n } from "vue";
const x = /* @__PURE__ */ a({
  __name: "FilePdf",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(o) {
    const c = o, t = s(() => {
      const l = ["moxo-icon", "moxo-icon-file-pdf"];
      return c.class && l.push(c.class), l.join(" ");
    }), r = s(() => {
      const l = {};
      if (c.color && (l.color = c.color), c.style) {
        if (typeof c.style == "string")
          return [l, c.style];
        Object.assign(l, c.style);
      }
      return l;
    });
    return (l, e) => (p(), i("svg", {
      class: m(t.value),
      style: u([r.value, { "enable-background": "new 0 0 20 20" }]),
      version: "1.1",
      id: "Layer_1",
      xmlns: "http://www.w3.org/2000/svg",
      "xmlns:xlink": "http://www.w3.org/1999/xlink",
      x: "0px",
      y: "0px",
      viewBox: "0 0 20 20",
      "xml:space": "preserve"
    }, e[0] || (e[0] = [
      n("path", {
        class: "st0",
        d: `M9,1c1.4,0,3.4,0.9,4.4,1.8l1.8,1.8c1,1,1.8,3,1.8,4.4v8c0,1.1-0.9,2-2,2H5c-1.1,0-2-0.9-2-2V3c0-1.1,0.9-2,2-2\r
	H9 M9,0H5C3.3,0,2,1.4,2,3v14c0,1.6,1.3,3,3,3h10c1.6,0,3-1.4,3-3V9c0-1.7-1-4-2.1-5.1l-1.8-1.8C13,1,10.6,0,9,0L9,0z`
      }, null, -1),
      n("path", {
        class: "st0",
        d: `M15.9,12.3c0.1-0.2,0.1-0.3,0-0.5c-0.2-0.3-0.9-0.5-2-0.5c-0.4,0-0.9,0-1.4,0.1c-0.4-0.2-0.7-0.5-1.1-0.7\r
	c-0.4-0.4-1.3-1.2-1.8-3c0.2-0.8,0.2-1.6,0.2-2.3c0-0.1,0-0.2,0-0.4L9.7,5c0,0.1-0.1,0.3-0.1,0.5C9.5,5.9,9.5,6.3,9.3,6.9\r
	c-0.3-1-0.3-2-0.1-2.5c0-0.1,0.1-0.2,0.1-0.2c0.1,0.1,0.3,0.2,0.4,0.7l0.1,0c0.1-0.3,0-0.6-0.1-0.7C9.6,4,9.5,4,9.4,4h0L9.2,4\r
	C9.1,4,8.9,4,8.8,4.4c-0.2,0.7-0.1,2,0.3,3.2c0,0.1,0,0.1,0.1,0.2c-0.3,0.9-0.7,2-1.3,3.2c-0.8,1.6-1.5,2.8-2.1,3.6\r
	c-0.6,0.8-1,1.2-1.4,1.2c0,0,0,0,0,0c0-0.4,0.8-1.5,1.9-2.4l0-0.1c-0.7,0.4-1.2,0.8-1.6,1.2c-0.3,0.3-0.5,0.7-0.6,1\r
	c0,0.1,0,0.2,0,0.3l0,0l0,0l0.3,0.2C4.4,16,4.5,16,4.6,16l0,0c0.7,0,1.6-1,2.8-3.1c1.3-0.5,3.2-0.9,4.9-1c0.3,0.2,0.6,0.3,0.9,0.4\r
	c0.7,0.3,1.5,0.5,2,0.5c0.5,0,0.7-0.2,0.7-0.3L15.9,12.3l-0.1,0.1c-0.1,0-0.2,0.1-0.4,0.1l0,0c-0.5,0-1.4-0.2-2.3-0.7\r
	c0.3,0,0.5,0,0.8,0c1,0,1.6,0.1,1.8,0.3C15.8,12.1,15.9,12.2,15.9,12.3L15.9,12.3z M8.3,11.3c0.6-1.1,0.9-2,1.1-2.8\r
	c0.4,1,1,1.9,1.7,2.5c0.2,0.2,0.4,0.3,0.7,0.5c-0.3,0.1-0.7,0.1-1,0.2c-1.1,0.2-2.2,0.6-3.2,1C7.7,12.3,8,11.8,8.3,11.3z`
      }, null, -1)
    ]), 6));
  }
});
export {
  x as default
};
