import { defineComponent as c, computed as s, createElementBlock as i, openBlock as a, normalizeStyle as u, normalizeClass as d, createElementVNode as p } from "vue";
const f = ["width", "height"], w = /* @__PURE__ */ c({
  __name: "DropdownCenteredArrowDownR",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(r) {
    const o = r, l = s(() => {
      const e = ["moxo-icon", "moxo-icon-dropdown-centered-arrow-down-r"];
      return o.class && e.push(o.class), e.join(" ");
    }), n = s(() => {
      const e = {};
      if (o.color && (e.color = o.color), o.style) {
        if (typeof o.style == "string")
          return [e, o.style];
        Object.assign(e, o.style);
      }
      return e;
    });
    return (e, t) => (a(), i("svg", {
      class: d(l.value),
      style: u(n.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, t[0] || (t[0] = [
      p("path", {
        d: "M18.0605 9.5L12.3536 15.207C12.1583 15.4023 11.8417 15.4023 11.6464 15.207L5.93945 9.5L7 8.43945L12 13.4395L17 8.43945L18.0605 9.5Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, f));
  }
});
export {
  w as default
};
