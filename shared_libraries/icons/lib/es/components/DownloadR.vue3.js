import { defineComponent as i, computed as t, createElementBlock as a, openBlock as u, normalizeStyle as p, normalizeClass as d, createElementVNode as s } from "vue";
const f = ["width", "height"], h = /* @__PURE__ */ i({
  __name: "DownloadR",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(n) {
    const e = n, r = t(() => {
      const o = ["moxo-icon", "moxo-icon-download-r"];
      return e.class && o.push(e.class), o.join(" ");
    }), c = t(() => {
      const o = {};
      if (e.color && (o.color = e.color), e.style) {
        if (typeof e.style == "string")
          return [o, e.style];
        Object.assign(o, e.style);
      }
      return o;
    });
    return (o, l) => (u(), a("svg", {
      class: d(r.value),
      style: p(c.value),
      width: o.size,
      height: o.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, l[0] || (l[0] = [
      s("path", {
        d: "M19.25 20.5H4.75V19H19.25V20.5Z",
        fill: "currentColor"
      }, null, -1),
      s("path", {
        d: "M12.75 14.1953L16.5762 10.3691L17.6367 11.4297L12.3594 16.707C12.1642 16.9023 11.8476 16.9023 11.6523 16.707L6.42383 11.4785L7.48438 10.418L11.25 14.1836V3.75H12.75V14.1953Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, f));
  }
});
export {
  h as default
};
