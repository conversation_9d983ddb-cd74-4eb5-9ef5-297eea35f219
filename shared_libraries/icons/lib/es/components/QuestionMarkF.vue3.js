import { defineComponent as i, computed as t, createElementBlock as c, openBlock as a, normalizeStyle as u, normalizeClass as C, createElementVNode as f } from "vue";
const p = ["width", "height"], m = /* @__PURE__ */ i({
  __name: "QuestionMarkF",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(l) {
    const o = l, n = t(() => {
      const e = ["moxo-icon", "moxo-icon-question-mark-f"];
      return o.class && e.push(o.class), e.join(" ");
    }), r = t(() => {
      const e = {};
      if (o.color && (e.color = o.color), o.style) {
        if (typeof o.style == "string")
          return [e, o.style];
        Object.assign(e, o.style);
      }
      return e;
    });
    return (e, s) => (a(), c("svg", {
      class: C(n.value),
      style: u(r.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, s[0] || (s[0] = [
      f("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2ZM12 16.5C11.4477 16.5 11 16.9477 11 17.5C11 18.0523 11.4477 18.5 12 18.5C12.5523 18.5 13 18.0523 13 17.5C13 16.9477 12.5523 16.5 12 16.5ZM12 5.5C9.79086 5.5 8 7.29086 8 9.5V10.5H10V9.5C10 8.39543 10.8954 7.5 12 7.5C13.1046 7.5 14 8.39543 14 9.5C14 10.6046 13.1046 11.5 12 11.5C11.4477 11.5 11 11.9477 11 12.5V15.5H13V13.374C14.7252 12.93 16 11.3638 16 9.5C16 7.29086 14.2091 5.5 12 5.5Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, p));
  }
});
export {
  m as default
};
