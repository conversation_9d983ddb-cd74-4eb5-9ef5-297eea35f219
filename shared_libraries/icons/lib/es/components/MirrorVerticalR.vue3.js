import { defineComponent as c, computed as t, createElementBlock as u, openBlock as a, normalizeStyle as d, normalizeClass as p, createElementVNode as o } from "vue";
const f = ["width", "height"], C = /* @__PURE__ */ c({
  __name: "MirrorVerticalR",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(s) {
    const l = s, n = t(() => {
      const e = ["moxo-icon", "moxo-icon-mirror-vertical-r"];
      return l.class && e.push(l.class), e.join(" ");
    }), i = t(() => {
      const e = {};
      if (l.color && (e.color = l.color), l.style) {
        if (typeof l.style == "string")
          return [e, l.style];
        Object.assign(e, l.style);
      }
      return e;
    });
    return (e, r) => (a(), u("svg", {
      class: p(n.value),
      style: d(i.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 24 24",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, r[0] || (r[0] = [
      o("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M18.1464 20.1464C18.4614 20.4614 18.2383 21 17.7929 21H6.20711C5.76165 21 5.53857 20.4614 5.85355 20.1464L11.6464 14.3536C11.8417 14.1583 12.1583 14.1583 12.3536 14.3536L18.1464 20.1464ZM8.62109 19.5H15.3789L12 16.1211L8.62109 19.5Z",
        fill: "currentColor"
      }, null, -1),
      o("path", {
        d: "M21 12.75H3V11.25H21V12.75Z",
        fill: "currentColor"
      }, null, -1),
      o("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M12.3536 9.64645C12.1583 9.84171 11.8417 9.84171 11.6464 9.64645L5.85355 3.85355C5.53857 3.53857 5.76165 3 6.20711 3H17.7929C18.2383 3 18.4614 3.53857 18.1464 3.85355L12.3536 9.64645ZM12 7.87891L15.3789 4.5H8.62109L12 7.87891Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, f));
  }
});
export {
  C as default
};
