import { defineComponent as c, computed as s, createElementBlock as i, openBlock as a, normalizeStyle as u, normalizeClass as C, createElementVNode as f } from "vue";
const p = ["width", "height"], m = /* @__PURE__ */ c({
  __name: "FlowCover",
  props: {
    size: { default: 24 },
    color: { default: "currentColor" },
    class: {},
    style: {}
  },
  setup(t) {
    const o = t, r = s(() => {
      const e = ["moxo-icon", "moxo-icon-flow-cover"];
      return o.class && e.push(o.class), e.join(" ");
    }), n = s(() => {
      const e = {};
      if (o.color && (e.color = o.color), o.style) {
        if (typeof o.style == "string")
          return [e, o.style];
        Object.assign(e, o.style);
      }
      return e;
    });
    return (e, l) => (a(), i("svg", {
      class: C(r.value),
      style: u(n.value),
      width: e.size,
      height: e.size,
      viewBox: "0 0 12 12",
      fill: "currentColor",
      xmlns: "http://www.w3.org/2000/svg"
    }, l[0] || (l[0] = [
      f("path", {
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
        d: "M1 0H7C7.53043 0 8.03923 0.210712 8.41431 0.585785C8.78938 0.960858 9 1.46957 9 2V10C9 10.5304 8.78938 11.0391 8.41431 11.4142C8.03923 11.7893 7.53043 12 7 12H1C0.447715 12 0 11.5523 0 11V1C0 0.447715 0.447715 0 1 0ZM9.36561 0.172839C9.31222 0.104388 9.35923 0 9.44604 0H11C11.5523 0 12 0.447715 12 1V3.57531C12 3.84671 11.8897 4.10645 11.6944 4.2949L10.1695 5.76648C10.106 5.82775 10 5.78276 10 5.69452V2C9.99607 1.33554 9.7717 0.693456 9.36561 0.172839Z",
        fill: "currentColor"
      }, null, -1)
    ]), 14, p));
  }
});
export {
  m as default
};
