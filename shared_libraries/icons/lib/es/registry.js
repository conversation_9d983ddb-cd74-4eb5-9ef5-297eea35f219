var s = Object.defineProperty;
var i = (e, r, n) => r in e ? s(e, r, { enumerable: !0, configurable: !0, writable: !0, value: n }) : e[r] = n;
var o = (e, r, n) => i(e, typeof r != "symbol" ? r + "" : r, n);
class c {
  constructor() {
    o(this, "icons", /* @__PURE__ */ new Map());
  }
  register(r, n) {
    this.icons.set(r, n);
  }
  get(r) {
    return this.icons.get(r);
  }
  getNames() {
    return Array.from(this.icons.keys());
  }
  has(r) {
    return this.icons.has(r);
  }
  clear() {
    this.icons.clear();
  }
}
const t = new c();
function g(e) {
  Object.entries(e).forEach(([r, n]) => {
    t.register(r, n);
  });
}
async function l(e) {
  if (t.has(e))
    return t.get(e);
  try {
    console.warn(`Dynamic loading not implemented for icon: ${e}`);
    return;
  } catch (r) {
    console.warn(`Failed to load icon: ${e}`, r);
    return;
  }
}
export {
  t as iconRegistry,
  l as loadAndRegisterIcon,
  g as registerIcons
};
