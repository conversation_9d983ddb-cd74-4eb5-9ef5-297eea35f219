"use strict";const s=require("vue"),i=s.defineComponent({__name:"Icon",props:{name:{},size:{default:24},color:{default:"currentColor"},class:{},style:{},rotate:{},flip:{}},setup(n){const e=n,r=s.computed(()=>s.defineAsyncComponent(()=>import(`~icons/moxo/${e.name}`))),c=s.computed(()=>{const o=["moxo-icon"];return e.class&&o.push(e.class),o.join(" ")}),l=s.computed(()=>{const o={};if(e.size){const t=typeof e.size=="number"?`${e.size}px`:e.size;o.width=t,o.height=t}if(e.color&&(o.color=e.color),e.rotate&&(o.transform=`rotate(${e.rotate}deg)`),e.flip){const t=[];(e.flip==="horizontal"||e.flip==="both")&&t.push("scaleX(-1)"),(e.flip==="vertical"||e.flip==="both")&&t.push("scaleY(-1)"),t.length>0&&(o.transform=t.join(" "))}if(e.style){if(typeof e.style=="string")return[o,e.style];Object.assign(o,e.style)}return o});return(o,t)=>(s.openBlock(),s.createBlock(s.resolveDynamicComponent(r.value),s.mergeProps({class:c.value,style:l.value},o.$attrs),null,16,["class","style"]))}});module.exports=i;
