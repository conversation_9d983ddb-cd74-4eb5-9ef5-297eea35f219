"use strict";const s=require("vue"),i=require("./generated.cjs"),a=s.defineComponent({__name:"Icon",props:{name:{},size:{default:24},color:{default:"currentColor"},class:{},style:{},rotate:{},flip:{}},setup(n){const e=n,r=s.computed(()=>s.defineAsyncComponent(()=>i.iconComponents[e.name]())),c=s.computed(()=>{const t=["moxo-icon"];return e.class&&t.push(e.class),t.join(" ")}),l=s.computed(()=>{const t={};if(e.size){const o=typeof e.size=="number"?`${e.size}px`:e.size;t.width=o,t.height=o}if(e.color&&(t.color=e.color),e.rotate&&(t.transform=`rotate(${e.rotate}deg)`),e.flip){const o=[];(e.flip==="horizontal"||e.flip==="both")&&o.push("scaleX(-1)"),(e.flip==="vertical"||e.flip==="both")&&o.push("scaleY(-1)"),o.length>0&&(t.transform=o.join(" "))}if(e.style){if(typeof e.style=="string")return[t,e.style];Object.assign(t,e.style)}return t});return(t,o)=>(s.openBlock(),s.createBlock(s.resolveDynamicComponent(r.value),s.mergeProps({class:c.value,style:l.value},t.$attrs),null,16,["class","style"]))}});module.exports=a;
