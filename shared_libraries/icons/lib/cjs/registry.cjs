"use strict";var i=Object.defineProperty;var s=(r,e,t)=>e in r?i(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var o=(r,e,t)=>s(r,typeof e!="symbol"?e+"":e,t);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});class c{constructor(){o(this,"icons",new Map)}register(e,t){this.icons.set(e,t)}get(e){return this.icons.get(e)}getNames(){return Array.from(this.icons.keys())}has(e){return this.icons.has(e)}clear(){this.icons.clear()}}const n=new c;function a(r){Object.entries(r).forEach(([e,t])=>{n.register(e,t)})}async function g(r){if(n.has(r))return n.get(r);try{console.warn(`Dynamic loading not implemented for icon: ${r}`);return}catch(e){console.warn(`Failed to load icon: ${r}`,e);return}}exports.iconRegistry=n;exports.loadAndRegisterIcon=g;exports.registerIcons=a;
