"use strict";const e=require("vue"),c=["width","height"],i=e.defineComponent({__name:"Circle",props:{size:{default:24},color:{default:"currentColor"},class:{},style:{}},setup(s){const l=s,r=e.computed(()=>{const t=["moxo-icon","moxo-icon-circle"];return l.class&&t.push(l.class),t.join(" ")}),n=e.computed(()=>{const t={};if(l.color&&(t.color=l.color),l.style){if(typeof l.style=="string")return[t,l.style];Object.assign(t,l.style)}return t});return(t,o)=>(e.openBlock(),e.createElementBlock("svg",{class:e.normalizeClass(r.value),style:e.normalizeStyle(n.value),width:t.size,height:t.size,viewBox:"0 0 24 24",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"},o[0]||(o[0]=[e.createElementVNode("title",null,"Icon/Default/Circle",-1),e.createElementVNode("desc",null,"Created with Sketch.",-1),e.createElementVNode("defs",null,[e.createElementVNode("path",{d:"M12,2 C17.522,2 22,6.477 22,12 C22,17.523 17.522,22 12,22 C6.478,22 2,17.523 2,12 C2,6.477 6.478,2 12,2 Z M12,4 C7.589,4 4,7.589 4,12 C4,16.411 7.589,20 12,20 C16.411,20 20,16.411 20,12 C20,7.589 16.411,4 12,4 Z",id:"path-1"})],-1),e.createElementVNode("g",{id:"Default",stroke:"none","stroke-:width":"size",fill:"currentColor","fill-rule":"evenodd"},[e.createElementVNode("g",{id:"Icon/Default/Circle"},[e.createElementVNode("mask",{id:"mask-2",fill:"currentColor"},[e.createElementVNode("use",{"xlink:href":"#path-1"})]),e.createElementVNode("use",{id:"Mask",fill:"currentColor","xlink:href":"#path-1"})])],-1)]),14,c))}});module.exports=i;
