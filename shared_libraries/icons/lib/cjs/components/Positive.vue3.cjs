"use strict";const e=require("vue"),i=["width","height"],c=e.defineComponent({__name:"Positive",props:{size:{default:24},color:{default:"currentColor"},class:{},style:{}},setup(l){const o=l,n=e.computed(()=>{const t=["moxo-icon","moxo-icon-positive"];return o.class&&t.push(o.class),t.join(" ")}),r=e.computed(()=>{const t={};if(o.color&&(t.color=o.color),o.style){if(typeof o.style=="string")return[t,o.style];Object.assign(t,o.style)}return t});return(t,s)=>(e.openBlock(),e.createElementBlock("svg",{class:e.normalizeClass(n.value),style:e.normalizeStyle(r.value),width:t.size,height:t.size,viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"},s[0]||(s[0]=[e.createElementVNode("title",null,"Icon/03 - XS/Positive",-1),e.createElementVNode("desc",null,"Created with Sketch.",-1),e.createElementVNode("defs",null,[e.createElementVNode("path",{d:"M7,8 L7,6 L5,6 L5,8 L5,10 L12,10 L12,8 L7,8 Z M8,0 C12.418,0 16,3.582 16,8 C16,12.418 12.418,16 8,16 C3.581,16 0,12.418 0,8 C0,3.582 3.581,0 8,0 Z",id:"path-1"})],-1),e.createElementVNode("g",{id:"Icon/03---XS/Positive",stroke:"none","stroke-:width":"size",fill:"currentColor","fill-rule":"evenodd"},[e.createElementVNode("mask",{id:"mask-2",fill:"currentColor"},[e.createElementVNode("use",{"xlink:href":"#path-1"})]),e.createElementVNode("use",{id:"Color",fill:"currentColor",transform:"translate(8.000000, 8.000000) rotate(-45.000000) translate(-8.000000, -8.000000) ","xlink:href":"#path-1"})],-1)]),14,i))}});module.exports=c;
