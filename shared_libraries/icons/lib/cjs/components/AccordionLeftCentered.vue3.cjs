"use strict";const t=require("vue"),c=["width","height"],i={id:"clip0_2069_1658"},a=["width","height"],d=t.defineComponent({__name:"AccordionLeftCentered",props:{size:{default:24},color:{default:"currentColor"},class:{},style:{}},setup(l){const o=l,r=t.computed(()=>{const e=["moxo-icon","moxo-icon-accordion-left-centered"];return o.class&&e.push(o.class),e.join(" ")}),n=t.computed(()=>{const e={};if(o.color&&(e.color=o.color),o.style){if(typeof o.style=="string")return[e,o.style];Object.assign(e,o.style)}return e});return(e,s)=>(t.openBlock(),t.createElementBlock("svg",{class:t.normalizeClass(r.value),style:t.normalizeStyle(n.value),width:e.size,height:e.size,viewBox:"0 0 16 16",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},[s[0]||(s[0]=t.createElementVNode("g",{"clip-path":"url(#clip0_2069_1658)"},[t.createElementVNode("path",{d:"M4.51472 7.94971C4.51472 7.69378 4.61235 7.43786 4.80761 7.2426L8.34315 3.70707C8.73367 3.31654 9.36684 3.31654 9.75736 3.70707C10.1479 4.09759 10.1479 4.73076 9.75736 5.12128L6.92893 7.94971L9.75736 10.7781C10.1479 11.1687 10.1479 11.8018 9.75736 12.1923C9.36684 12.5829 8.73367 12.5829 8.34315 12.1923L4.80761 8.65681C4.61235 8.46155 4.51472 8.20563 4.51472 7.94971Z",fill:"currentColor"})],-1)),t.createElementVNode("defs",null,[t.createElementVNode("clipPath",i,[t.createElementVNode("rect",{width:e.size,height:e.size,fill:"currentColor"},null,8,a)])])],14,c))}});module.exports=d;
