"use strict";const t=require("vue"),i=["width","height"],c={id:"clip0_354_16715"},a=["width","height"],d=t.defineComponent({__name:"AccordionRightCentered",props:{size:{default:24},color:{default:"currentColor"},class:{},style:{}},setup(l){const o=l,r=t.computed(()=>{const e=["moxo-icon","moxo-icon-accordion-right-centered"];return o.class&&e.push(o.class),e.join(" ")}),n=t.computed(()=>{const e={};if(o.color&&(e.color=o.color),o.style){if(typeof o.style=="string")return[e,o.style];Object.assign(e,o.style)}return e});return(e,s)=>(t.openBlock(),t.createElementBlock("svg",{class:t.normalizeClass(r.value),style:t.normalizeStyle(n.value),width:e.size,height:e.size,viewBox:"0 0 16 16",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},[s[0]||(s[0]=t.createElementVNode("g",{"clip-path":"url(#clip0_354_16715)"},[t.createElementVNode("path",{d:"M11.4853 7.94971C11.4853 8.20563 11.3876 8.46155 11.1924 8.65681L7.65685 12.1923C7.26633 12.5829 6.63316 12.5829 6.24264 12.1923C5.85212 11.8018 5.85212 11.1687 6.24264 10.7781L9.07107 7.94971L6.24264 5.12128C5.85212 4.73076 5.85212 4.09759 6.24264 3.70707C6.63316 3.31654 7.26633 3.31654 7.65685 3.70707L11.1924 7.2426C11.3876 7.43786 11.4853 7.69378 11.4853 7.94971Z",fill:"currentColor"})],-1)),t.createElementVNode("defs",null,[t.createElementVNode("clipPath",c,[t.createElementVNode("rect",{width:e.size,height:e.size,fill:"currentColor"},null,8,a)])])],14,i))}});module.exports=d;
