"use strict";const t=require("vue"),c=["width","height"],i=t.defineComponent({__name:"DropdownCenteredArrowLeftB",props:{size:{default:24},color:{default:"currentColor"},class:{},style:{}},setup(r){const o=r,l=t.computed(()=>{const e=["moxo-icon","moxo-icon-dropdown-centered-arrow-left-b"];return o.class&&e.push(o.class),e.join(" ")}),n=t.computed(()=>{const e={};if(o.color&&(e.color=o.color),o.style){if(typeof o.style=="string")return[e,o.style];Object.assign(e,o.style)}return e});return(e,s)=>(t.openBlock(),t.createElementBlock("svg",{class:t.normalizeClass(l.value),style:t.normalizeStyle(n.value),width:e.size,height:e.size,viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},s[0]||(s[0]=[t.createElementVNode("path",{d:"M15.914 7L10.914 12L15.914 17L14.5 18.4141L8.43948 12.3536C8.24421 12.1583 8.24421 11.8417 8.43948 11.6464L14.5 5.58594L15.914 7Z",fill:"currentColor"},null,-1)]),14,c))}});module.exports=i;
