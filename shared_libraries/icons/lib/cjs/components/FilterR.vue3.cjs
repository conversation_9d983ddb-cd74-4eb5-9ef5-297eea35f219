"use strict";const l=require("vue"),i=["width","height"],c=l.defineComponent({__name:"FilterR",props:{size:{default:24},color:{default:"currentColor"},class:{},style:{}},setup(s){const o=s,r=l.computed(()=>{const e=["moxo-icon","moxo-icon-filter-r"];return o.class&&e.push(o.class),e.join(" ")}),n=l.computed(()=>{const e={};if(o.color&&(e.color=o.color),o.style){if(typeof o.style=="string")return[e,o.style];Object.assign(e,o.style)}return e});return(e,t)=>(l.openBlock(),l.createElementBlock("svg",{class:l.normalizeClass(r.value),style:l.normalizeStyle(n.value),width:e.size,height:e.size,viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},t[0]||(t[0]=[l.createElementVNode("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M17.5 13.25C19.2949 13.25 20.75 14.7051 20.75 16.5C20.75 18.2949 19.2949 19.75 17.5 19.75C15.9633 19.75 14.6765 18.6832 14.3379 17.25H3V15.75H14.3379C14.6765 14.3168 15.9633 13.25 17.5 13.25ZM17.5 14.75C16.5335 14.75 15.75 15.5335 15.75 16.5C15.75 17.4665 16.5335 18.25 17.5 18.25C18.4665 18.25 19.25 17.4665 19.25 16.5C19.25 15.5335 18.4665 14.75 17.5 14.75Z",fill:"currentColor"},null,-1),l.createElementVNode("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M6.5 4.25C8.03672 4.25 9.32345 5.31675 9.66211 6.75H21V8.25H9.66211C9.32345 9.68325 8.03672 10.75 6.5 10.75C4.70507 10.75 3.25 9.29493 3.25 7.5C3.25 5.70507 4.70507 4.25 6.5 4.25ZM6.5 5.75C5.5335 5.75 4.75 6.5335 4.75 7.5C4.75 8.4665 5.5335 9.25 6.5 9.25C7.4665 9.25 8.25 8.4665 8.25 7.5C8.25 6.5335 7.4665 5.75 6.5 5.75Z",fill:"currentColor"},null,-1)]),14,i))}});module.exports=c;
