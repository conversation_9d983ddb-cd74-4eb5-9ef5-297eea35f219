export { default as AccordionLeftCentered } from './components/AccordionLeftCentered.vue';
export { default as AccordionRightCentered } from './components/AccordionRightCentered.vue';
export type MoxoIconName = 'accordion-left-centered' | 'accordion-right-centered';
export declare const iconNames: MoxoIconName[];
export declare const iconComponents: {
    readonly 'accordion-left-centered': () => Promise<typeof import("*.vue")>;
    readonly 'accordion-right-centered': () => Promise<typeof import("*.vue")>;
};
//# sourceMappingURL=generated.d.ts.map