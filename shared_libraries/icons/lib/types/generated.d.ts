export { default as Integration } from './components/Integration.vue';
export { default as InviteLink } from './components/InviteLink.vue';
export { default as Jumio } from './components/Jumio.vue';
export { default as Template } from './components/Template.vue';
export { default as AccordionDownF } from './components/AccordionDownF.vue';
export { default as AccordionLeftF } from './components/AccordionLeftF.vue';
export { default as AccordionRightF } from './components/AccordionRightF.vue';
export { default as AccordionUpF } from './components/AccordionUpF.vue';
export { default as AcknowledgementNewly } from './components/AcknowledgementNewly.vue';
export { default as ActionCheck } from './components/ActionCheck.vue';
export { default as ActionFilesR } from './components/ActionFilesR.vue';
export { default as ActiveB } from './components/ActiveB.vue';
export { default as ActiveR } from './components/ActiveR.vue';
export { default as AddB } from './components/AddB.vue';
export { default as AddR } from './components/AddR.vue';
export { default as AddWithCircleB } from './components/AddWithCircleB.vue';
export { default as AddWithCircleF } from './components/AddWithCircleF.vue';
export { default as AddWithCircleR } from './components/AddWithCircleR.vue';
export { default as AdminUserB } from './components/AdminUserB.vue';
export { default as AdminUserR } from './components/AdminUserR.vue';
export { default as AmMeeting } from './components/AmMeeting.vue';
export { default as Annotation } from './components/Annotation.vue';
export { default as Approval } from './components/Approval.vue';
export { default as ArchiveB } from './components/ArchiveB.vue';
export { default as ArchiveF } from './components/ArchiveF.vue';
export { default as ArchiveR } from './components/ArchiveR.vue';
export { default as ArrowAnnotation } from './components/ArrowAnnotation.vue';
export { default as ArrowLargeDownB } from './components/ArrowLargeDownB.vue';
export { default as ArrowLargeDownR } from './components/ArrowLargeDownR.vue';
export { default as ArrowLargeUpB } from './components/ArrowLargeUpB.vue';
export { default as ArrowLargeUpR } from './components/ArrowLargeUpR.vue';
export { default as ArrowLeftB } from './components/ArrowLeftB.vue';
export { default as ArrowLeftR } from './components/ArrowLeftR.vue';
export { default as ArrowRightB } from './components/ArrowRightB.vue';
export { default as ArrowRightR } from './components/ArrowRightR.vue';
export { default as AssigneeR } from './components/AssigneeR.vue';
export { default as Assistance } from './components/Assistance.vue';
export { default as AttachmentB } from './components/AttachmentB.vue';
export { default as AttachmentR } from './components/AttachmentR.vue';
export { default as Automation } from './components/Automation.vue';
export { default as BarB } from './components/BarB.vue';
export { default as BarR } from './components/BarR.vue';
export { default as BellMuted } from './components/BellMuted.vue';
export { default as BellOutline } from './components/BellOutline.vue';
export { default as Bell } from './components/Bell.vue';
export { default as Binder } from './components/Binder.vue';
export { default as BookmarkBadge } from './components/BookmarkBadge.vue';
export { default as Bookmark } from './components/Bookmark.vue';
export { default as Branch } from './components/Branch.vue';
export { default as BusinessR } from './components/BusinessR.vue';
export { default as CalendarB } from './components/CalendarB.vue';
export { default as CalendarF } from './components/CalendarF.vue';
export { default as CalendarR } from './components/CalendarR.vue';
export { default as CallCalendarFull } from './components/CallCalendarFull.vue';
export { default as Cancel } from './components/Cancel.vue';
export { default as Card } from './components/Card.vue';
export { default as CcB } from './components/CcB.vue';
export { default as CcR } from './components/CcR.vue';
export { default as CheckAccountB } from './components/CheckAccountB.vue';
export { default as CheckAccountR } from './components/CheckAccountR.vue';
export { default as CheckB } from './components/CheckB.vue';
export { default as CheckR } from './components/CheckR.vue';
export { default as CircleCheckbox } from './components/CircleCheckbox.vue';
export { default as Circle } from './components/Circle.vue';
export { default as ClockB } from './components/ClockB.vue';
export { default as ClockF } from './components/ClockF.vue';
export { default as ClockR } from './components/ClockR.vue';
export { default as CloseB } from './components/CloseB.vue';
export { default as CloseDrawerB } from './components/CloseDrawerB.vue';
export { default as CloseDrawerR } from './components/CloseDrawerR.vue';
export { default as CloseR } from './components/CloseR.vue';
export { default as Comment } from './components/Comment.vue';
export { default as Complete2B } from './components/Complete2B.vue';
export { default as Complete2R } from './components/Complete2R.vue';
export { default as CompletedB } from './components/CompletedB.vue';
export { default as CompletedByOthersB } from './components/CompletedByOthersB.vue';
export { default as CompletedByOthersR } from './components/CompletedByOthersR.vue';
export { default as CompletedByYouB } from './components/CompletedByYouB.vue';
export { default as CompletedByYouR } from './components/CompletedByYouR.vue';
export { default as CompletedF } from './components/CompletedF.vue';
export { default as CompletedR } from './components/CompletedR.vue';
export { default as ContentLibraryOutline } from './components/ContentLibraryOutline.vue';
export { default as ConversationChat } from './components/ConversationChat.vue';
export { default as CopyB } from './components/CopyB.vue';
export { default as CopyR } from './components/CopyR.vue';
export { default as CreateWorkspaceR } from './components/CreateWorkspaceR.vue';
export { default as DdrTextR } from './components/DdrTextR.vue';
export { default as DeactivateB } from './components/DeactivateB.vue';
export { default as DeactivateF } from './components/DeactivateF.vue';
export { default as DeactivateR } from './components/DeactivateR.vue';
export { default as DeleteB } from './components/DeleteB.vue';
export { default as DeleteF } from './components/DeleteF.vue';
export { default as DeleteFeed } from './components/DeleteFeed.vue';
export { default as DeleteR } from './components/DeleteR.vue';
export { default as Dependency } from './components/Dependency.vue';
export { default as DesktopB } from './components/DesktopB.vue';
export { default as DesktopR } from './components/DesktopR.vue';
export { default as DialPad } from './components/DialPad.vue';
export { default as DistributionList } from './components/DistributionList.vue';
export { default as DownloadB } from './components/DownloadB.vue';
export { default as DownloadR } from './components/DownloadR.vue';
export { default as DragHandleB } from './components/DragHandleB.vue';
export { default as DragHandleR } from './components/DragHandleR.vue';
export { default as DropdownCenteredArrowDownB } from './components/DropdownCenteredArrowDownB.vue';
export { default as DropdownCenteredArrowDownR } from './components/DropdownCenteredArrowDownR.vue';
export { default as DropdownCenteredArrowLeftB } from './components/DropdownCenteredArrowLeftB.vue';
export { default as DropdownCenteredArrowLeftR } from './components/DropdownCenteredArrowLeftR.vue';
export { default as DropdownCenteredArrowRightB } from './components/DropdownCenteredArrowRightB.vue';
export { default as DropdownCenteredArrowRightR } from './components/DropdownCenteredArrowRightR.vue';
export { default as DropdownCenteredArrowUpB } from './components/DropdownCenteredArrowUpB.vue';
export { default as DropdownCenteredArrowUpR } from './components/DropdownCenteredArrowUpR.vue';
export { default as DueDateBadge } from './components/DueDateBadge.vue';
export { default as DueDateS } from './components/DueDateS.vue';
export { default as EditB } from './components/EditB.vue';
export { default as EditF } from './components/EditF.vue';
export { default as EditR } from './components/EditR.vue';
export { default as EndDate } from './components/EndDate.vue';
export { default as EnterKey } from './components/EnterKey.vue';
export { default as ErrorB } from './components/ErrorB.vue';
export { default as ErrorF } from './components/ErrorF.vue';
export { default as ErrorR } from './components/ErrorR.vue';
export { default as Export } from './components/Export.vue';
export { default as ExternalLinkB } from './components/ExternalLinkB.vue';
export { default as ExternalLinkR } from './components/ExternalLinkR.vue';
export { default as FileB } from './components/FileB.vue';
export { default as FileF } from './components/FileF.vue';
export { default as FileMove } from './components/FileMove.vue';
export { default as FilePdf } from './components/FilePdf.vue';
export { default as FileR } from './components/FileR.vue';
export { default as FileRename } from './components/FileRename.vue';
export { default as FileRequest } from './components/FileRequest.vue';
export { default as FileUpload } from './components/FileUpload.vue';
export { default as FilterB } from './components/FilterB.vue';
export { default as FilterR } from './components/FilterR.vue';
export { default as FlowCover } from './components/FlowCover.vue';
export { default as FlowF } from './components/FlowF.vue';
export { default as FlowWorkspace } from './components/FlowWorkspace.vue';
export { default as Folder } from './components/Folder.vue';
export { default as FormItem } from './components/FormItem.vue';
export { default as FormSignature } from './components/FormSignature.vue';
export { default as FormTemplate } from './components/FormTemplate.vue';
export { default as Form } from './components/Form.vue';
export { default as FullscreenCollapseB } from './components/FullscreenCollapseB.vue';
export { default as FullscreenCollapseR } from './components/FullscreenCollapseR.vue';
export { default as FullscreenExpandB } from './components/FullscreenExpandB.vue';
export { default as FullscreenExpandR } from './components/FullscreenExpandR.vue';
export { default as GridViewB } from './components/GridViewB.vue';
export { default as GridViewR } from './components/GridViewR.vue';
export { default as GroupConversation } from './components/GroupConversation.vue';
export { default as Highlighter } from './components/Highlighter.vue';
export { default as Host } from './components/Host.vue';
export { default as Image } from './components/Image.vue';
export { default as InformationB } from './components/InformationB.vue';
export { default as InformationF } from './components/InformationF.vue';
export { default as InformationR } from './components/InformationR.vue';
export { default as Initials } from './components/Initials.vue';
export { default as InviteB } from './components/InviteB.vue';
export { default as InviteInternalUser } from './components/InviteInternalUser.vue';
export { default as InviteR } from './components/InviteR.vue';
export { default as LineB } from './components/LineB.vue';
export { default as LineR } from './components/LineR.vue';
export { default as LinkB } from './components/LinkB.vue';
export { default as LinkR } from './components/LinkR.vue';
export { default as ListViewB } from './components/ListViewB.vue';
export { default as ListViewR } from './components/ListViewR.vue';
export { default as LoadingB } from './components/LoadingB.vue';
export { default as LoadingR } from './components/LoadingR.vue';
export { default as LogOutB } from './components/LogOutB.vue';
export { default as LogOutR } from './components/LogOutR.vue';
export { default as LogicGroup } from './components/LogicGroup.vue';
export { default as Logic } from './components/Logic.vue';
export { default as Mail } from './components/Mail.vue';
export { default as MapPropertyB } from './components/MapPropertyB.vue';
export { default as MapPropertyF } from './components/MapPropertyF.vue';
export { default as MapPropertyR } from './components/MapPropertyR.vue';
export { default as MeetCanceled } from './components/MeetCanceled.vue';
export { default as MeetContentLibrary } from './components/MeetContentLibrary.vue';
export { default as MeetRecording } from './components/MeetRecording.vue';
export { default as MeetingR } from './components/MeetingR.vue';
export { default as MeetingRequest } from './components/MeetingRequest.vue';
export { default as MentionB } from './components/MentionB.vue';
export { default as MentionNavbar } from './components/MentionNavbar.vue';
export { default as MentionR } from './components/MentionR.vue';
export { default as MessageB } from './components/MessageB.vue';
export { default as MessageF } from './components/MessageF.vue';
export { default as MessageR } from './components/MessageR.vue';
export { default as MicMute } from './components/MicMute.vue';
export { default as Milestone } from './components/Milestone.vue';
export { default as MinusB } from './components/MinusB.vue';
export { default as MinusR } from './components/MinusR.vue';
export { default as MirrorHorizontalB } from './components/MirrorHorizontalB.vue';
export { default as MirrorHorizontalF } from './components/MirrorHorizontalF.vue';
export { default as MirrorHorizontalR } from './components/MirrorHorizontalR.vue';
export { default as MirrorVerticalB } from './components/MirrorVerticalB.vue';
export { default as MirrorVerticalF } from './components/MirrorVerticalF.vue';
export { default as MirrorVerticalR } from './components/MirrorVerticalR.vue';
export { default as Mobile } from './components/Mobile.vue';
export { default as MoreCircleR } from './components/MoreCircleR.vue';
export { default as MoreHorizontalB } from './components/MoreHorizontalB.vue';
export { default as MoreHorizontalR } from './components/MoreHorizontalR.vue';
export { default as MoreIntegrationsB } from './components/MoreIntegrationsB.vue';
export { default as MoreIntegrationsF } from './components/MoreIntegrationsF.vue';
export { default as MoreIntegrationsR } from './components/MoreIntegrationsR.vue';
export { default as MoreVerticalB } from './components/MoreVerticalB.vue';
export { default as MoreVerticalR } from './components/MoreVerticalR.vue';
export { default as Move } from './components/Move.vue';
export { default as MultiplePages } from './components/MultiplePages.vue';
export { default as NewB } from './components/NewB.vue';
export { default as NewFolder } from './components/NewFolder.vue';
export { default as NewR } from './components/NewR.vue';
export { default as Note } from './components/Note.vue';
export { default as OauthConnect } from './components/OauthConnect.vue';
export { default as OpenDrawerB } from './components/OpenDrawerB.vue';
export { default as OpenDrawerR } from './components/OpenDrawerR.vue';
export { default as Open } from './components/Open.vue';
export { default as Parallel } from './components/Parallel.vue';
export { default as PdfForm } from './components/PdfForm.vue';
export { default as Pen } from './components/Pen.vue';
export { default as PendingB } from './components/PendingB.vue';
export { default as PendingF } from './components/PendingF.vue';
export { default as PendingR } from './components/PendingR.vue';
export { default as PermissionsB } from './components/PermissionsB.vue';
export { default as PermissionsF } from './components/PermissionsF.vue';
export { default as PermissionsR } from './components/PermissionsR.vue';
export { default as Photo } from './components/Photo.vue';
export { default as PinB } from './components/PinB.vue';
export { default as PinF } from './components/PinF.vue';
export { default as PinR } from './components/PinR.vue';
export { default as PlayB } from './components/PlayB.vue';
export { default as PlayCircleB } from './components/PlayCircleB.vue';
export { default as PlayCircleF } from './components/PlayCircleF.vue';
export { default as PlayCircleR } from './components/PlayCircleR.vue';
export { default as PlayF } from './components/PlayF.vue';
export { default as PlayR } from './components/PlayR.vue';
export { default as PlusB } from './components/PlusB.vue';
export { default as PlusR } from './components/PlusR.vue';
export { default as PositionComment } from './components/PositionComment.vue';
export { default as Positive } from './components/Positive.vue';
export { default as Print } from './components/Print.vue';
export { default as ProfileB } from './components/ProfileB.vue';
export { default as ProfileF } from './components/ProfileF.vue';
export { default as ProfileR } from './components/ProfileR.vue';
export { default as PublicChannel } from './components/PublicChannel.vue';
export { default as QuestionMarkB } from './components/QuestionMarkB.vue';
export { default as QuestionMarkF } from './components/QuestionMarkF.vue';
export { default as QuestionMarkR } from './components/QuestionMarkR.vue';
export { default as QuoteLeft } from './components/QuoteLeft.vue';
export { default as Reaction } from './components/Reaction.vue';
export { default as ReactivateB } from './components/ReactivateB.vue';
export { default as ReactivateR } from './components/ReactivateR.vue';
export { default as ReadTick } from './components/ReadTick.vue';
export { default as Rectangle } from './components/Rectangle.vue';
export { default as Redo } from './components/Redo.vue';
export { default as RefreshB } from './components/RefreshB.vue';
export { default as RefreshR } from './components/RefreshR.vue';
export { default as ReminderS } from './components/ReminderS.vue';
export { default as Reminder } from './components/Reminder.vue';
export { default as RemoveAttachment } from './components/RemoveAttachment.vue';
export { default as Reopen } from './components/Reopen.vue';
export { default as Retry } from './components/Retry.vue';
export { default as RotateB } from './components/RotateB.vue';
export { default as RotateF } from './components/RotateF.vue';
export { default as RotateR } from './components/RotateR.vue';
export { default as Rubber } from './components/Rubber.vue';
export { default as SaveToCloudF } from './components/SaveToCloudF.vue';
export { default as SavedToCloudB } from './components/SavedToCloudB.vue';
export { default as SavedToCloudR } from './components/SavedToCloudR.vue';
export { default as SavingB } from './components/SavingB.vue';
export { default as SavingR } from './components/SavingR.vue';
export { default as ScheduleMeeting } from './components/ScheduleMeeting.vue';
export { default as ScrollToTop } from './components/ScrollToTop.vue';
export { default as SearchB } from './components/SearchB.vue';
export { default as SearchR } from './components/SearchR.vue';
export { default as SelectTool } from './components/SelectTool.vue';
export { default as Select } from './components/Select.vue';
export { default as SendB } from './components/SendB.vue';
export { default as SendR } from './components/SendR.vue';
export { default as SequentialB } from './components/SequentialB.vue';
export { default as SequentialR } from './components/SequentialR.vue';
export { default as SettingsB } from './components/SettingsB.vue';
export { default as SettingsF } from './components/SettingsF.vue';
export { default as SettingsR } from './components/SettingsR.vue';
export { default as ShapeRectangle } from './components/ShapeRectangle.vue';
export { default as ShareFileOff } from './components/ShareFileOff.vue';
export { default as SignFailure } from './components/SignFailure.vue';
export { default as SignR } from './components/SignR.vue';
export { default as SignSuccess } from './components/SignSuccess.vue';
export { default as SignatureB } from './components/SignatureB.vue';
export { default as SignatureR } from './components/SignatureR.vue';
export { default as SignerReassign } from './components/SignerReassign.vue';
export { default as Single_question_mark } from './components/Single_question_mark.vue';
export { default as Smile } from './components/Smile.vue';
export { default as SortB } from './components/SortB.vue';
export { default as SortDown } from './components/SortDown.vue';
export { default as SortR } from './components/SortR.vue';
export { default as SortUp } from './components/SortUp.vue';
export { default as StampSolid } from './components/StampSolid.vue';
export { default as Stamp } from './components/Stamp.vue';
export { default as StatusDueB } from './components/StatusDueB.vue';
export { default as StatusDueF } from './components/StatusDueF.vue';
export { default as StatusDueR } from './components/StatusDueR.vue';
export { default as StatusInProgressR } from './components/StatusInProgressR.vue';
export { default as StatusNewR } from './components/StatusNewR.vue';
export { default as Stop } from './components/Stop.vue';
export { default as SwitchB } from './components/SwitchB.vue';
export { default as SwitchR } from './components/SwitchR.vue';
export { default as Tag } from './components/Tag.vue';
export { default as Tape } from './components/Tape.vue';
export { default as TeamMember } from './components/TeamMember.vue';
export { default as Text } from './components/Text.vue';
export { default as ThumbnailViewBadge } from './components/ThumbnailViewBadge.vue';
export { default as Thumbnails } from './components/Thumbnails.vue';
export { default as Tick } from './components/Tick.vue';
export { default as Timezone } from './components/Timezone.vue';
export { default as ToDoCompleted } from './components/ToDoCompleted.vue';
export { default as ToDoReopened } from './components/ToDoReopened.vue';
export { default as TodoR } from './components/TodoR.vue';
export { default as Todo } from './components/Todo.vue';
export { default as ToggleOff } from './components/ToggleOff.vue';
export { default as ToggleOn } from './components/ToggleOn.vue';
export { default as Transaction } from './components/Transaction.vue';
export { default as TriggerF } from './components/TriggerF.vue';
export { default as UnarchiveF } from './components/UnarchiveF.vue';
export { default as Undo } from './components/Undo.vue';
export { default as Union } from './components/Union.vue';
export { default as UnlinkB } from './components/UnlinkB.vue';
export { default as UnlinkR } from './components/UnlinkR.vue';
export { default as UnlockB } from './components/UnlockB.vue';
export { default as UnlockF } from './components/UnlockF.vue';
export { default as UnlockR } from './components/UnlockR.vue';
export { default as UploadImageB } from './components/UploadImageB.vue';
export { default as UploadImageR } from './components/UploadImageR.vue';
export { default as UserB } from './components/UserB.vue';
export { default as UserF } from './components/UserF.vue';
export { default as UserGroupS } from './components/UserGroupS.vue';
export { default as UserR } from './components/UserR.vue';
export { default as UserWithArrow } from './components/UserWithArrow.vue';
export { default as VariableIcon } from './components/VariableIcon.vue';
export { default as ViewOriginal } from './components/ViewOriginal.vue';
export { default as VisibilityB } from './components/VisibilityB.vue';
export { default as VisibilityF } from './components/VisibilityF.vue';
export { default as VisibilityR } from './components/VisibilityR.vue';
export { default as Waiting } from './components/Waiting.vue';
export { default as WarningTriangleB } from './components/WarningTriangleB.vue';
export { default as WarningTriangleF } from './components/WarningTriangleF.vue';
export { default as WarningTriangleR } from './components/WarningTriangleR.vue';
export { default as Web } from './components/Web.vue';
export { default as WelcomeMessageB } from './components/WelcomeMessageB.vue';
export { default as WelcomeMessageF } from './components/WelcomeMessageF.vue';
export { default as WelcomeMessageR } from './components/WelcomeMessageR.vue';
export { default as Whiteboard } from './components/Whiteboard.vue';
export { default as WorkspaceB } from './components/WorkspaceB.vue';
export { default as WorkspaceF } from './components/WorkspaceF.vue';
export { default as WorkspaceR } from './components/WorkspaceR.vue';
export { default as ZoomIn } from './components/ZoomIn.vue';
export { default as ZoomOut } from './components/ZoomOut.vue';
export type MoxoIconName = 'Integration' | 'Invite-link' | 'Jumio' | 'Template' | 'accordion-down-f' | 'accordion-left-f' | 'accordion-right-f' | 'accordion-up-f' | 'acknowledgement-newly' | 'action-check' | 'action-files-r' | 'active-b' | 'active-r' | 'add-b' | 'add-r' | 'add-with-circle-b' | 'add-with-circle-f' | 'add-with-circle-r' | 'admin-user-b' | 'admin-user-r' | 'am-meeting' | 'annotation' | 'approval' | 'archive-b' | 'archive-f' | 'archive-r' | 'arrow-annotation' | 'arrow-large-down-b' | 'arrow-large-down-r' | 'arrow-large-up-b' | 'arrow-large-up-r' | 'arrow-left-b' | 'arrow-left-r' | 'arrow-right-b' | 'arrow-right-r' | 'assignee-r' | 'assistance' | 'attachment-b' | 'attachment-r' | 'automation' | 'bar-b' | 'bar-r' | 'bell-muted' | 'bell-outline' | 'bell' | 'binder' | 'bookmark-badge' | 'bookmark' | 'branch' | 'business-r' | 'calendar-b' | 'calendar-f' | 'calendar-r' | 'call-calendar-full' | 'cancel' | 'card' | 'cc-b' | 'cc-r' | 'check-account-b' | 'check-account-r' | 'check-b' | 'check-r' | 'circle-checkbox' | 'circle' | 'clock-b' | 'clock-f' | 'clock-r' | 'close-b' | 'close-drawer-b' | 'close-drawer-r' | 'close-r' | 'comment' | 'complete-2-b' | 'complete-2-r' | 'completed-b' | 'completed-by-others-b' | 'completed-by-others-r' | 'completed-by-you-b' | 'completed-by-you-r' | 'completed-f' | 'completed-r' | 'content-library-outline' | 'conversation-chat' | 'copy-b' | 'copy-r' | 'create-workspace-r' | 'ddr-text-r' | 'deactivate-b' | 'deactivate-f' | 'deactivate-r' | 'delete-b' | 'delete-f' | 'delete-feed' | 'delete-r' | 'dependency' | 'desktop-b' | 'desktop-r' | 'dial-pad' | 'distribution-list' | 'download-b' | 'download-r' | 'drag-handle-b' | 'drag-handle-r' | 'dropdown-centered-arrow-down-b' | 'dropdown-centered-arrow-down-r' | 'dropdown-centered-arrow-left-b' | 'dropdown-centered-arrow-left-r' | 'dropdown-centered-arrow-right-b' | 'dropdown-centered-arrow-right-r' | 'dropdown-centered-arrow-up-b' | 'dropdown-centered-arrow-up-r' | 'due-date-badge' | 'due-date-s' | 'edit-b' | 'edit-f' | 'edit-r' | 'end-date' | 'enter-key' | 'error-b' | 'error-f' | 'error-r' | 'export' | 'external-link-b' | 'external-link-r' | 'file-b' | 'file-f' | 'file-move' | 'file-pdf' | 'file-r' | 'file-rename' | 'file-request' | 'file-upload' | 'filter-b' | 'filter-r' | 'flow-cover' | 'flow-f' | 'flow-workspace' | 'folder' | 'form-item' | 'form-signature' | 'form-template' | 'form' | 'fullscreen-collapse-b' | 'fullscreen-collapse-r' | 'fullscreen-expand-b' | 'fullscreen-expand-r' | 'grid-view-b' | 'grid-view-r' | 'group-conversation' | 'highlighter' | 'host' | 'image' | 'information-b' | 'information-f' | 'information-r' | 'initials' | 'invite-b' | 'invite-internal-user' | 'invite-r' | 'line-b' | 'line-r' | 'link-b' | 'link-r' | 'list-view-b' | 'list-view-r' | 'loading-b' | 'loading-r' | 'log-out-b' | 'log-out-r' | 'logic-group' | 'logic' | 'mail' | 'map-property-b' | 'map-property-f' | 'map-property-r' | 'meet-canceled' | 'meet-content-library' | 'meet-recording' | 'meeting-r' | 'meeting-request' | 'mention-b' | 'mention-navbar' | 'mention-r' | 'message-b' | 'message-f' | 'message-r' | 'mic-mute' | 'milestone' | 'minus-b' | 'minus-r' | 'mirror-horizontal-b' | 'mirror-horizontal-f' | 'mirror-horizontal-r' | 'mirror-vertical-b' | 'mirror-vertical-f' | 'mirror-vertical-r' | 'mobile' | 'more-circle-r' | 'more-horizontal-b' | 'more-horizontal-r' | 'more-integrations-b' | 'more-integrations-f' | 'more-integrations-r' | 'more-vertical-b' | 'more-vertical-r' | 'move' | 'multiple-pages' | 'new-b' | 'new-folder' | 'new-r' | 'note' | 'oauth-connect' | 'open-drawer-b' | 'open-drawer-r' | 'open' | 'parallel' | 'pdf-form' | 'pen' | 'pending-b' | 'pending-f' | 'pending-r' | 'permissions-b' | 'permissions-f' | 'permissions-r' | 'photo' | 'pin-b' | 'pin-f' | 'pin-r' | 'play-b' | 'play-circle-b' | 'play-circle-f' | 'play-circle-r' | 'play-f' | 'play-r' | 'plus-b' | 'plus-r' | 'position-comment' | 'positive' | 'print' | 'profile-b' | 'profile-f' | 'profile-r' | 'public-channel' | 'question-mark-b' | 'question-mark-f' | 'question-mark-r' | 'quote-left' | 'reaction' | 'reactivate-b' | 'reactivate-r' | 'read-tick' | 'rectangle' | 'redo' | 'refresh-b' | 'refresh-r' | 'reminder-s' | 'reminder' | 'remove-attachment' | 'reopen' | 'retry' | 'rotate-b' | 'rotate-f' | 'rotate-r' | 'rubber' | 'save-to-cloud-f' | 'saved-to-cloud-b' | 'saved-to-cloud-r' | 'saving-b' | 'saving-r' | 'schedule-meeting' | 'scroll-to-top' | 'search-b' | 'search-r' | 'select-tool' | 'select' | 'send-b' | 'send-r' | 'sequential-b' | 'sequential-r' | 'settings-b' | 'settings-f' | 'settings-r' | 'shape-rectangle' | 'share-file-off' | 'sign-failure' | 'sign-r' | 'sign-success' | 'signature-b' | 'signature-r' | 'signer-reassign' | 'single_question_mark' | 'smile' | 'sort-b' | 'sort-down' | 'sort-r' | 'sort-up' | 'stamp-solid' | 'stamp' | 'status-due-b' | 'status-due-f' | 'status-due-r' | 'status-in-progress-r' | 'status-new-r' | 'stop' | 'switch-b' | 'switch-r' | 'tag' | 'tape' | 'team-member' | 'text' | 'thumbnail-view-badge' | 'thumbnails' | 'tick' | 'timezone' | 'to-do-completed' | 'to-do-reopened' | 'todo-r' | 'todo' | 'toggle-off' | 'toggle-on' | 'transaction' | 'trigger-f' | 'unarchive-f' | 'undo' | 'union' | 'unlink-b' | 'unlink-r' | 'unlock-b' | 'unlock-f' | 'unlock-r' | 'upload-image-b' | 'upload-image-r' | 'user-b' | 'user-f' | 'user-group-s' | 'user-r' | 'user-with-arrow' | 'variable-icon' | 'view-original' | 'visibility-b' | 'visibility-f' | 'visibility-r' | 'waiting' | 'warning-triangle-b' | 'warning-triangle-f' | 'warning-triangle-r' | 'web' | 'welcome-message-b' | 'welcome-message-f' | 'welcome-message-r' | 'whiteboard' | 'workspace-b' | 'workspace-f' | 'workspace-r' | 'zoom-in' | 'zoom-out';
export declare const iconNames: MoxoIconName[];
export declare const iconComponents: {
    readonly Integration: () => Promise<typeof import("*.vue")>;
    readonly 'Invite-link': () => Promise<typeof import("*.vue")>;
    readonly Jumio: () => Promise<typeof import("*.vue")>;
    readonly Template: () => Promise<typeof import("*.vue")>;
    readonly 'accordion-down-f': () => Promise<typeof import("*.vue")>;
    readonly 'accordion-left-f': () => Promise<typeof import("*.vue")>;
    readonly 'accordion-right-f': () => Promise<typeof import("*.vue")>;
    readonly 'accordion-up-f': () => Promise<typeof import("*.vue")>;
    readonly 'acknowledgement-newly': () => Promise<typeof import("*.vue")>;
    readonly 'action-check': () => Promise<typeof import("*.vue")>;
    readonly 'action-files-r': () => Promise<typeof import("*.vue")>;
    readonly 'active-b': () => Promise<typeof import("*.vue")>;
    readonly 'active-r': () => Promise<typeof import("*.vue")>;
    readonly 'add-b': () => Promise<typeof import("*.vue")>;
    readonly 'add-r': () => Promise<typeof import("*.vue")>;
    readonly 'add-with-circle-b': () => Promise<typeof import("*.vue")>;
    readonly 'add-with-circle-f': () => Promise<typeof import("*.vue")>;
    readonly 'add-with-circle-r': () => Promise<typeof import("*.vue")>;
    readonly 'admin-user-b': () => Promise<typeof import("*.vue")>;
    readonly 'admin-user-r': () => Promise<typeof import("*.vue")>;
    readonly 'am-meeting': () => Promise<typeof import("*.vue")>;
    readonly annotation: () => Promise<typeof import("*.vue")>;
    readonly approval: () => Promise<typeof import("*.vue")>;
    readonly 'archive-b': () => Promise<typeof import("*.vue")>;
    readonly 'archive-f': () => Promise<typeof import("*.vue")>;
    readonly 'archive-r': () => Promise<typeof import("*.vue")>;
    readonly 'arrow-annotation': () => Promise<typeof import("*.vue")>;
    readonly 'arrow-large-down-b': () => Promise<typeof import("*.vue")>;
    readonly 'arrow-large-down-r': () => Promise<typeof import("*.vue")>;
    readonly 'arrow-large-up-b': () => Promise<typeof import("*.vue")>;
    readonly 'arrow-large-up-r': () => Promise<typeof import("*.vue")>;
    readonly 'arrow-left-b': () => Promise<typeof import("*.vue")>;
    readonly 'arrow-left-r': () => Promise<typeof import("*.vue")>;
    readonly 'arrow-right-b': () => Promise<typeof import("*.vue")>;
    readonly 'arrow-right-r': () => Promise<typeof import("*.vue")>;
    readonly 'assignee-r': () => Promise<typeof import("*.vue")>;
    readonly assistance: () => Promise<typeof import("*.vue")>;
    readonly 'attachment-b': () => Promise<typeof import("*.vue")>;
    readonly 'attachment-r': () => Promise<typeof import("*.vue")>;
    readonly automation: () => Promise<typeof import("*.vue")>;
    readonly 'bar-b': () => Promise<typeof import("*.vue")>;
    readonly 'bar-r': () => Promise<typeof import("*.vue")>;
    readonly 'bell-muted': () => Promise<typeof import("*.vue")>;
    readonly 'bell-outline': () => Promise<typeof import("*.vue")>;
    readonly bell: () => Promise<typeof import("*.vue")>;
    readonly binder: () => Promise<typeof import("*.vue")>;
    readonly 'bookmark-badge': () => Promise<typeof import("*.vue")>;
    readonly bookmark: () => Promise<typeof import("*.vue")>;
    readonly branch: () => Promise<typeof import("*.vue")>;
    readonly 'business-r': () => Promise<typeof import("*.vue")>;
    readonly 'calendar-b': () => Promise<typeof import("*.vue")>;
    readonly 'calendar-f': () => Promise<typeof import("*.vue")>;
    readonly 'calendar-r': () => Promise<typeof import("*.vue")>;
    readonly 'call-calendar-full': () => Promise<typeof import("*.vue")>;
    readonly cancel: () => Promise<typeof import("*.vue")>;
    readonly card: () => Promise<typeof import("*.vue")>;
    readonly 'cc-b': () => Promise<typeof import("*.vue")>;
    readonly 'cc-r': () => Promise<typeof import("*.vue")>;
    readonly 'check-account-b': () => Promise<typeof import("*.vue")>;
    readonly 'check-account-r': () => Promise<typeof import("*.vue")>;
    readonly 'check-b': () => Promise<typeof import("*.vue")>;
    readonly 'check-r': () => Promise<typeof import("*.vue")>;
    readonly 'circle-checkbox': () => Promise<typeof import("*.vue")>;
    readonly circle: () => Promise<typeof import("*.vue")>;
    readonly 'clock-b': () => Promise<typeof import("*.vue")>;
    readonly 'clock-f': () => Promise<typeof import("*.vue")>;
    readonly 'clock-r': () => Promise<typeof import("*.vue")>;
    readonly 'close-b': () => Promise<typeof import("*.vue")>;
    readonly 'close-drawer-b': () => Promise<typeof import("*.vue")>;
    readonly 'close-drawer-r': () => Promise<typeof import("*.vue")>;
    readonly 'close-r': () => Promise<typeof import("*.vue")>;
    readonly comment: () => Promise<typeof import("*.vue")>;
    readonly 'complete-2-b': () => Promise<typeof import("*.vue")>;
    readonly 'complete-2-r': () => Promise<typeof import("*.vue")>;
    readonly 'completed-b': () => Promise<typeof import("*.vue")>;
    readonly 'completed-by-others-b': () => Promise<typeof import("*.vue")>;
    readonly 'completed-by-others-r': () => Promise<typeof import("*.vue")>;
    readonly 'completed-by-you-b': () => Promise<typeof import("*.vue")>;
    readonly 'completed-by-you-r': () => Promise<typeof import("*.vue")>;
    readonly 'completed-f': () => Promise<typeof import("*.vue")>;
    readonly 'completed-r': () => Promise<typeof import("*.vue")>;
    readonly 'content-library-outline': () => Promise<typeof import("*.vue")>;
    readonly 'conversation-chat': () => Promise<typeof import("*.vue")>;
    readonly 'copy-b': () => Promise<typeof import("*.vue")>;
    readonly 'copy-r': () => Promise<typeof import("*.vue")>;
    readonly 'create-workspace-r': () => Promise<typeof import("*.vue")>;
    readonly 'ddr-text-r': () => Promise<typeof import("*.vue")>;
    readonly 'deactivate-b': () => Promise<typeof import("*.vue")>;
    readonly 'deactivate-f': () => Promise<typeof import("*.vue")>;
    readonly 'deactivate-r': () => Promise<typeof import("*.vue")>;
    readonly 'delete-b': () => Promise<typeof import("*.vue")>;
    readonly 'delete-f': () => Promise<typeof import("*.vue")>;
    readonly 'delete-feed': () => Promise<typeof import("*.vue")>;
    readonly 'delete-r': () => Promise<typeof import("*.vue")>;
    readonly dependency: () => Promise<typeof import("*.vue")>;
    readonly 'desktop-b': () => Promise<typeof import("*.vue")>;
    readonly 'desktop-r': () => Promise<typeof import("*.vue")>;
    readonly 'dial-pad': () => Promise<typeof import("*.vue")>;
    readonly 'distribution-list': () => Promise<typeof import("*.vue")>;
    readonly 'download-b': () => Promise<typeof import("*.vue")>;
    readonly 'download-r': () => Promise<typeof import("*.vue")>;
    readonly 'drag-handle-b': () => Promise<typeof import("*.vue")>;
    readonly 'drag-handle-r': () => Promise<typeof import("*.vue")>;
    readonly 'dropdown-centered-arrow-down-b': () => Promise<typeof import("*.vue")>;
    readonly 'dropdown-centered-arrow-down-r': () => Promise<typeof import("*.vue")>;
    readonly 'dropdown-centered-arrow-left-b': () => Promise<typeof import("*.vue")>;
    readonly 'dropdown-centered-arrow-left-r': () => Promise<typeof import("*.vue")>;
    readonly 'dropdown-centered-arrow-right-b': () => Promise<typeof import("*.vue")>;
    readonly 'dropdown-centered-arrow-right-r': () => Promise<typeof import("*.vue")>;
    readonly 'dropdown-centered-arrow-up-b': () => Promise<typeof import("*.vue")>;
    readonly 'dropdown-centered-arrow-up-r': () => Promise<typeof import("*.vue")>;
    readonly 'due-date-badge': () => Promise<typeof import("*.vue")>;
    readonly 'due-date-s': () => Promise<typeof import("*.vue")>;
    readonly 'edit-b': () => Promise<typeof import("*.vue")>;
    readonly 'edit-f': () => Promise<typeof import("*.vue")>;
    readonly 'edit-r': () => Promise<typeof import("*.vue")>;
    readonly 'end-date': () => Promise<typeof import("*.vue")>;
    readonly 'enter-key': () => Promise<typeof import("*.vue")>;
    readonly 'error-b': () => Promise<typeof import("*.vue")>;
    readonly 'error-f': () => Promise<typeof import("*.vue")>;
    readonly 'error-r': () => Promise<typeof import("*.vue")>;
    readonly export: () => Promise<typeof import("*.vue")>;
    readonly 'external-link-b': () => Promise<typeof import("*.vue")>;
    readonly 'external-link-r': () => Promise<typeof import("*.vue")>;
    readonly 'file-b': () => Promise<typeof import("*.vue")>;
    readonly 'file-f': () => Promise<typeof import("*.vue")>;
    readonly 'file-move': () => Promise<typeof import("*.vue")>;
    readonly 'file-pdf': () => Promise<typeof import("*.vue")>;
    readonly 'file-r': () => Promise<typeof import("*.vue")>;
    readonly 'file-rename': () => Promise<typeof import("*.vue")>;
    readonly 'file-request': () => Promise<typeof import("*.vue")>;
    readonly 'file-upload': () => Promise<typeof import("*.vue")>;
    readonly 'filter-b': () => Promise<typeof import("*.vue")>;
    readonly 'filter-r': () => Promise<typeof import("*.vue")>;
    readonly 'flow-cover': () => Promise<typeof import("*.vue")>;
    readonly 'flow-f': () => Promise<typeof import("*.vue")>;
    readonly 'flow-workspace': () => Promise<typeof import("*.vue")>;
    readonly folder: () => Promise<typeof import("*.vue")>;
    readonly 'form-item': () => Promise<typeof import("*.vue")>;
    readonly 'form-signature': () => Promise<typeof import("*.vue")>;
    readonly 'form-template': () => Promise<typeof import("*.vue")>;
    readonly form: () => Promise<typeof import("*.vue")>;
    readonly 'fullscreen-collapse-b': () => Promise<typeof import("*.vue")>;
    readonly 'fullscreen-collapse-r': () => Promise<typeof import("*.vue")>;
    readonly 'fullscreen-expand-b': () => Promise<typeof import("*.vue")>;
    readonly 'fullscreen-expand-r': () => Promise<typeof import("*.vue")>;
    readonly 'grid-view-b': () => Promise<typeof import("*.vue")>;
    readonly 'grid-view-r': () => Promise<typeof import("*.vue")>;
    readonly 'group-conversation': () => Promise<typeof import("*.vue")>;
    readonly highlighter: () => Promise<typeof import("*.vue")>;
    readonly host: () => Promise<typeof import("*.vue")>;
    readonly image: () => Promise<typeof import("*.vue")>;
    readonly 'information-b': () => Promise<typeof import("*.vue")>;
    readonly 'information-f': () => Promise<typeof import("*.vue")>;
    readonly 'information-r': () => Promise<typeof import("*.vue")>;
    readonly initials: () => Promise<typeof import("*.vue")>;
    readonly 'invite-b': () => Promise<typeof import("*.vue")>;
    readonly 'invite-internal-user': () => Promise<typeof import("*.vue")>;
    readonly 'invite-r': () => Promise<typeof import("*.vue")>;
    readonly 'line-b': () => Promise<typeof import("*.vue")>;
    readonly 'line-r': () => Promise<typeof import("*.vue")>;
    readonly 'link-b': () => Promise<typeof import("*.vue")>;
    readonly 'link-r': () => Promise<typeof import("*.vue")>;
    readonly 'list-view-b': () => Promise<typeof import("*.vue")>;
    readonly 'list-view-r': () => Promise<typeof import("*.vue")>;
    readonly 'loading-b': () => Promise<typeof import("*.vue")>;
    readonly 'loading-r': () => Promise<typeof import("*.vue")>;
    readonly 'log-out-b': () => Promise<typeof import("*.vue")>;
    readonly 'log-out-r': () => Promise<typeof import("*.vue")>;
    readonly 'logic-group': () => Promise<typeof import("*.vue")>;
    readonly logic: () => Promise<typeof import("*.vue")>;
    readonly mail: () => Promise<typeof import("*.vue")>;
    readonly 'map-property-b': () => Promise<typeof import("*.vue")>;
    readonly 'map-property-f': () => Promise<typeof import("*.vue")>;
    readonly 'map-property-r': () => Promise<typeof import("*.vue")>;
    readonly 'meet-canceled': () => Promise<typeof import("*.vue")>;
    readonly 'meet-content-library': () => Promise<typeof import("*.vue")>;
    readonly 'meet-recording': () => Promise<typeof import("*.vue")>;
    readonly 'meeting-r': () => Promise<typeof import("*.vue")>;
    readonly 'meeting-request': () => Promise<typeof import("*.vue")>;
    readonly 'mention-b': () => Promise<typeof import("*.vue")>;
    readonly 'mention-navbar': () => Promise<typeof import("*.vue")>;
    readonly 'mention-r': () => Promise<typeof import("*.vue")>;
    readonly 'message-b': () => Promise<typeof import("*.vue")>;
    readonly 'message-f': () => Promise<typeof import("*.vue")>;
    readonly 'message-r': () => Promise<typeof import("*.vue")>;
    readonly 'mic-mute': () => Promise<typeof import("*.vue")>;
    readonly milestone: () => Promise<typeof import("*.vue")>;
    readonly 'minus-b': () => Promise<typeof import("*.vue")>;
    readonly 'minus-r': () => Promise<typeof import("*.vue")>;
    readonly 'mirror-horizontal-b': () => Promise<typeof import("*.vue")>;
    readonly 'mirror-horizontal-f': () => Promise<typeof import("*.vue")>;
    readonly 'mirror-horizontal-r': () => Promise<typeof import("*.vue")>;
    readonly 'mirror-vertical-b': () => Promise<typeof import("*.vue")>;
    readonly 'mirror-vertical-f': () => Promise<typeof import("*.vue")>;
    readonly 'mirror-vertical-r': () => Promise<typeof import("*.vue")>;
    readonly mobile: () => Promise<typeof import("*.vue")>;
    readonly 'more-circle-r': () => Promise<typeof import("*.vue")>;
    readonly 'more-horizontal-b': () => Promise<typeof import("*.vue")>;
    readonly 'more-horizontal-r': () => Promise<typeof import("*.vue")>;
    readonly 'more-integrations-b': () => Promise<typeof import("*.vue")>;
    readonly 'more-integrations-f': () => Promise<typeof import("*.vue")>;
    readonly 'more-integrations-r': () => Promise<typeof import("*.vue")>;
    readonly 'more-vertical-b': () => Promise<typeof import("*.vue")>;
    readonly 'more-vertical-r': () => Promise<typeof import("*.vue")>;
    readonly move: () => Promise<typeof import("*.vue")>;
    readonly 'multiple-pages': () => Promise<typeof import("*.vue")>;
    readonly 'new-b': () => Promise<typeof import("*.vue")>;
    readonly 'new-folder': () => Promise<typeof import("*.vue")>;
    readonly 'new-r': () => Promise<typeof import("*.vue")>;
    readonly note: () => Promise<typeof import("*.vue")>;
    readonly 'oauth-connect': () => Promise<typeof import("*.vue")>;
    readonly 'open-drawer-b': () => Promise<typeof import("*.vue")>;
    readonly 'open-drawer-r': () => Promise<typeof import("*.vue")>;
    readonly open: () => Promise<typeof import("*.vue")>;
    readonly parallel: () => Promise<typeof import("*.vue")>;
    readonly 'pdf-form': () => Promise<typeof import("*.vue")>;
    readonly pen: () => Promise<typeof import("*.vue")>;
    readonly 'pending-b': () => Promise<typeof import("*.vue")>;
    readonly 'pending-f': () => Promise<typeof import("*.vue")>;
    readonly 'pending-r': () => Promise<typeof import("*.vue")>;
    readonly 'permissions-b': () => Promise<typeof import("*.vue")>;
    readonly 'permissions-f': () => Promise<typeof import("*.vue")>;
    readonly 'permissions-r': () => Promise<typeof import("*.vue")>;
    readonly photo: () => Promise<typeof import("*.vue")>;
    readonly 'pin-b': () => Promise<typeof import("*.vue")>;
    readonly 'pin-f': () => Promise<typeof import("*.vue")>;
    readonly 'pin-r': () => Promise<typeof import("*.vue")>;
    readonly 'play-b': () => Promise<typeof import("*.vue")>;
    readonly 'play-circle-b': () => Promise<typeof import("*.vue")>;
    readonly 'play-circle-f': () => Promise<typeof import("*.vue")>;
    readonly 'play-circle-r': () => Promise<typeof import("*.vue")>;
    readonly 'play-f': () => Promise<typeof import("*.vue")>;
    readonly 'play-r': () => Promise<typeof import("*.vue")>;
    readonly 'plus-b': () => Promise<typeof import("*.vue")>;
    readonly 'plus-r': () => Promise<typeof import("*.vue")>;
    readonly 'position-comment': () => Promise<typeof import("*.vue")>;
    readonly positive: () => Promise<typeof import("*.vue")>;
    readonly print: () => Promise<typeof import("*.vue")>;
    readonly 'profile-b': () => Promise<typeof import("*.vue")>;
    readonly 'profile-f': () => Promise<typeof import("*.vue")>;
    readonly 'profile-r': () => Promise<typeof import("*.vue")>;
    readonly 'public-channel': () => Promise<typeof import("*.vue")>;
    readonly 'question-mark-b': () => Promise<typeof import("*.vue")>;
    readonly 'question-mark-f': () => Promise<typeof import("*.vue")>;
    readonly 'question-mark-r': () => Promise<typeof import("*.vue")>;
    readonly 'quote-left': () => Promise<typeof import("*.vue")>;
    readonly reaction: () => Promise<typeof import("*.vue")>;
    readonly 'reactivate-b': () => Promise<typeof import("*.vue")>;
    readonly 'reactivate-r': () => Promise<typeof import("*.vue")>;
    readonly 'read-tick': () => Promise<typeof import("*.vue")>;
    readonly rectangle: () => Promise<typeof import("*.vue")>;
    readonly redo: () => Promise<typeof import("*.vue")>;
    readonly 'refresh-b': () => Promise<typeof import("*.vue")>;
    readonly 'refresh-r': () => Promise<typeof import("*.vue")>;
    readonly 'reminder-s': () => Promise<typeof import("*.vue")>;
    readonly reminder: () => Promise<typeof import("*.vue")>;
    readonly 'remove-attachment': () => Promise<typeof import("*.vue")>;
    readonly reopen: () => Promise<typeof import("*.vue")>;
    readonly retry: () => Promise<typeof import("*.vue")>;
    readonly 'rotate-b': () => Promise<typeof import("*.vue")>;
    readonly 'rotate-f': () => Promise<typeof import("*.vue")>;
    readonly 'rotate-r': () => Promise<typeof import("*.vue")>;
    readonly rubber: () => Promise<typeof import("*.vue")>;
    readonly 'save-to-cloud-f': () => Promise<typeof import("*.vue")>;
    readonly 'saved-to-cloud-b': () => Promise<typeof import("*.vue")>;
    readonly 'saved-to-cloud-r': () => Promise<typeof import("*.vue")>;
    readonly 'saving-b': () => Promise<typeof import("*.vue")>;
    readonly 'saving-r': () => Promise<typeof import("*.vue")>;
    readonly 'schedule-meeting': () => Promise<typeof import("*.vue")>;
    readonly 'scroll-to-top': () => Promise<typeof import("*.vue")>;
    readonly 'search-b': () => Promise<typeof import("*.vue")>;
    readonly 'search-r': () => Promise<typeof import("*.vue")>;
    readonly 'select-tool': () => Promise<typeof import("*.vue")>;
    readonly select: () => Promise<typeof import("*.vue")>;
    readonly 'send-b': () => Promise<typeof import("*.vue")>;
    readonly 'send-r': () => Promise<typeof import("*.vue")>;
    readonly 'sequential-b': () => Promise<typeof import("*.vue")>;
    readonly 'sequential-r': () => Promise<typeof import("*.vue")>;
    readonly 'settings-b': () => Promise<typeof import("*.vue")>;
    readonly 'settings-f': () => Promise<typeof import("*.vue")>;
    readonly 'settings-r': () => Promise<typeof import("*.vue")>;
    readonly 'shape-rectangle': () => Promise<typeof import("*.vue")>;
    readonly 'share-file-off': () => Promise<typeof import("*.vue")>;
    readonly 'sign-failure': () => Promise<typeof import("*.vue")>;
    readonly 'sign-r': () => Promise<typeof import("*.vue")>;
    readonly 'sign-success': () => Promise<typeof import("*.vue")>;
    readonly 'signature-b': () => Promise<typeof import("*.vue")>;
    readonly 'signature-r': () => Promise<typeof import("*.vue")>;
    readonly 'signer-reassign': () => Promise<typeof import("*.vue")>;
    readonly single_question_mark: () => Promise<typeof import("*.vue")>;
    readonly smile: () => Promise<typeof import("*.vue")>;
    readonly 'sort-b': () => Promise<typeof import("*.vue")>;
    readonly 'sort-down': () => Promise<typeof import("*.vue")>;
    readonly 'sort-r': () => Promise<typeof import("*.vue")>;
    readonly 'sort-up': () => Promise<typeof import("*.vue")>;
    readonly 'stamp-solid': () => Promise<typeof import("*.vue")>;
    readonly stamp: () => Promise<typeof import("*.vue")>;
    readonly 'status-due-b': () => Promise<typeof import("*.vue")>;
    readonly 'status-due-f': () => Promise<typeof import("*.vue")>;
    readonly 'status-due-r': () => Promise<typeof import("*.vue")>;
    readonly 'status-in-progress-r': () => Promise<typeof import("*.vue")>;
    readonly 'status-new-r': () => Promise<typeof import("*.vue")>;
    readonly stop: () => Promise<typeof import("*.vue")>;
    readonly 'switch-b': () => Promise<typeof import("*.vue")>;
    readonly 'switch-r': () => Promise<typeof import("*.vue")>;
    readonly tag: () => Promise<typeof import("*.vue")>;
    readonly tape: () => Promise<typeof import("*.vue")>;
    readonly 'team-member': () => Promise<typeof import("*.vue")>;
    readonly text: () => Promise<typeof import("*.vue")>;
    readonly 'thumbnail-view-badge': () => Promise<typeof import("*.vue")>;
    readonly thumbnails: () => Promise<typeof import("*.vue")>;
    readonly tick: () => Promise<typeof import("*.vue")>;
    readonly timezone: () => Promise<typeof import("*.vue")>;
    readonly 'to-do-completed': () => Promise<typeof import("*.vue")>;
    readonly 'to-do-reopened': () => Promise<typeof import("*.vue")>;
    readonly 'todo-r': () => Promise<typeof import("*.vue")>;
    readonly todo: () => Promise<typeof import("*.vue")>;
    readonly 'toggle-off': () => Promise<typeof import("*.vue")>;
    readonly 'toggle-on': () => Promise<typeof import("*.vue")>;
    readonly transaction: () => Promise<typeof import("*.vue")>;
    readonly 'trigger-f': () => Promise<typeof import("*.vue")>;
    readonly 'unarchive-f': () => Promise<typeof import("*.vue")>;
    readonly undo: () => Promise<typeof import("*.vue")>;
    readonly union: () => Promise<typeof import("*.vue")>;
    readonly 'unlink-b': () => Promise<typeof import("*.vue")>;
    readonly 'unlink-r': () => Promise<typeof import("*.vue")>;
    readonly 'unlock-b': () => Promise<typeof import("*.vue")>;
    readonly 'unlock-f': () => Promise<typeof import("*.vue")>;
    readonly 'unlock-r': () => Promise<typeof import("*.vue")>;
    readonly 'upload-image-b': () => Promise<typeof import("*.vue")>;
    readonly 'upload-image-r': () => Promise<typeof import("*.vue")>;
    readonly 'user-b': () => Promise<typeof import("*.vue")>;
    readonly 'user-f': () => Promise<typeof import("*.vue")>;
    readonly 'user-group-s': () => Promise<typeof import("*.vue")>;
    readonly 'user-r': () => Promise<typeof import("*.vue")>;
    readonly 'user-with-arrow': () => Promise<typeof import("*.vue")>;
    readonly 'variable-icon': () => Promise<typeof import("*.vue")>;
    readonly 'view-original': () => Promise<typeof import("*.vue")>;
    readonly 'visibility-b': () => Promise<typeof import("*.vue")>;
    readonly 'visibility-f': () => Promise<typeof import("*.vue")>;
    readonly 'visibility-r': () => Promise<typeof import("*.vue")>;
    readonly waiting: () => Promise<typeof import("*.vue")>;
    readonly 'warning-triangle-b': () => Promise<typeof import("*.vue")>;
    readonly 'warning-triangle-f': () => Promise<typeof import("*.vue")>;
    readonly 'warning-triangle-r': () => Promise<typeof import("*.vue")>;
    readonly web: () => Promise<typeof import("*.vue")>;
    readonly 'welcome-message-b': () => Promise<typeof import("*.vue")>;
    readonly 'welcome-message-f': () => Promise<typeof import("*.vue")>;
    readonly 'welcome-message-r': () => Promise<typeof import("*.vue")>;
    readonly whiteboard: () => Promise<typeof import("*.vue")>;
    readonly 'workspace-b': () => Promise<typeof import("*.vue")>;
    readonly 'workspace-f': () => Promise<typeof import("*.vue")>;
    readonly 'workspace-r': () => Promise<typeof import("*.vue")>;
    readonly 'zoom-in': () => Promise<typeof import("*.vue")>;
    readonly 'zoom-out': () => Promise<typeof import("*.vue")>;
};
//# sourceMappingURL=generated.d.ts.map