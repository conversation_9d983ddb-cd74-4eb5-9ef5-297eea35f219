import type { IconComponent, IconRegistry } from './types';
/**
 * 图标注册表实现
 */
declare class IconRegistryImpl implements IconRegistry {
    private icons;
    register(name: string, component: IconComponent): void;
    get(name: string): IconComponent | undefined;
    getNames(): string[];
    has(name: string): boolean;
    clear(): void;
}
export declare const iconRegistry: IconRegistryImpl;
export type { IconRegistry } from './types';
export declare function registerIcons(icons: Record<string, IconComponent>): void;
export declare function loadAndRegisterIcon(name: string): Promise<IconComponent | undefined>;
//# sourceMappingURL=registry.d.ts.map