<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Moxo Icons 测试页面</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      padding: 30px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 40px;
    }
    
    .section {
      margin-bottom: 40px;
    }
    
    .section h2 {
      color: #666;
      border-bottom: 2px solid #eee;
      padding-bottom: 10px;
      margin-bottom: 20px;
    }
    
    .icon-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }
    
    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;
      border: 1px solid #eee;
      border-radius: 6px;
      transition: all 0.2s;
    }
    
    .icon-item:hover {
      border-color: #007bff;
      box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
    }
    
    .icon-display {
      margin-bottom: 10px;
    }
    
    .icon-name {
      font-size: 14px;
      color: #666;
      text-align: center;
      word-break: break-all;
    }
    
    .size-demo {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 15px;
    }
    
    .color-demo {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 15px;
    }
    
    .demo-label {
      min-width: 80px;
      font-weight: 500;
      color: #333;
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="container">
      <h1>🎨 Moxo Icons 图标库</h1>
      
      <div class="section">
        <h2>📦 所有图标</h2>
        <div class="icon-grid">
          <div v-for="iconName in iconNames" :key="iconName" class="icon-item">
            <div class="icon-display">
              <Icon :name="iconName" :size="32" />
            </div>
            <div class="icon-name">{{ iconName }}</div>
          </div>
        </div>
      </div>
      
      <div class="section">
        <h2>📏 尺寸演示</h2>
        <div class="size-demo">
          <span class="demo-label">16px:</span>
          <Icon name="accordion-left-centered" :size="16" />
        </div>
        <div class="size-demo">
          <span class="demo-label">24px:</span>
          <Icon name="accordion-left-centered" :size="24" />
        </div>
        <div class="size-demo">
          <span class="demo-label">32px:</span>
          <Icon name="accordion-left-centered" :size="32" />
        </div>
        <div class="size-demo">
          <span class="demo-label">48px:</span>
          <Icon name="accordion-left-centered" :size="48" />
        </div>
      </div>
      
      <div class="section">
        <h2>🎨 颜色演示</h2>
        <div class="color-demo">
          <span class="demo-label">默认:</span>
          <Icon name="accordion-right-centered" :size="24" />
        </div>
        <div class="color-demo">
          <span class="demo-label">红色:</span>
          <Icon name="accordion-right-centered" :size="24" color="#ff4757" />
        </div>
        <div class="color-demo">
          <span class="demo-label">蓝色:</span>
          <Icon name="accordion-right-centered" :size="24" color="#3742fa" />
        </div>
        <div class="color-demo">
          <span class="demo-label">绿色:</span>
          <Icon name="accordion-right-centered" :size="24" color="#2ed573" />
        </div>
      </div>
    </div>
  </div>

  <script type="module" src="./main.ts"></script>
</body>
</html>
