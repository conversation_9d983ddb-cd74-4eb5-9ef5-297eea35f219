import {AccessToken} from './AccessToken'
import {ClientRequest} from './ClientRequest'
import {ClientResponse} from './ClientResponse'
import {User} from './User'
import {ActivityStatistics} from './ActivityStatistics'
import {TransactionLog} from './TransactionLog'
export interface ActivityLog {
  token?: AccessToken
  request?: ClientRequest
  response?: ClientResponse
  user?: User
  stats?: ActivityStatistics
  logs?: TransactionLog[]
  created_time?: number
}
