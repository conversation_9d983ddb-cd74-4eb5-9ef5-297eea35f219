export declare enum BoardType {
    BOARD_TYPE_DEFAULT = "BOARD_TYPE_DEFAULT",
    BOARD_TYPE_WORKFLOW = "BOARD_TYPE_WORKFLOW",
    BOARD_TYPE_WORKFLOW_TEMPLATE = "BOARD_TYPE_WORKFLOW_TEMPLATE",
    BOARD_TYPE_CONTENT_LIBRARY_ACTION = "BOARD_TYPE_CONTENT_LIBRARY_ACTION",
    BOARD_TYPE_CONTENT_LIBRARY_FILE = "BOARD_TYPE_CONTENT_LIBRARY_FILE",
    BOARD_TYPE_CONTENT_LIBRARY_MILESTONE = "BOARD_TYPE_CONTENT_LIBRARY_MILESTONE",
    BOARD_TYPE_SCHEDULE = "BOARD_TYPE_SCHEDULE",
    BOARD_TYPE_SELF_SERVICE_TEMPLATE = "BOARD_TYPE_SELF_SERVICE_TEMPLATE",
    BOARD_TYPE_WORKFLOW_TEMPLATE_FOLDER = "BOARD_TYPE_WORKFLOW_TEMPLATE_FOLDER",
    BOARD_TYPE_BROADCAST = "BOARD_TYPE_BROADCAST",
    BOARD_TYPE_AI = "BOARD_TYPE_AI"
}
//# sourceMappingURL=BoardType.d.ts.map