import {AppStatCategoryLeftSidePanel} from './AppStatCategoryLeftSidePanel'
import {AppStatCategoryMainNewPlusPanel} from './AppStatCategoryMainNewPlusPanel'
import {AppStatCategoryTopNavBar} from './AppStatCategoryTopNavBar'
import {AppStatCategoryMentionList} from './AppStatCategoryMentionList'
import {AppStatCategoryActionItems} from './AppStatCategoryActionItems'
import {AppStatCategoryTimeline} from './AppStatCategoryTimeline'
import {AppStatCategoryBinderView} from './AppStatCategoryBinderView'
import {AppStatCategoryOverview} from './AppStatCategoryOverview'
import {AppStatCategoryNewFlowWorkspace} from './AppStatCategoryNewFlowWorkspace'
export interface AppStatistics {
  web_left_side_panel?: AppStatCategoryLeftSidePanel
  web_main_new_plus_panel?: AppStatCategoryMainNewPlusPanel
  web_top_nav_bar?: AppStatCategoryTopNavBar
  ios_mention_list?: AppStatCategoryMentionList
  android_mention_list?: AppStatCategoryMentionList
  web_mention_list?: AppStatCategoryMentionList
  ios_action_items?: AppStatCategoryActionItems
  android_action_items?: AppStatCategoryActionItems
  web_action_items?: AppStatCategoryActionItems
  ios_timeline?: AppStatCategoryTimeline
  android_timeline?: AppStatCategoryTimeline
  ios_binder_view?: AppStatCategoryBinderView
  android_binder_view?: AppStatCategoryBinderView
  ios_overview?: AppStatCategoryOverview
  android_overview?: AppStatCategoryOverview
  ios_new_flow_workspace?: AppStatCategoryNewFlowWorkspace
  android_new_flow_workspace?: AppStatCategoryNewFlowWorkspace
  web_new_flow_workspace?: AppStatCategoryNewFlowWorkspace
}
