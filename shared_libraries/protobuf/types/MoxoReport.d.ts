import {Engagement} from './Engagement'
import {ClientCoverage} from './ClientCoverage'
import {SocialEngagement} from './SocialEngagement'
import {UserEngageMent} from './UserEngageMent'
import {ACDSummary} from './ACDSummary'
import {ACDAgentSummary} from './ACDAgentSummary'
import {SRSummary} from './SRSummary'
import {SRAgentSummary} from './SRAgentSummary'
import {GroupReport} from './GroupReport'
export interface MoxoReport {
  client_engagement?: Engagement[]
  internal_user_engagement?: Engagement[]
  client_coverage?: ClientCoverage[]
  social_engagement?: SocialEngagement[]
  user_engagement?: UserEngageMent[]
  user_activity_summary?: UserEngageMent[]
  client_changes?: ClientCoverage[]
  acd_summary?: ACDSummary[]
  acd_agent_summary?: ACDAgentSummary[]
  sr_summary?: SRSummary[]
  sr_agent_summary?: SRAgentSummary[]
  group_report?: GroupReport
}
