export interface AppStatCategoryNewFlowWorkspace {
  total_launched_by_webhook?: number
  total_launched_by_webhook_with_newly_invited_client?: number
  total_launched_from_template?: number
  total_launched_from_template_with_newly_invited_client?: number
  total_launched_from_plus_new?: number
  total_launched_from_plus_new_with_newly_invited_client?: number
  total_launched_from_scheduled_flow?: number
  total_launched_from_scheduled_flow_with_newly_invited_client?: number
  total_launched_by_zapier?: number
  total_launched_by_zapier_with_newly_invited_client?: number
  total_launched_by_rest_api?: number
  total_launched_by_rest_api_with_newly_invited_client?: number
  total_launched_from_sr?: number
  total_launched_from_main_flow?: number
}
