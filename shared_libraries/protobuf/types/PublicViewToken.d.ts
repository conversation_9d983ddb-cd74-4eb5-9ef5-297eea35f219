import {PublicViewTokenType} from './PublicViewTokenType'
export interface PublicViewToken {
  version?: number
  token?: string
  user_id?: string
  board_id?: string
  board_token?: number
  session_key?: string
  roster_index?: number
  roster_channel?: number
  type?: PublicViewTokenType
  resource_hash?: string
  resource_seq?: number
  resource_origin?: string
  actor_id?: string
  feed_seq?: number
  contact_seq?: number
  transaction_seq?: number
  boarduser_seq?: number
  created_time?: number
  user_email?: string
  user_phone_number?: string
  group_id?: string
  groupuser_seq?: number
  invitation_token_seq?: number
  invitation_token_created_timestamp?: number
  partner_id?: string
  token_created_timestamp?: number
  token_expire_timestamp?: number
}
