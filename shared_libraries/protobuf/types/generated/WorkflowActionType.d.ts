export declare enum WorkflowActionType {
    WORK<PERSON>OW_ACTION_TYPE_INVALID = "WORKFLOW_ACTION_TYPE_INVALID",
    WORKFLOW_ACTION_TYPE_SMS = "WORKFLOW_ACTION_TYPE_SMS",
    WORKFLOW_ACTION_TYPE_EMAIL = "WORKFLOW_ACTION_TYPE_EMAIL",
    WORKFLOW_ACTION_TYPE_COMMENT = "WORKFLOW_ACTION_TYPE_COMMENT",
    WORKFLOW_ACTION_TYPE_APPROVAL = "WORKFLOW_ACTION_TYPE_APPROVAL",
    WORKFLOW_ACTION_TYPE_ACKNOWLEDGE = "WORKFLOW_ACTION_TYPE_ACKNOWLEDGE",
    WORKFLOW_ACTION_TYPE_SIGNATURE = "WORKFLOW_ACTION_TYPE_SIGNATURE",
    WORKFLOW_ACTION_TYPE_TODO = "WORKFLOW_ACTION_TYPE_TODO"
}
//# sourceMappingURL=WorkflowActionType.d.ts.map