import {SystemEmailConfig} from './SystemEmailConfig'
import {UserCap} from './UserCap'
import {CachePlan} from './CachePlan'
import {SystemAppMapping} from './SystemAppMapping'
import {SystemUdpMapping} from './SystemUdpMapping'
import {SystemDocumentConverter} from './SystemDocumentConverter'
import {SystemAdminUser} from './SystemAdminUser'
import {SystemFeatures} from './SystemFeatures'
import {SystemPasswordRule} from './SystemPasswordRule'
export interface SystemConfig {
  domain?: string
  timezone?: string
  email_config?: SystemEmailConfig
  default_plan_cap?: UserCap
  plans?: CachePlan[]
  app_mappings?: SystemAppMapping[]
  udp_mappings?: SystemUdpMapping[]
  document_converter?: SystemDocumentConverter
  users?: SystemAdminUser[]
  features?: SystemFeatures
  password_rule?: SystemPasswordRule
  validation_code?: string
  is_validated?: boolean
  validation_expires_after?: number
  revision?: number
  is_deleted?: boolean
  local_revision?: number
  assignments?: string
  created_time?: number
  updated_time?: number
}
