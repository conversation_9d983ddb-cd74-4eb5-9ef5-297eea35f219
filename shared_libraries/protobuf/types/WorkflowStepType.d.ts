export declare enum WorkflowStepType {
    WORKFLOW_STEP_TYPE_INVALID = "WORKFLOW_STEP_TYPE_INVALID",
    WORKFLOW_STEP_TYPE_MILESTONE = "WORKFLOW_STEP_TYPE_MILESTONE",
    WORKFLOW_STEP_TYPE_AB = "WORKFLOW_STEP_TYPE_AB",
    WORKFLOW_STEP_TYPE_AUTOMATION = "WORKFLOW_STEP_TYPE_AUTOMATION",
    WORKFLOW_STEP_TYPE_CB = "WORKFLOW_STEP_TYPE_CB",
    WORKFLOW_STEP_TYPE_SHADOW_FLOW = "WORKFLOW_STEP_TYPE_SHADOW_FLOW",
    WOR<PERSON>FLOW_STEP_TYPE_SEND_FILE = "WORKFLOW_STEP_TYPE_SEND_FILE",
    WORKFLOW_STEP_TYPE_TRANSACTION = "WORKFLOW_STEP_TYPE_TRANSACTION",
    WORK<PERSON>OW_STEP_TYPE_FORM_REQUEST = "WORKFLOW_STEP_TYPE_FORM_REQUEST",
    WORK<PERSON>OW_STEP_TYPE_FILE_REQUEST = "WORKFLOW_STEP_TYPE_FILE_REQUEST",
    WORKFLOW_STEP_TYPE_MEET_REQUEST = "WORKFLOW_STEP_TYPE_MEET_REQUEST",
    WORKFLOW_STEP_TYPE_APPROVAL = "WORKFLOW_STEP_TYPE_APPROVAL",
    WORKFLOW_STEP_TYPE_ACKNOWLEDGE = "WORKFLOW_STEP_TYPE_ACKNOWLEDGE",
    WORKFLOW_STEP_TYPE_SIGNATURE = "WORKFLOW_STEP_TYPE_SIGNATURE",
    WORKFLOW_STEP_TYPE_TODO = "WORKFLOW_STEP_TYPE_TODO",
    WORKFLOW_STEP_TYPE_TIME_BOOKING = "WORKFLOW_STEP_TYPE_TIME_BOOKING",
    WORKFLOW_STEP_TYPE_DOCUSIGN = "WORKFLOW_STEP_TYPE_DOCUSIGN",
    WORKFLOW_STEP_TYPE_WEBHOOK = "WORKFLOW_STEP_TYPE_WEBHOOK",
    WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP = "WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP",
    WORKFLOW_STEP_TYPE_INTEGRATION = "WORKFLOW_STEP_TYPE_INTEGRATION",
    WORKFLOW_STEP_TYPE_TODO_TRANSACTION = "WORKFLOW_STEP_TYPE_TODO_TRANSACTION",
    WORKFLOW_STEP_TYPE_DECISION = "WORKFLOW_STEP_TYPE_DECISION",
    WORKFLOW_STEP_TYPE_AWAIT = "WORKFLOW_STEP_TYPE_AWAIT",
    WORKFLOW_STEP_TYPE_PDF_FORM = "WORKFLOW_STEP_TYPE_PDF_FORM",
    WORKFLOW_STEP_TYPE_SHADOW_ACTION = "WORKFLOW_STEP_TYPE_SHADOW_ACTION"
}
//# sourceMappingURL=WorkflowStepType.d.ts.map