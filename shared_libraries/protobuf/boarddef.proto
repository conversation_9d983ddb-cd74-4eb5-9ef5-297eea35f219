
message AccessToken {
    // board id from access token with type 5 (board view token)
    optional string board_id = 2;
    // device id from access token
    optional string device_id = 3;
    // client id from access token
    optional string client_id = 4;
    // agent id from cookie c_user
    optional string agent_id = 5;
    // access token from client request
    optional string access_token = 6;
    // access token expire timestamp
    optional uint64 access_token_expire_timestamp = 7;
    // access token created timestamp
    optional uint64 access_token_created_timestamp = 8;
    // user id from cookie c_user, or agent.agent_user_id
    optional string uid = 10;
    // user token from cookie token, required if uid exists
    optional string token = 11;
    // true: user token from cookie, false: from args
    optional bool token_from_cookie = 12;

    // session id: user session id
    optional string session_id = 15;
    // true: session id from cookie, false: new session
    optional bool session_id_from_cookie = 16;

    // connection id in GUID format, websocket reconnect will generate new id
    // connection id will become session id if a session id are not provided
    optional string connection_id = 17;

    // user email
    // set when reveive incoming email to {board_id}@{incoming_domain}
    optional string email = 18;

    optional string client_ip = 20;
    optional string server_ip = 21;
    optional string awselb = 22;
    optional string request_url = 23;
    optional string host = 24;
    optional string referer = 25;
    optional string client_private_ip = 26;
    optional string origin = 27;

    // nginx connection number in string format
    // used for log purpose after endpoint left
    optional string connection_number = 29;
    // client request user-agent
    optional string client_ua = 30;
    // client version from user-agent
    optional uint64 client_version = 31;
    // client_accept_language from Accept-Language
    optional string client_accept_language = 32;

    // group id of the group user belongs to
    // or the group id of the host domain for superadmin and partner admin
    optional string gid = 40;

    // user name
    // set when handle request from guest user in meet
    optional string name = 50;

    optional string x_forwarded_uri = 60;

    optional bool token_verified = 100;
    optional bool agent_token_verified = 105;
    optional bool server_token_verified = 110;
    optional bool websocket = 120;
    optional bool board_access_token_verified = 130;

    // support for webapp removed on May 22, 2014. Bingo
    //optional string webapp_id = 200;
    //optional string webapp_authorization = 210;
    //optional bool webapp_token_verified = 220;

    // used in recurly
    //optional string basic_authorization = 300;

    // access token scope
    optional uint64 scope = 400;

    optional uint64 created_time = 1000;
}

message TransactionLog {
    optional uint64 timestamp = 10;
    optional string sequence = 20;
    optional string message = 30;
}

// =============================================
// Client request and response from client/page to biz layer
// =============================================

enum ClientRequestType {
    option allow_alias = true;

    // the default value of enum is 0
	INVALID_REQUEST = 0;

	CLIENT_REQUEST_CONNECT = 5;
	JOB_REQUEST_CONNECT = 6;
    CLIENT_REQUEST_PING = 7;

    // user requests

    // user single signon request from our own SAML server
	USER_REQUEST_SSO = 8;
    // user read sso options by email
	USER_REQUEST_READ_SSO_OPTIONS = 9;
    // register a normal user, or a device
	USER_REQUEST_REGISTER = 10;
    // register an agent
	USER_REQUEST_REGISTER_AGENT = 11;
    // remove/unlink an agent
	USER_REQUEST_UNREGISTER_AGENT = 12;
    // resend email verification email
	USER_REQUEST_RESEND_VERIFICATION_EMAIL = 15;
    // verify email token and set user email as verified
	USER_REQUEST_VERIFY_EMAIL_TOKEN = 16;
    // notify server that app will enter foreground for statistics purpose
	USER_REQUEST_ENTER_FOREGROUND = 17;
    // resend org registration verification email/phone with 6 digits code
    USER_REQUEST_RESEND_VERIFICATION_CODE = 18;
    USER_REQUEST_RESEND_VERIFICATION_CODE_EMAIL = 18;
    USER_REQUEST_VERIFY_CODE = 19;
    USER_REQUEST_VERIFY_EMAIL_CODE = 19;
    // read user object after token verify
	USER_REQUEST_READ = 20;
    // read user cap and monthly bandwidth info
	USER_REQUEST_READ_CAP = 21;
    // for performance improvement on unread feed count
    USER_REQUEST_UNREAD_FEEDS_COUNT = 22;
    // subscribe to user object changes, mainly new invited board
    // also subscribe to feeds
	USER_REQUEST_SUBSCRIBE = 25;
    // read user feeds
	USER_REQUEST_READ_FEEDS = 26;
    // read user notes: video comments
	USER_REQUEST_READ_NOTES = 27;
    // update user name, email and password
	USER_REQUEST_UPDATE = 30;
    // update user profile pictures
	USER_REQUEST_UPDATE_PICTURES = 31;
    // update user name
	USER_REQUEST_UPDATE_NAME = 32;
    // update user phone number
	USER_REQUEST_UPDATE_PHONE_NUMBER = 33;
    // update user email
	USER_REQUEST_UPDATE_EMAIL = 34;
    // update user board
	USER_REQUEST_UPDATE_USER_BOARD = 35;
	USER_REQUEST_UPDATE_USER_BOARD_ENTER = 36;
        USER_REQUEST_UPDATE_USER_GROUP = 37;
    // update agent name or passcode protected
	USER_REQUEST_UPDATE_AGENT = 39;
    // login with email and pass
	USER_REQUEST_LOGIN = 40;
    // generate resource access token
    USER_REQUEST_RESOURCE_TOKEN = 43;
    USER_REQUEST_ACCESS_TOKEN = 44;
    // verify access token to see if it is still valid
	USER_REQUEST_VERIFY_TOKEN = 45;
    // create new token based on existing and valid token, server-token required so only sso server can use it
	USER_REQUEST_DUPLICATE_TOKEN = 46;
    // verify password to see if it is correct, for integration needs only
    USER_REQUEST_VERIFY_PASSWORD = 47;
    // create refresh token and access token if a regular token was provided, or refresh the access token if a refresh token was provided
    USER_REQUEST_REFRESH_TOKEN = 48;
    // set the cookie/token as invalid
    USER_REQUEST_LOGOUT = 50;
    // logout current user from all other devices
    USER_REQUEST_LOGOUT_ALL_DEVICES = 51;
    // upload a file to user resource table
    // note: picture is uploaded by adding parameter type=picture
    USER_REQUEST_UPLOAD_RESOURCE = 60;
    // upload profile pictures with 3 resolution in multipart request
    USER_REQUEST_UPLOAD_PROFILE_PICTURES = 61;
    // download user picture and other resources
	USER_REQUEST_DOWNLOAD_RESOURCE = 70;
    // request a reset password email
	USER_REQUEST_RESET_PASSWORD = 80;
	// set new password
	USER_REQUEST_CHANGE_PASSWORD = 81;

	USER_REQUEST_CATEGORY_CREATE = 90;
	USER_REQUEST_CATEGORY_RENAME = 91;
	USER_REQUEST_CATEGORY_DELETE = 92;
	USER_REQUEST_CATEGORY_ASSIGN = 93;

    // read user sessions, including live and recorded sessions
    USER_REQUEST_READ_SESSIONS = 100;
    USER_REQUEST_READ_BOARDS = 101;
    USER_REQUEST_READ_RELATIONS = 102;
    USER_REQUEST_READ_AUTO_ARCHIVED_BOARDS = 103;
    USER_REQUEST_READ_USER_BOARDS = 104;

    // check whether the email is registered
    USER_REQUEST_EMAIL_LOOKUP = 110;
    USER_REQUEST_PHONE_NUMBER_LOOKUP = 111;

    // look up user's board with provided user id/email/unique_id
    USER_REQUEST_BOARD_LOOKUP = 120;
    USER_REQUEST_RELATION_LOOKUP = 121;

    USER_REQUEST_REMOVE_FAVORITE = 130;
    USER_REQUEST_REMOVE_MENTIONME = 135;
    USER_REQUEST_REMOVE_MENTIONME_BEFORE = 136;
    USER_REQUEST_REMOVE_NOTIFICATION = 137;
    USER_REQUEST_REMOVE_NOTIFICATION_BEFORE = 138;

    // update order_number of UserBoardCategory and UserGroup
    USER_REQUEST_UPDATE_ORDER_NUMBER = 140;

    USER_REQUEST_UPDATE_ACTION_ITEM = 141;

    USER_REQUEST_REGISTER_LOCAL_USER = 150;
    USER_REQUEST_LOGIN_LOCAL_USER = 151;

    // register local user and build relation with RM
    USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN = 152;
    USER_REQUEST_VERIFY_LOCAL_EMAIL_CODE = 153;
    USER_REQUEST_CREATE_RELATION_VIA_QR_TOKEN = 155;

    // verify but do not update user object
    USER_REQUEST_PREVIEW_EMAIL_TOKEN = 160;

    USER_REQUEST_REGISTER_LOCAL_USER_BY_APPLE_ID = 170;
    USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_APPLE_ID = 171;
    USER_REQUEST_LOGIN_LOCAL_USER_BY_APPLE_ID = 172;
    USER_REQUEST_REGISTER_LOCAL_USER_BY_GOOGLE_ID = 173;
    USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_GOOGLE_ID = 174;
    USER_REQUEST_LOGIN_LOCAL_USER_BY_GOOGLE_ID = 175;
    USER_REQUEST_REGISTER_LOCAL_USER_BY_VERIFICATION_CODE = 176;
    USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_VERIFICATION_CODE = 177;
    USER_REQUEST_LOGIN_LOCAL_USER_BY_VERIFICATION_CODE = 178;
    USER_REQUEST_LOGIN_LOCAL_USER_BY_GROUP_INVITATION_TOKEN = 179;
    USER_REQUEST_LOGIN_LOCAL_USER_BY_SALESFORCE_ID = 180;

    // user update apple push notification device token
    USER_REQUEST_UPDATE_DEVICE_TOKEN = 200;

    USER_REQUEST_SEARCH = 210;

    USER_REQUEST_SEARCH_CONTENT_LIBRARY_BOARD = 220;
    USER_REQUEST_SEARCH_FLOW_TEMPLATE_LIBRARY_BOARD = 230;
    USER_REQUEST_KEEP_ALIVE = 240;

    // user email auth
	USER_REQUEST_EMAIL_AUTH = 300;
    USER_REQUEST_EMAIL_DELIVERY = 310;

    // invite a contact
    USER_REQUEST_CONTACT_INVITE = 400;
    // accept an invitation
    USER_REQUEST_CONTACT_ACCEPT = 410;
    // deny an invitation
    USER_REQUEST_CONTACT_DENY = 420;
    // cancel a pending invitation
    USER_REQUEST_CONTACT_CANCEL = 430;
    // view a contact invitation token
    USER_REQUEST_CONTACT_VIEW_INVITATION = 440;

    // add/update/delete static contact
    USER_REQUEST_CREATE_CONTACT = 450;
    USER_REQUEST_UPDATE_CONTACT = 451;
    USER_REQUEST_DELETE_CONTACT = 452;

    // list/search LDAP contact
    //USER_REQUEST_LIST_LDAP = 460;
    //USER_REQUEST_SEARCH_LDAP = 461;

    USER_REQUEST_DELETE_RESOURCE = 475;

	USER_REQUEST_FEEDBACK = 500;
    USER_REQUEST_FEEDBACK_AND_ATTACHMENT = 501;    
    //USER_REQUEST_SALES_FEEDBACK = 510;
    USER_REQUEST_SYSTEM_FEEDBACK = 520;

    // App Access for SDK Model 1 App
    // User login with username/password and client_id/signature
    // response with a url redirect with access_token
	USER_REQUEST_SSO_REGISTERED_USER = 600;
    // App Access for SDK Model 2 App
    // User login with unique id (in a group) and client_id/signature, which integrated to the group
    // response with a url redirect with access_token
	USER_REQUEST_SSO_GROUP_UNIQUE_ID = 610;
    // Use another Moxtra system as authentication service
    USER_REQUEST_SSO_EXTERNAL_MOXTRA = 620;
    // For logged in logic to provide cookie to redirect to sso_config.web_target
    // response with a url redirect with access_token
    USER_REQUEST_SSO_REDIRECT = 630;

    // send learn more about business pro email
    //USER_REQUEST_SEND_ABOUT_BUSINESS_PRO_EMAIL = 650;

    // sip proxy to update sip registration status
    USER_REQUEST_UPDATE_SIP_REGISTRATION_STATUS = 659;
    USER_REQUEST_CREATE_CALL_LOG = 660;
    USER_REQUEST_UPDATE_CALL_LOG = 661;
    USER_REQUEST_DELETE_CALL_LOG = 662;

    //USER_REQUEST_READ_FLOWS = 670;

    USER_REQUEST_READ_PASSWORD_RULE = 710;

    // resend user deletion email
	USER_REQUEST_RESEND_DELETION_EMAIL = 800;
    // verify user deletion token
	USER_REQUEST_VERIFY_DELETION_TOKEN = 801;
	// delete user
	USER_REQUEST_DELETE = 802;
    USER_REQUEST_DELETE_LOCAL_USER = 803;

    // user subscribe group public binder
    USER_REQUEST_FOLLOW_GROUP_BOARD = 810;
    // user unsubscribe group public binder
    USER_REQUEST_UNFOLLOW_GROUP_BOARD = 811;
    // user update group public binder, is_favorite etc.
    USER_REQUEST_UPDATE_GROUP_BOARD = 812;
    USER_REQUEST_UPDATE_OUT_OF_OFFICE = 813;

    // user post user activity log
    USER_REQUEST_POST_ACTIVITY = 820;
    // user read user activity logs
    USER_REQUEST_READ_ACTIVITY = 821;

    // resend verification code email in local user registration
    USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_EMAIL = 850;
    USER_REQUEST_QR_TOKEN = 851;
    USER_REQUEST_VIEW_QR_TOKEN = 852;
    USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_SMS = 860;
    USER_REQUEST_VERIFY_LOCAL_SMS_CODE = 865;

    USER_REQUEST_VERIFY_LOCAL_APPLE_JWT = 866;
    USER_REQUEST_VERIFY_LOCAL_GOOGLE_JWT = 867;

    //resend app download link via sms
    USER_REQUEST_RESEND_APP_DOWNLOAD_LINK_SMS = 870;

    USER_REQUEST_GLOBAL_LOOKUP_DOMAIN_BY_EMAIL = 880;
    USER_REQUEST_GLOBAL_LOOKUP_DOMAIN_BY_PHONE_NUMBER = 881;
    USER_REQUEST_GLOBAL_RESEND_SMS_CODE = 882;
    USER_REQUEST_GLOBAL_VERIFY_SMS_CODE = 883;
    USER_REQUEST_GLOBAL_RESEND_EMAIL_CODE = 884;
    USER_REQUEST_GLOBAL_VERIFY_EMAIL_CODE = 885;

    // libboard to pass push notification to libjob
    USER_REQUEST_PUSH_NOTIFICATION = 900;
    USER_REQUEST_SMS = 905;
    
    // broadcast crud
    USER_REQUEST_CREATE_BROADCAST = 910;
    USER_REQUEST_UPDATE_BROADCAST = 911;
    USER_REQUEST_DELETE_BROADCAST = 912;
    
    USER_REQUEST_GET_HTML = 920;
    
    USER_REQUEST_SHORT_URL = 929;
    USER_REQUEST_LOOKUP_SHORT_URL = 930;
    USER_REQUEST_VERIFY_GOOGLE_PLAY_INTEGRITY_TOKEN = 931;
    USER_REQUEST_USER_BOARD_ARCHIVE = 932;

    USER_REQUEST_MAX = 999;

    // board requests

    // for create board only
	BOARD_REQUEST_CREATE = 2000;
	BOARD_REQUEST_DUPLICATE = 2001;
    //BOARD_REQUEST_CREATE_WITH_USERS = 2002;

    // read board list without paramter
    // read board brief page list by board id
    // read board complete page by board id and page sequence
    // read board difference by revision (similiar to svn revision concept)
	BOARD_REQUEST_READ = 2010;

    // read board pages or download a resource specified in a view token
	BOARD_REQUEST_VIEW = 2011;
    // read public shared board as opengraph object format
	BOARD_REQUEST_VIEW_AS_OGO = 2012;

    // read board feeds
	BOARD_REQUEST_READ_FEEDS = 2013;

    // subscribe to board changes, including new/deleted page, annotation, member changes
    // subscribe with a revision (default is 0) is a combo command to read revision difference and subscribe to future changes
	BOARD_REQUEST_SUBSCRIBE = 2015;
    // if user is not interested with board change anymore
	BOARD_REQUEST_UNSUBSCRIBE = 2016;
    // subscribe to multiple boards, only support http long polling
	BOARD_REQUEST_SUBSCRIBE_MULTIPLE = 2017;

    // todo: update is a batch command, suggest to add individual change pdu type: like add page, add annotation, etc.
    // changes to board content, including page, annotations
    // add, update, delete (by set is_deleted flag in the object)
	BOARD_REQUEST_UPDATE = 2020;

    // copy pages from existing boards to destination board
	BOARD_REQUEST_COPY_PAGES = 2021;
    // copy resources from existing boards to destination board
	BOARD_REQUEST_COPY_RESOURCES = 2022;
    // copy todos from existing boards to destination board
	BOARD_REQUEST_COPY_TODOS = 2023;
    // copy pure flows from existing boards to destination board
    //BOARD_REQUEST_COPY_FLOWS = 2024;

    // create board comment without resources
	BOARD_REQUEST_CREATE_COMMENT = 2225;
    // create board comment with multipart POST including request and all resources
	BOARD_REQUEST_UPLOAD_COMMENT = 2226;
    // change a board comment, like text
	BOARD_REQUEST_UPDATE_COMMENT = 2227;
    // delete a board comment
	BOARD_REQUEST_DELETE_COMMENT = 2228;
    // type indication when create a board comment
    BOARD_REQUEST_TYPE_INDICATION = 2229;

    // delete the board, board page, annotation
	BOARD_REQUEST_DELETE = 2030;
    BOARD_REQUEST_UPDATE_COMMENT_URL_PREVIEW = 2031;
    BOARD_REQUEST_DELETE_COMMENT_URL_PREVIEW = 2032;

    // increase total used count
    BOARD_REQUEST_INCREASE_USED_COUNT = 2033;
    BOARD_REQUEST_UPDATE_BOARD_USER = 2034;

    // upload resource to board resource table or page resource table by a HTTP Post
	BOARD_REQUEST_UPLOAD_RESOURCE = 2040;
    // upload audio recording to S3 by a HTTP POST, audio server->biz->S3
	BOARD_REQUEST_UPLOAD_AUDIO = 2041;
    // download a resource
	BOARD_REQUEST_DOWNLOAD_RESOURCE = 2050;
    // download a single file for board
    BOARD_REQUEST_DOWNLOAD_BOARD = 2052;
    BOARD_REQUEST_UPLOAD_RESOURCE_URL = 2053;
    BOARD_REQUEST_DOWNLOAD_FOLDER = 2054;
    BOARD_REQUEST_DOWNLOAD_ZIP = 2055;

    // board user relationship
    // invite user with email
	BOARD_REQUEST_INVITE = 2060;
    // accept the invitation or request to join
	BOARD_REQUEST_JOIN = 2061;
    // leave board (member), cancel request, decline from invitation
    // remove visited
	BOARD_REQUEST_LEAVE = 2062;
    // approve the join request
	BOARD_REQUEST_APPROVE = 2063;
    // deny the join request
	BOARD_REQUEST_DENY = 2064;
    // expel the member join request
	BOARD_REQUEST_EXPEL = 2065;
    // change the member access type
	BOARD_REQUEST_SET_ACCESS_TYPE = 2066;
    // view invitation token to get invitee email and board id
	BOARD_REQUEST_VIEW_INVITATION = 2067;
    // invite ooo backup user to board
    BOARD_REQUEST_INVITE_OUT_OF_OFFICE_BACKUP_USER = 2068; 

    // transfer pending action to other board user
    BOARD_REQUEST_ACTION_TRANSFER = 2069;

    // read board recordings
    BOARD_REQUEST_GET_RECORDINGS = 2070;
    // DEPRECATED: get board recording index
    //BOARD_REQUEST_GET_RECORDING_INDEX = 2071;
    // DEPRECATED: get recording page, including the page snapshot and activities until next page
    //BOARD_REQUEST_GET_RECORDING_PAGE = 2072;
    // DEPRECATED: download audio recording file
    //BOARD_REQUEST_DOWNLOAD_RECORDING_AUDIO = 2073;
    BOARD_REQUEST_JOIN_BY_VIEW_TOKEN = 2074;
    BOARD_REQUEST_UPDATE_REQUESTING_USER = 2075;
    BOARD_REQUEST_JOIN_BY_VIEW_TOKEN_APPLE_ID = 2076;
    BOARD_REQUEST_JOIN_BY_VIEW_TOKEN_GOOGLE_ID = 2077;

    BOARD_REQUEST_CREATE_VIEW_TOKEN = 2080;
    BOARD_REQUEST_UPDATE_VIEW_TOKEN = 2083;
    BOARD_REQUEST_REMOVE_VIEW_TOKEN = 2081;
    // invite user public view with email
	BOARD_REQUEST_EMAIL_VIEW_TOKEN = 2082;
    BOARD_REQUEST_READ_VIEW_TOKEN = 2084;
    // outgoing webhook server to read all view tokens with server token
    BOARD_REQUEST_READ_VIEW_TOKENS = 2085;

    BOARD_REQUEST_SEARCH_BOARD = 2090;
    BOARD_REQUEST_SEARCH_GROUP_BOARD = 2091;
    BOARD_REQUEST_SEARCH_GROUP = 2092; // search all boards whose owner belongs to this org

    // board session requests
    // board member can start a session and becomes host of the session
	SESSION_REQUEST_START = 2100;
    // generate a new instance and start
    SESSION_REQUEST_RESTART = 2101;
    // session host can end the session
	SESSION_REQUEST_END = 2110;
    // board member can join the session and guest can join by key
	SESSION_REQUEST_JOIN = 2120;
    // similar with BOARD_REQUEST_SUBSCRIBE, only difference is that it allows subscribe even if sessionid is not in session
    SESSION_REQUEST_SUBSCRIBE = 2125;
    // leave the session
	SESSION_REQUEST_LEAVE = 2130;
    // invite user in the session via email
	SESSION_REQUEST_INVITE = 2140;
    // session roster to send keep alive
    SESSION_REQUEST_KEEP_ALIVE = 2150;
    // session roster to log events
    SESSION_REQUEST_EVENT_LOG = 2152;
    // read session events
    SESSION_REQUEST_READ_EVENT_LOG = 2153;
    // webrtc VoIP client offer are now provided from audio server
    SESSION_REQUEST_WEBRTC_OFFER = 2160;
    // audio/video/ds server to change presenter
    SESSION_REQUEST_CHANGE_PRESENTER = 2161;
    // audio/video/ds server to start ds
    SESSION_REQUEST_START_DS = 2162;
    // audio/video/ds server to stop ds
    SESSION_REQUEST_STOP_DS = 2163;
    // audio/video/ds server to publish ds state
    SESSION_REQUEST_PUBLISH_DS_STATE = 2164;
    // audio/video/ds server to publish audio state
    SESSION_REQUEST_PUBLISH_AUDIO_STATE = 2165;
    // audio/video/ds server to publish video state
    SESSION_REQUEST_PUBLISH_VIDEO_STATE = 2166;
    // sip server to read session
    SESSION_REQUEST_READ = 2168;
    // audio server to read session roster
    SESSION_REQUEST_READ_ROSTER = 2170;
    // audio server to update audio status
    SESSION_REQUEST_UPDATE_AUDIO_STATUS = 2172;
    // video server to update video status
    SESSION_REQUEST_UPDATE_VIDEO_STATUS = 2173;
    // sip server to start meet for call-in user
    SESSION_REQUEST_START_SESSION = 2174;
    // reclaim host
    SESSION_REQUEST_RECLAIM_HOST = 2175;
    // set presenter
    SESSION_REQUEST_SET_PRESENTER = 2180;
    // set host
    SESSION_REQUEST_SET_HOST = 2181;
    // mute one roster
    SESSION_REQUEST_MUTE = 2182;
    // unmute one roster
    SESSION_REQUEST_UNMUTE = 2183;
    // mute all rosters except host/presenter
    SESSION_REQUEST_MUTE_ALL = 2184;
    SESSION_REQUEST_SWITCH_PAGE = 2185;
    // combo request to create ds page and set page_switch
    SESSION_REQUEST_START_DESKTOPSHARE = 2186;
    SESSION_REQUEST_LEAVE_TELEPHONY = 2187;
    // schedule a session
    SESSION_REQUEST_SCHEDULE = 2190;
    SESSION_REQUEST_DOWNLOAD_CALENDAR = 2191;

    // schedule a personal meet room meet
    SESSION_REQUEST_CREATE_PERSONAL_ROOM = 2192;

    // personal meet room host to remove a roster
    SESSION_REQUEST_REMOVE_ROSTER = 2193;

    // lock session
    SESSION_REQUEST_LOCK = 2194;

    // unlock session
    SESSION_REQUEST_UNLOCK = 2195;

    // schedule a meet and set the given board user as owner
    SESSION_REQUEST_SCHEDULE_TO_BOARD_USER = 2198;

    // change a board, like name, do not include pages
	BOARD_REQUEST_UPDATE_BOARD = 2200;
    // multipart upload board resource
	BOARD_REQUEST_UPLOAD_BOARD_RESOURCE = 2201;

    // create page without resources
	BOARD_REQUEST_CREATE_PAGE = 2210;
    // create page with multipart POST including request and all resources
	BOARD_REQUEST_UPLOAD_PAGE = 2211;
    // change a page, like rotate, do not include elements
	BOARD_REQUEST_UPDATE_PAGE = 2212;
    // delete a page
	BOARD_REQUEST_DELETE_PAGE = 2213;

    // delete resource and pages generated
    BOARD_REQUEST_DELETE_RESOURCE = 2214;

    // set requestor to be editor
    BOARD_REQUEST_SET_EDITOR = 2215;
    BOARD_REQUEST_REMOVE_EDITOR = 2216;

    // update page's editor user type
    BOARD_REQUEST_SET_EDITOR_TYPE = 2217;

    // create page element without resources
    BOARD_REQUEST_CREATE_PAGE_ELEMENT = 2220;
    // create page element with multipart POST including request and all resources
    BOARD_REQUEST_UPLOAD_PAGE_ELEMENT = 2221;
    // change a page element, like svg content
    BOARD_REQUEST_UPDATE_PAGE_ELEMENT = 2222;
    // delete a page element
    BOARD_REQUEST_DELETE_PAGE_ELEMENT = 2223;

    // create page comment without resources
    BOARD_REQUEST_CREATE_PAGE_COMMENT = 2230;
    // create page comment with multipart POST including request and all resources
    BOARD_REQUEST_UPLOAD_PAGE_COMMENT = 2231;
    // change a page comment, like text
    BOARD_REQUEST_UPDATE_PAGE_COMMENT = 2232;
    // delete a page comment
    BOARD_REQUEST_DELETE_PAGE_COMMENT = 2233;

    BOARD_REQUEST_CREATE_PAGE_POSITION_COMMENT = 2234;
    // create page comment with multipart POST including request and all resources
    BOARD_REQUEST_UPLOAD_PAGE_POSITION_COMMENT = 2235;
    // change a page comment, like text
    BOARD_REQUEST_UPDATE_PAGE_POSITION_COMMENT = 2236;
    // delete a page comment
    BOARD_REQUEST_DELETE_PAGE_POSITION_COMMENT = 2237;

    // create page group
    BOARD_REQUEST_CREATE_PAGE_GROUP = 2240;
    // change a page group, like name
    BOARD_REQUEST_UPDATE_PAGE_GROUP = 2242;
    // delete a page group
    BOARD_REQUEST_DELETE_PAGE_GROUP = 2243;
    // copy a page group
    BOARD_REQUEST_COPY_PAGE_GROUP = 2244;
    // client will use copy+delete request to move a page group cross boards.
    // this request type is only used to move a page group inside board.
    BOARD_REQUEST_MOVE_PAGE_GROUP = 2245;

    // copy signature
    BOARD_REQUEST_COPY_SIGNATURE = 2246;
    
    // create page tag
    BOARD_REQUEST_CREATE_PAGE_TAG = 2250;
    // change a page tag
    BOARD_REQUEST_UPDATE_PAGE_TAG = 2252;
    // delete a page tag
    BOARD_REQUEST_DELETE_PAGE_TAG = 2253;

    // download converted video from board resources
    // the board has been created for web moxtra note
    BOARD_REQUEST_DOWNLOAD_NOTE = 2260;

    // create todo without resources
    BOARD_REQUEST_CREATE_TODO = 2270;
    // create todo with multipart POST including request and all resources
    BOARD_REQUEST_UPLOAD_TODO = 2271;
    // change a todo, like rotate, do not include elements
    BOARD_REQUEST_UPDATE_TODO = 2272;
    // delete a todo
    BOARD_REQUEST_DELETE_TODO = 2273;
    // assign a todo
    BOARD_REQUEST_SET_TODO_ASSIGNEE = 2274;
    // set due date to a todo
    BOARD_REQUEST_SET_TODO_DUE_DATE = 2275;
    // set a todo as completed
    BOARD_REQUEST_SET_TODO_COMPLETED = 2276;
    // update todo reference
    BOARD_REQUEST_UPDATE_TODO_ATTACHMENT = 2277;

    // create todo comment with multipart POST including request and all resources
    BOARD_REQUEST_UPLOAD_TODO_COMMENT = 2279;
    // create todo comment without resources
    BOARD_REQUEST_CREATE_TODO_COMMENT = 2280;
    // change a todo comment, like text
    BOARD_REQUEST_UPDATE_TODO_COMMENT = 2281;
    // delete a todo comment
    BOARD_REQUEST_DELETE_TODO_COMMENT = 2282;

    BOARD_REQUEST_CREATE_TODO_REMINDER = 2283;
    BOARD_REQUEST_UPDATE_TODO_REMINDER = 2284;
    BOARD_REQUEST_DELETE_TODO_REMINDER = 2285;

    // DEPRECATED
    BOARD_REQUEST_DELETE_TODO_FILE = 2290;

    // read board flat feeds
    BOARD_REQUEST_READ_FLAT_FEEDS = 2300;

    // read board thread's activities in flat feeds mode
    BOARD_REQUEST_READ_THREAD = 2301;

    // read ongoing signatures
    BOARD_REQUEST_READ_ONGOING_SIGNATURES = 2302;

    // read ongoing transactions
    BOARD_REQUEST_READ_ONGOING_TRANSACTIONS = 2303;

    // read ongoing delegation feeds
    BOARD_REQUEST_READ_ONGOING_DELEGATE_FEEDS = 2304;

    // read board cover
    BOARD_REQUEST_READ_COVER = 2305;

    // list given folder's sub folders/files
    BOARD_REQUEST_LIST_FOLDER = 2306;

    // read given file with all child pages
    BOARD_REQUEST_READ_FILE = 2307;

    // list all signatures
    BOARD_REQUEST_LIST_SIGNATURES = 2308;

    // list all todos
    BOARD_REQUEST_LIST_TODOS = 2309;

    // read signature
    BOARD_REQUEST_READ_SIGNATURE = 2310;

    BOARD_REQUEST_CREATE_REMINDER = 2320;
    BOARD_REQUEST_UPDATE_REMINDER = 2321;
    BOARD_REQUEST_DELETE_REMINDER = 2322;

    // read audit board object
    BOARD_REQUEST_READ_AUDIT_OBJECT = 2400;
    BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_RESOURCE = 2401;
    BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_BOARD = 2402;
    BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_ZIP = 2403;

    BOARD_REQUEST_RESEND_INVITATION_EMAIL = 2410;
    BOARD_REQUEST_RESEND_INVITATION_SMS = 2411;
    BOARD_REQUEST_CREATE_INVITATION_VIEW_TOKEN = 2412;
    BOARD_REQUEST_RESEND_VIEW_TOKEN = 2413;

    BOARD_REQUEST_RENEW_WORKSPACE_ID = 2415;
    BOARD_REQUEST_RESET_INVITE_CODE = 2416;

    // public session actions
    BOARD_PUBLISH_ACTION = 3000;

    // set customizable email address for board
    OBSOLETE_BOARD_REQUEST_SET_EMAIL_ADDRESS = 3010;
    // set phone number for board
    BOARD_REQUEST_SET_PHONE_NUMBER = 3020;

    BOARD_REQUEST_CREATE_FOLDER = 3030;
    BOARD_REQUEST_UPDATE_FOLDER = 3031;
    BOARD_REQUEST_DELETE_FOLDER = 3032;
    BOARD_REQUEST_COPY_FOLDER = 3033;
    BOARD_REQUEST_MOVE_FOLDER = 3034;

    BOARD_REQUEST_CREATE_FAVORITE = 3040;
    BOARD_REQUEST_PIN = 3045;

    // check whether the actor and invitees belongs to the same org
    BOARD_REQUEST_CHECK_ISRESTRICT = 3050;
    BOARD_REQUEST_CALL_LOG = 3060;
    BOARD_REQUEST_SET_OWNER_DELEGATE = 3065;
    BOARD_REQUEST_SET_FEED_STATUS = 3066;
    BOARD_REQUEST_SET_OWNER = 3067;

    BOARD_REQUEST_CREATE_SIGNATURE = 3070;
    BOARD_REQUEST_UPDATE_SIGNATURE = 3071;
    BOARD_REQUEST_DELETE_SIGNATURE = 3072;
    BOARD_REQUEST_ADD_SIGNATURE_SIGNEE = 3073;
    BOARD_REQUEST_UPDATE_SIGNATURE_SIGNEE = 3074;
    BOARD_REQUEST_REMOVE_SIGNATURE_SIGNEE = 3075;
    BOARD_REQUEST_CREATE_SIGNATURE_PAGE_ELEMENT = 3076;
    BOARD_REQUEST_UPLOAD_SIGNATURE_PAGE_ELEMENT = 3077;
    BOARD_REQUEST_UPDATE_SIGNATURE_PAGE_ELEMENT = 3078;
    BOARD_REQUEST_DELETE_SIGNATURE_PAGE_ELEMENT = 3079;
    BOARD_REQUEST_START_SIGNATURE = 3080;
    BOARD_REQUEST_UPLOAD_SIGNATURE_RESOURCE = 3081;
    BOARD_REQUEST_SUBMIT_SIGNATURE = 3082;
    BOARD_REQUEST_VIEW_SIGNATURE = 3083;
    BOARD_REQUEST_SIGNEE_UPDATE = 3084;
    BOARD_REQUEST_SIGNEE_UPLOAD_RESOURCE = 3085;
    BOARD_REQUEST_DOWNLOAD_SIGNATURE_RESOURCE = 3086;
    BOARD_REQUEST_SIGNATURE_UPDATE_ATTACHMENT = 3090;   
    BOARD_REQUEST_SIGNATURE_DELETE_ATTACHMENT = 3091;
    BOARD_REQUEST_SIGNATURE_REOPEN = 3092;
    BOARD_REQUEST_SIGNATURE_RESET_STATUS = 3093;
    BOARD_REQUEST_SIGNATURE_REPLACE = 3094;
    BOARD_REQUEST_SIGNATURE_REPLACE_PAGES_FROM_FILE = 3095;
    
    // BOT: create JSON Web Token for webapp to verify current user
    BOARD_REQUEST_CREATE_WEBAPP_TOKEN = 3100;
    BOARD_REQUEST_WEBAPP_CALLBACK = 3110;

    FILE_FLOW_REQUEST_COMMENT_CREATE = 3260;
    FILE_FLOW_REQUEST_COMMENT_UPDATE = 3261;
    FILE_FLOW_REQUEST_COMMENT_UPLOAD = 3262;
    FILE_FLOW_REQUEST_COMMENT_DELETE = 3263;

    SESSION_FLOW_REQUEST_COMMENT_CREATE = 3320;
    SESSION_FLOW_REQUEST_COMMENT_UPDATE = 3321;
    SESSION_FLOW_REQUEST_COMMENT_UPLOAD = 3322;
    SESSION_FLOW_REQUEST_COMMENT_DELETE = 3323;

    SIGN_FLOW_REQUEST_COMMENT_CREATE = 3340;
    SIGN_FLOW_REQUEST_COMMENT_UPDATE = 3341;
    SIGN_FLOW_REQUEST_COMMENT_UPLOAD = 3342;
    SIGN_FLOW_REQUEST_COMMENT_DELETE = 3343;

    BOARD_REQUEST_CREATE_WAITING_USER = 3410;
    BOARD_REQUEST_UPDATE_WAITING_USER = 3420;
    BOARD_REQUEST_DELETE_WAITING_USER = 3430;

    // update board resource's password
    BOARD_REQUEST_UPDATE_RESOURCE = 3500;

    BOARD_REQUEST_TRANSACTION_CREATE = 3510;
    BOARD_REQUEST_TRANSACTION_DELETE = 3511;
    BOARD_REQUEST_TRANSACTION_UPDATE = 3512;
    BOARD_REQUEST_TRANSACTION_COPY = 3513;
    BOARD_REQUEST_TRANSACTION_READ = 3514;
    BOARD_REQUEST_TRANSACTION_STEP_SUBMIT = 3515;
    BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT = 3516;
    BOARD_REQUEST_TRANSACTION_UPDATE_ATTACHMENT = 3517;
    BOARD_REQUEST_TRANSACTION_DELETE_ATTACHMENT = 3518;
    BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_RESOURCE = 3519;
    BOARD_REQUEST_TRANSACTION_COMMENT_CREATE = 3520;
    BOARD_REQUEST_TRANSACTION_COMMENT_UPDATE = 3521;
    BOARD_REQUEST_TRANSACTION_COMMENT_DELETE = 3522;
    BOARD_REQUEST_TRANSACTION_COMMENT_UPLOAD = 3523;
    BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_FILE = 3524;
    BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_URL = 3525;
    BOARD_REQUEST_TRANSACTION_STEP_SUBMIT_BATCH = 3530;
    BOARD_REQUEST_TRANSACTION_VIEW = 3532;
    BOARD_REQUEST_TRANSACTION_RESET_STATUS = 3538;
    BOARD_REQUEST_TRANSACTION_REOPEN = 3539;
    BOARD_REQUEST_TRANSACTION_STEP_REOPEN = 3540;
    BOARD_REQUEST_TRANSACTION_DELETE_BATCH = 3541;
    BOARD_REQUEST_TRANSACTION_UPLOAD_RESOURCE = 3542;
    BOARD_REQUEST_TRANSACTION_UPLOAD_RESOURCE_URL = 3543;
    BOARD_REQUEST_TRANSACTION_DOWNLOAD_RESOURCE = 3544;
    BOARD_REQUEST_TRANSACTION_REMINDER_CREATE = 3545;
    BOARD_REQUEST_TRANSACTION_REMINDER_UPDATE = 3546;
    BOARD_REQUEST_TRANSACTION_REMINDER_DELETE = 3547;

    //mark feed as unread
    BOARD_REQUEST_SET_FEED_UNREAD = 3600;

    // set board istemp to false
    BOARD_REQUEST_SET_ISTEMP_OFF = 3531;

    BOARD_REQUEST_UPDATE_RSVP = 3535;

    BOARD_REQUEST_CREATE_PIN = 3550;
    BOARD_REQUEST_DELETE_PIN = 3551;
    BOARD_REQUEST_READ_FLAT_PINS = 3555;

    BOARD_REQUEST_SET_BOARD_TYPE = 3556;
    BOARD_REQUEST_SET_ACTIVE = 3557;

    BOARD_REQUEST_WORKFLOW_CREATE = 3560;
    BOARD_REQUEST_WORKFLOW_UPDATE = 3561;
    BOARD_REQUEST_WORKFLOW_DELETE = 3562;

    // read predecessor boards that referenced in object.group.integrations[0].board
    BOARD_REQUEST_READ_PREDECESSORS = 3565;
    
    // server workflow requests 
    WORKFLOW_REQUEST_CREATE_TEMPLATE = 3570;
    WORKFLOW_REQUEST_UPDATE_TEMPLATE = 3571;
    WORKFLOW_REQUEST_DELETE_TEMPLATE = 3572;
    WORKFLOW_REQUEST_LIST_TEMPLATE = 3574;
    WORKFLOW_REQUEST_USE_TEMPLATE = 3575;
    WORKFLOW_REQUEST_COPY_TEMPLATE = 3576;
    WORKFLOW_REQUEST_LIST_PREBUILT_TEMPLATE = 3577;
    WORKFLOW_REQUEST_UPDATE_BOARD_WORKFLOW = 3578;
    WORKFLOW_REQUEST_UPDATE_BOARD_WORKFLOW_STEP = 3579;
    WORKFLOW_REQUEST_SKIP_BOARD_WORKFLOW_STEP = 3580;
    WORKFLOW_REQUEST_CREATE_WORKFLOW = 3581;
    WORKFLOW_REQUEST_UPDATE_WORKFLOW = 3582;
    WORKFLOW_REQUEST_DELETE_WORKFLOW = 3583;
    WORKFLOW_REQUEST_UPDATE_STATUS = 3584;
    WORKFLOW_REQUEST_RESTART = 3585;
    WORKFLOW_REQUEST_COMPLETE = 3586;
    WORKFLOW_REQUEST_CANCEL = 3587;
    WORKFLOW_REQUEST_REOPEN_STEP = 3588;
    WORKFLOW_REQUEST_SKIP_STEP = 3589;
    WORKFLOW_REQUEST_INCREASE_USED_COUNT = 3590;
    WORKFLOW_REQUEST_COPY_AS_TEMPLATE = 3591;
    WORKFLOW_REQUEST_READ_TEMPLATE_LIBRARY_FOLDER_COUNT = 3592;
    WORKFLOW_REQUEST_COPY_WORKFLOW = 3593;
    WORKFLOW_REQUEST_READ_TEMPLATE_LIBRARY_TEMPLATE_COUNT = 3594;
    WORKFLOW_REQUEST_MOVE_TEMPLATE = 3595;

    BOARD_REQUEST_CREATE_PROPERTY = 3626;
    BOARD_REQUEST_UPDATE_PROPERTY = 3627;
    BOARD_REQUEST_DELETE_PROPERTY = 3628;
    BOARD_REQUEST_UPDATE_FEED_REACTION = 3629;

    BOARD_REQUEST_CREATE_BROADCAST = 3630;
    BOARD_REQUEST_UPDATE_BROADCAST = 3631;
    BOARD_REQUEST_DELETE_BROADCAST = 3632;

    BOARD_REQUEST_UPDATE_MEETING_TRANSCRIPTION = 3640; 
    BOARD_REQUEST_DOWNLOAD_MEETING_TRANSCRIPTION = 3641;
    BOARD_REQUEST_COPY_MEETING_TRANSCRIPTION = 3642;
    BOARD_REQUEST_TRANSCRIPT_RESOURCE = 3643;
    BOARD_REQUEST_UPDATE_MEETING_SUMMARY = 3650;

    //for unit test
    BOARD_REQUEST_UNITTEST = 3998;

    BOARD_REQUEST_MAX = 3999;

    // server capacity notification & information
    SERVER_AUDIO_CAPACITY = 4000;
    SERVER_PBX_REPORT = 4002;
    // server capacity of desktop share server
    SERVER_DESKTOP_SHARE_CAPACITY = 4010;
    // server capacity of video server
    //SERVER_VIDEO_CAPACITY = 4020;
    SERVER_PROBE = 4100;
    SERVER_OBJECT_READ = 4110;
    SERVER_OBJECT_SUBSCRIBE = 4111;
    SERVER_OBJECT_UNSUBSCRIBE = 4112;
    SERVER_OBJECT_ACTIVITY = 4115;
    SERVER_OBJECT_AUDIT = 4116;
    SERVER_OBJECT_WRITE = 4120;
    SERVER_OBJECT_STATS = 4122;
    SERVER_OBJECT_LIST_SERVERS = 4124;
    SERVER_FILE_DOWNLOAD = 4130;
    SERVER_FILE_UPLOAD = 4132;
    SERVER_FILE_PREVIEW = 4134;
    SERVER_USER_DISABLE = 4140;
    SERVER_USER_ENABLE = 4141;
    SERVER_USER_LEVEL_UPGRADE = 4142;
    SERVER_USER_LEVEL_DOWNGRADE = 4143;
    // superadmin to read user object by email
    SERVER_USER_READ = 4144;
    SERVER_USER_UP_SIZE = 4145;
    SERVER_USER_DOWN_SIZE = 4146;
    SERVER_REDO_JOB = 4150;
    //forward request to corresponding server
    SERVER_FORWARD_REQUEST = 4151;

    SERVER_UPLOAD_CRASH_REPORT = 4200;
    SERVER_LIST_CRASH_REPORTS = 4210;
    SERVER_DOWNLOAD_CRASH_REPORT = 4220;
    SERVER_DELETE_CRASH_REPORT = 4230;

    SERVER_READ_STATISTICS = 4300;
    SERVER_UPDATE_STATISTICS = 4301;
    SERVER_TOKEN_DECODE = 4310;

    // public readable except private key
    // with server token, output private key as well
    SERVER_SERVICE_PROVIDERS_READ = 4400;
    // superadmin only, to create/update sso sp list
    SERVER_SERVICE_PROVIDERS_UPDATE = 4410;
    // read idp configuration with groupid/partner id and idp id
    // with server token only
    SERVER_IDP_CONFIG_READ = 4420;
    // read webapp through client id
    SERVER_WEBAPP_READ = 4430;

    // read and update system config
    SERVER_SYSTEM_CONFIG_READ = 4500;
    SERVER_SYSTEM_CONFIG_UPDATE = 4510;

    SERVER_REQUEST_VALIDATION_CODE = 4520;

    // add an org to global group list
    SERVER_GROUP_LIST_ADD = 4530;
    // remove an org from global group list
    SERVER_GROUP_LIST_REMOVE = 4535;

    // retrieve statistics data in Google Chart format
    SERVER_CHART_READ = 4600;

    // send the group usage report to license server
    SERVER_GROUP_USAGE_REPORT = 4700;
    // send the server probe report to license server
    SERVER_PROBE_REPORT = 4710;
    // send the daily statistics report to license server
    SERVER_STATISTICS_REPORT = 4720;

    SERVER_REQUEST_MAX = 4999;

    // client request to agent for list a folder
    // Parameter: agent id and path
    AGENT_REQUEST_LIST_FOLDER = 5000;
    // client request to agent to get a file
    // Parameter: agent id and path
    AGENT_REQUEST_DOWNLOAD_FILE = 5010;
    // client request to agent to get a preview picture of the file
    // Parameter: agent id , path, suggested width and height
    AGENT_REQUEST_PREVIEW_FILE = 5011;
    // client request to agent to upload a resource to board
    // Parameter: agent id, board id and path
    AGENT_REQUEST_UPLOAD_RESOURCE = 5020;
    // client request to agent to upload a file to agent space
    // Parameter: agent id, file name, content type and content data
    AGENT_REQUEST_UPLOAD_FILE = 5030;
    // client request to agent to upload a file resource, such as preview
    // Parameter: agent id, file entry, resource name, content type and content data
    AGENT_REQUEST_UPLOAD_FILE_RESOURCE = 5031;
    // client request to agent to create a folder
    // Parameter: agent id, path, and folder name
    AGENT_REQUEST_CREATE_FOLDER = 5040;
    // client request to agent to move a path
    // Parameter: agent id, path, and the new name
    AGENT_REQUEST_MOVE_ENTRY = 5041;
    // client request to agent to delete a path
    // Parameter: agent id and path
    AGENT_REQUEST_DELETE_ENTRY = 5050;
    // client request to query upload progress
    // Parameter: agent id and path, client_id
    AGENT_REQUEST_QUERY_UPLOAD_PROGRESS = 5060;

    // Agent to send metadata response back to client
    // For files, Agent has to use HTTP POST
    AGENT_PUBLISH_RESPONSE = 5100;

    // For files, Agent use HTTP POST
    AGENT_SERVE_FILE = 5110;

    // request to subscribe agent and update presence status as online
    AGENT_REQUEST_ONLINE = 5120;
    // request to unsubscribe agent and update presence status as offline
    AGENT_REQUEST_OFFLINE = 5121;


    AGENT_REQUEST_MAX = 5999;

    // web app management, webapp is also oauth app
	WEBAPP_REQUEST_CREATE = 6000;
	WEBAPP_REQUEST_READ = 6010;
	WEBAPP_REQUEST_UPDATE = 6020;
	WEBAPP_REQUEST_DELETE = 6030;
    // no longer used
    //WEBAPP_REQUEST_READ_USAGE = 6040;
    // list all webapps system wide
    WEBAPP_REQUEST_LIST = 6050;
    WEBAPP_REQUEST_LIST_BOT = 6052;
    WEBAPP_REQUEST_LIST_EMBEDDED = 6053;
    WEBAPP_REQUEST_LIST_SUBSCRIPTION = 6054;
    WEBAPP_REQUEST_LIST_INBOX_BOT = 6055;
    // download webapp resource
    WEBAPP_REQUEST_DOWNLOAD_RESOURCE = 6100;
    // upload webapp resource
    WEBAPP_REQUEST_UPLOAD_RESOURCE = 6110;
    WEBAPP_REQUEST_UPLOAD_PICTURE = 6112;
    // create JWT token for app portal
    WEBAPP_REQUEST_CREATE_TOKEN = 6200;
    // verify JWT token
    //WEBAPP_REQUEST_VERIFY_TOKEN = 6201;

    WEBAPP_REQUEST_MAX = 6999;

    // user group management
	GROUP_REQUEST_CREATE = 7000;
    // read group capabilities
    GROUP_REQUEST_READ_CAPABILITY = 7001;
    // org self signup
    GROUP_REQUEST_REGISTER = 7002;
    // list base domain for org registration
    GROUP_REQUEST_LIST_AVAILABLE_BASE_DOMAINS = 7003;
    GROUP_REQUEST_CHECK_DOMAIN_AVAILABILITY = 7004;
    GROUP_REQUEST_UNREGISTER = 7009;
	GROUP_REQUEST_READ = 7010;
	GROUP_REQUEST_READ_MEMBERS = 7011;
    GROUP_REQUEST_READ_SORT_MEMBERS = 7025;
    GROUP_REQUEST_READ_MANAGEMENT_MEMBERS = 7024;
    GROUP_REQUEST_EXPORT_MEMBERS = 7015;
    GROUP_REQUEST_EXPORT_USER_ACTIVITIES = 7016;
    GROUP_REQUEST_EXPORT_CLIENT_ENGAGEMENT = 7017;
    GROUP_REQUEST_EXPORT_INTERNAL_USER_ENGAGEMENT = 7018;
    GROUP_REQUEST_EXPORT_CLIENT_COVERAGE = 7019;
    GROUP_REQUEST_EXPORT_SOCIAL_ENGAGEMENT = 7012;
    GROUP_REQUEST_READ_MANAGEMENT_USER_ACTIVITIES = 7013;
    GROUP_REQUEST_READ_MANAGEMENT_USER_BOARDS = 7014;

    // export acd/sr report
    GROUP_REQUEST_EXPORT_SERVICE_REQUEST_SUMMARY = 7080;
    GROUP_REQUEST_EXPORT_SERVICE_REQUEST_AGENT_SUMMARY = 7081;
    GROUP_REQUEST_EXPORT_ACD_SUMMARY = 7082;
    GROUP_REQUEST_EXPORT_ACD_AGENT_SUMMARY = 7083;

    // ROUTE TO MEPX
    GROUP_REQUEST_EXPORT_CLIENT_USAGE = 7090;
    GROUP_REQUEST_EXPORT_INTERNAL_USAGE = 7091;
    GROUP_REQUEST_EXPORT_DAILY_ORG_ACTIVITY = 7092;
    GROUP_REQUEST_EXPORT_DAILY_USER_ACTIVITY = 7093;

	GROUP_REQUEST_UPDATE = 7020;
	GROUP_REQUEST_UPDATE_ALIAS = 7021;
	// update group user's alias in org
	GROUP_REQUEST_UPDATE_MEMBER_ALIAS = 7022;
	// read board id/session key by group/user alias
	GROUP_REQUEST_READ_ALIAS = 7023;
    // cancel the group and set group subscription status as expired
	GROUP_REQUEST_CANCEL = 7030;
	GROUP_REQUEST_READ_USAGE = 7040;
    GROUP_REQUEST_SUBSCRIBE = 7050;
    GROUP_REQUEST_UNSUBSCRIBE = 7060;

    GROUP_REQUEST_READ_APP_ASSOCIATION = 7070;
    GROUP_REQUEST_READ_APP_ASSETLINKS = 7071;

    GROUP_REQUEST_READ_TELEPHONY_DOMAIN = 7075;

    // group organizational hierarchy management
    //GROUP_REQUEST_CREATE_UNIT = 7090;
    //GROUP_REQUEST_UPDATE_UNIT = 7092;
    //GROUP_REQUEST_MOVE_UNIT = 7094;
    //GROUP_REQUEST_DELETE_UNIT = 7096;

    // user group and user relationship
    GROUP_REQUEST_INVITE = 7100;
    // csv import is always sync check and async import
    GROUP_REQUEST_INVITE_CSV_IMPORT = 7105;
    //GROUP_REQUEST_INVITE_CSV_IMPORT_ASYNC = 7106;
    //GROUP_REQUEST_DOWNLOAD_TASK_RESOURCE = 7107;
    GROUP_REQUEST_INVITE_CSV_IMPORT_BY_INTERNAL = 7108;
    GROUP_REQUEST_JOIN = 7110;
    GROUP_REQUEST_JOIN_VIA_INVITATION = 7115;
    GROUP_REQUEST_LEAVE = 7120;
    GROUP_REQUEST_RESEND_INVITATION_EMAIL = 7130;
    GROUP_REQUEST_INVITATION_CONFIRM_EMAIL = 7135;
    GROUP_REQUEST_RESEND_INVITATION_SMS = 7140;
    GROUP_REQUEST_INVITATION_CONFIRM_SMS = 7145;
    GROUP_REQUEST_EXPEL = 7150;
    GROUP_REQUEST_REMOVE_MEMBER = 7151;
    GROUP_REQUEST_SET_ACCESS_TYPE = 7160;
    GROUP_REQUEST_VIEW_INVITATION = 7170;
    // download group resource, or group member avatar
    GROUP_REQUEST_DOWNLOAD_RESOURCE = 7180;
    // upload a file to group resource table
    GROUP_REQUEST_UPLOAD_RESOURCE = 7190;

    // manage user with group admin
	GROUP_REQUEST_USER_READ = 7200;
	GROUP_REQUEST_USER_UPDATE = 7210;
    GROUP_REQUEST_USER_UPDATE_EMAIL = 7212;
    GROUP_REQUEST_USER_UPDATE_EMAIL_PHONE_NUMBER = 7212;
	GROUP_REQUEST_USER_DISABLE = 7230;
	GROUP_REQUEST_USER_ENABLE = 7240;
    // transfer boards user owned to another user
	GROUP_REQUEST_USER_TRANSFER = 7250;
    // transfer boards user owned to another user and set as archived
    GROUP_REQUEST_USER_ARCHIVE = 7255;
    // read group users usage info
	GROUP_REQUEST_USERS_READ = 7260;
    GROUP_REQUEST_READ_USER_BUSY_TIME = 7261;

    GROUP_REQUEST_IMPORT_REDEEM_URL = 7270;
    GROUP_REQUEST_RESET_REDEEM_URL = 7271;
    GROUP_REQUEST_GET_REDEEM_URL = 7272;

    GROUP_REQUEST_CREATE_BOARD_PROPERTY = 7280;
    GROUP_REQUEST_UPDATE_BOARD_PROPERTY = 7281;
    GROUP_REQUEST_DELETE_BOARD_PROPERTY = 7282;

    // recurly payment gateway integration
	//GROUP_REQUEST_RECURLY_NOTIFICATION = 7300;
	//GROUP_REQUEST_RECURLY_AUTHORIZE = 7310;
    // sync group status with recurly
	//GROUP_REQUEST_RECURLY_UPDATE = 7320;
    // get group billing information from recurly
	//GROUP_REQUEST_RECURLY_BILLING = 7340;
    // get group subscriptions
	//GROUP_REQUEST_RECURLY_SUBSCRIPTIONS = 7350;

    // manage board with group admin
    GROUP_REQUEST_BOARD_LEAVE = 7400;
    GROUP_REQUEST_BOARD_CREATE = 7401;
    GROUP_REQUEST_BOARD_DELETE = 7402;
    GROUP_REQUEST_BOARD_ADD_MEMBER = 7403;
    GROUP_REQUEST_BOARD_REMOVE_MEMBER = 7404;
    GROUP_REQUEST_BOARD_UPDATE_MEMBER = 7406;
    GROUP_REQUEST_BOARD_UPDATE = 7405;
    GROUP_REQUEST_SESSION_SCHEDULE = 7407;

    // internal user can invite external user
    //GROUP_REQUEST_INVITE_EXTERNAL_USER = 7410;
    // admin can create board for org members
    //GROUP_REQUEST_CREATE_BOARD_FOR_MEMBER = 7415;
    GROUP_REQUEST_CREATE_RELATION = 7416;
    GROUP_REQUEST_INVITE_AND_CREATE_RELATION = 7417;
    GROUP_REQUEST_CONFIRM_RELATION =7418;
    GROUP_REQUEST_TRANSFER_RELATION = 7419;
    GROUP_REQUEST_DELETE_RELATION = 7420;
    GROUP_REQUEST_UPDATE_RELATION = 7421;
    GROUP_REQUEST_INVITE_BOARD_REQUESTING_USER = 7422;

    GROUP_REQUEST_CREATE_BOT_RELATION = 7430;
    GROUP_REQUEST_DELETE_BOT_RELATION = 7431;

    GROUP_REQUEST_CREATE_INTEGRATION = 7500;
    GROUP_REQUEST_UPDATE_INTEGRATION = 7510;
    GROUP_REQUEST_DELETE_INTEGRATION = 7520;
    GROUP_REQUEST_VERIFY_INTEGRATION = 7530;
    GROUP_REQUEST_GET_INTEGRATION_USER_ACCESSTOKEN = 7540;

    // read group async tasks
    GROUP_REQUEST_READ_TASKS = 7550;

    // stripe payment gateway integration
    // webhook notification from stripe
	GROUP_REQUEST_STRIPE_WEBHOOK = 7600;
    // read stripe customer information including cards, payments and subscriptions
	GROUP_REQUEST_STRIPE_CUSTOMER = 7610;
    // start subscription, with a card token if none exists
	GROUP_REQUEST_STRIPE_SUBSCRIBE = 7620;
    // read stripe plan lists
	GROUP_REQUEST_STRIPE_PRICE = 7630;
    // read stripe invoices for customer
    GROUP_REQUEST_STRIPE_INVOICES = 7640;
    GROUP_REQUEST_STRIPE_UPCOMING_INVOICE = 7642;
    // read stripe coupon for customer
    GROUP_REQUEST_STRIPE_COUPON = 7650;
    // read stripe publishable key
    GROUP_REQUEST_STRIPE_PUBLISHABLE_KEY = 7660;

    // team group request
    GROUP_REQUEST_CREATE_TEAM = 7700;
    GROUP_REQUEST_UPDATE_TEAM = 7705;
    GROUP_REQUEST_DELETE_TEAM = 7710;
    GROUP_REQUEST_CREATE_PUBLIC_TEAM = 7711;
    GROUP_REQUEST_UPDATE_PUBLIC_TEAM = 7712;
    GROUP_REQUEST_DELETE_PUBLIC_TEAM = 7713;
    GROUP_REQUEST_ADD_TEAM_MEMBER = 7720;
    GROUP_REQUEST_REMOVE_TEAM_MEMBER = 7730;
    GROUP_REQUEST_LEAVE_TEAM = 7740;
    GROUP_REQUEST_REASSIGN_TEAM_OWNER = 7750;
    GROUP_REQUEST_SET_TEAM_MEMBER_ACCESS_TYPE = 7751;
    GROUP_REQUEST_ADD_TEAM_MANAGER = 7752;
    GROUP_REQUEST_REMOVE_TEAM_MANAGER = 7753;

    GROUP_REQUEST_ASSIGN_TELEPHONY_DOMAIN = 7760;
    GROUP_REQUEST_ADD_INTEGRATION_SUBSCRIBER = 7770;
    GROUP_REQUEST_REMOVE_INTEGRATION_SUBSCRIBER = 7780;
    GROUP_REQUEST_READ_INTEGRATION_SUBSCRIBERS = 7790;
    GROUP_REQUEST_SET_BOARD_MEMBER_ACCESS_TYPE = 7800;

    GROUP_REQUEST_USER_READ_ACTIVITIES = 7810;
    GROUP_REQUEST_READ_GROUP_ACTIVITIES = 7811;
    GROUP_REQUEST_USER_POST_ACTIVITIES = 7820;

    // sr dispatcher to subscribe org level open service requests
    // store in object named groupid_SERVICE_REQUESTS
    GROUP_REQUEST_SUBSCRIBE_SERVICE_REQUESTS = 7850;

    GROUP_REQUEST_CREATE_ROLE = 7900;
    GROUP_REQUEST_UPDATE_ROLE = 7901;
    GROUP_REQUEST_DELETE_ROLE = 7902;

    GROUP_REQUEST_USER_RESET_PASSWORD = 7910;
    GROUP_REQUEST_USER_UPDATE_PICTURES = 7911;
    GROUP_REQUEST_USER_UPLOAD_PROFILE_PICTURES = 7912;
    GROUP_REQUEST_USER_UPLOAD_RESOURCE = 7913;

    GROUP_REQUEST_CREATE_SOCIAL_CONNECTION = 7914;
    GROUP_REQUEST_UPDATE_SOCIAL_CONNECTION = 7915;
    GROUP_REQUEST_DELETE_SOCIAL_CONNECTION = 7916;
    GROUP_REQUEST_READ_SOCIAL_CONNECTION = 7917;

    GROUP_REQUEST_JWT_TOKEN = 7920;

    GROUP_REQUEST_USER_RESET_PASSWORD_SMS = 7925;

    GROUP_REQUEST_CREATE_ROUTING_CHANNEL = 7930;
    GROUP_REQUEST_UPDATE_ROUTING_CHANNEL = 7931;
    GROUP_REQUEST_DELETE_ROUTING_CHANNEL = 7932;

    GROUP_REQUEST_UPDATE_USER_INVITATION_TOKEN = 7940;

    GROUP_REQUEST_READ_SUBSCRIPTION_BOARDS = 7941;

    GROUP_REQUEST_READ_CONTENT_LIBRARY_BOARDS = 7942;

    GROUP_REQUEST_READ_CONTENT_LIBRARY_BOARD_COUNT = 7943;

    GROUP_REQUEST_UPDATE_CRM_REPORT = 7950;
    GROUP_REQUEST_READ_CRM_REPORT = 7951;

    GROUP_REQUEST_BOX_ACCESS_TOKEN = 7960;

    GROUP_REQUEST_SEARCH_USER_BOARD = 7970;
    GROUP_REQUEST_USER_BOARD_LOOKUP = 7971;
    GROUP_REQUEST_USER_UPDATE_OUT_OF_OFFICE = 7972;
    GROUP_REQUEST_INVITE_AND_USE_WORKFLOW_TEMPLATE = 7973;
    GROUP_REQUEST_USER_UPDATE_DISTRIBUTION_LIST = 7974;

    GROUP_REQUEST_USER_DELETE_FUTURE_SESSIONS = 7980;
    GROUP_REQUEST_USER_DELETE_SCHEDULE_BOARDS = 7981;

    GROUP_REQUEST_MAX = 7999;

    //presence
    PRESENCE_REQUEST_READ = 8000;
    PRESENCE_USER_REQUEST_READ = 8001;

    PRESENCE_REQUEST_MESSAGE = 8010;
    //set user status and message
    PRESENCE_USER_REQUEST_UPDATE = 8021;

    PRESENCE_REQUEST_MAX = 8999;

    // partner management
    // superadmin or via server token
	PARTNER_REQUEST_CREATE = 9000;
    // super admin or partner admin
	PARTNER_REQUEST_READ = 9010;
    // super admin or partner admin
	PARTNER_REQUEST_UPDATE = 9020;
    // super admin only, list all partners
    // output in user partners
	PARTNER_REQUEST_LIST = 9040;

    PARTNER_REQUEST_ADD_MEMBER = 9100;
    PARTNER_REQUEST_DELETE_MEMBER = 9110;
    PARTNER_REQUEST_VIEW_INVITATION = 9102;

    PARTNER_REQUEST_ADD_PLAN_CODE = 9120;
    PARTNER_REQUEST_DELETE_PLAN_CODE = 9130;

    PARTNER_REQUEST_CREATE_INTEGRATION = 9200;
    PARTNER_REQUEST_UPDATE_INTEGRATION = 9210;
    PARTNER_REQUEST_DELETE_INTEGRATION = 9220;

    // superadmin to read partners count, groups count, and users count
    // partner admin to read groups and users count which are managed by the partner
	PARTNER_REQUEST_READ_STATISTICS = 9300;
    // partner admin to read plan codes assigned to the partner
    // super admin to read all plan codes, or to use partner id to read plan codes assigned
	PARTNER_REQUEST_READ_PLAN_CODES = 9310;
    // partner/super admin to read license usage
    PARTNER_REQUEST_READ_USAGE = 9320;

    // super/partner admin to create a group
	PARTNER_REQUEST_CREATE_GROUP = 9400;
    // super/partner admin to list groups
	PARTNER_REQUEST_LIST_GROUPS = 9410;

    // super/partner admin to create one or multiple unique id based user in a group
    PARTNER_REQUEST_CREATE_USERS = 9500;

    PARTNER_REQUEST_CREATE_TELEPHONY_DOMAIN = 9600;
    PARTNER_REQUEST_UPDATE_TELEPHONY_DOMAIN = 9601;
    PARTNER_REQUEST_DELETE_TELEPHONY_DOMAIN = 9602;
    PARTNER_REQUEST_LIST_TELEPHONY_DOMAINS = 9610;
    // read teleppony domain by telphone number
    PARTNER_REQUEST_READ_TELEPHONY_DOMAIN = 9611;
    PARTNER_REQUEST_SET_DEFAULT_TELEPHONY_DOMAIN = 9612;
    PARTNER_REQUEST_CREATE_TELEPHONE_NUMBER = 9620;
    PARTNER_REQUEST_UPDATE_TELEPHONE_NUMBER = 9621;
    PARTNER_REQUEST_DELETE_TELEPHONE_NUMBER = 9622;
    PARTNER_REQUEST_UPLOAD_TELEPHONE_NUMBER_RESOURCE = 9630;
    PARTNER_REQUEST_DOWNLOAD_TELEPHONE_NUMBER_RESOURCE = 9631;

    PARTNER_REQUEST_MAX = 9999;

    // telephony
    TELEPHONY_REQUEST_ONCALLIN = 10000;
    TELEPHONY_REQUEST_SUBMIT_SESSIONKEY = 10010;
    TELEPHONY_REQUEST_SUBMIT_PARTICIPANTNUM = 10020;
    TELEPHONY_REQUEST_ONLEAVE = 10030;
    // on sip gateway call to join conference
    TELEPHONY_REQUEST_ON_SIPGATEWAY_CALL = 10040;
    // post sip gateway call
    TELEPHONY_REQUEST_POST_SIPGATEWAY_CALL = 10050;

    TELEPHONY_REQUEST_ON_TEXT_MESSAGE = 10060;
    TELEPHONY_REQUEST_ON_VOICE_MESSAGE = 10070;

    TELEPHONY_REQUEST_MAX = 10999;

    // Routing request from client for biz to forward to routing servers
    ROUTING_ACD_REQUEST_CREATE = 11010;
    ROUTING_ACD_REQUEST_UPDATE = 11020;
    ROUTING_ACD_REQUEST_ACCEPT = 11030;
    ROUTING_ACD_REQUEST_DECLINE = 11040;
    ROUTING_ACD_REQUEST_READ_OFFICE_HOUR = 11050;
    ROUTING_ACD_REQUEST_LEAVE_MESSAGE = 11060;
    ROUTING_ACD_REQUEST_ADD_BOT = 11070;
    ROUTING_ACD_REQUEST_REMOVE_BOT = 11072;

    // subscribe open routing request
    ROUTING_SERVICE_REQUEST_SUBSCRIBE = 11110;
    // list routing request
    ROUTING_SERVICE_REQUEST_LIST = 11120;
    ROUTING_SERVICE_REQUEST_CREATE = 11130;
    ROUTING_SERVICE_REQUEST_UPDATE = 11140;

    ROUTING_REQUEST_MAX = 11999;

    SEARCH_REQUEST_INDEX = 20000;
    SEARCH_REQUEST_SEARCH = 20010;
    SEARCH_REQUEST_MAX = 20999;

    // trigger request
    //TRIGGER_REQUEST_GET = 30000;
    //TRIGGER_REQUEST_PUT = 30010;
    //TRIGGER_REQUEST_DELETE = 30020;
    //TRIGGER_REQUEST_MAX = 30999;

    // sso request
    SSO_SP_REQUEST_GET = 40000;
    SSO_SP_REQUEST_POST = 40010;
    SSO_SP_REQUEST_MAX = 40499;

    SSO_IDP_REQUEST_GET = 40500;
    SSO_IDP_REQUEST_POST = 40510;

    SSO_REQUEST_MAX = 40999;
    //activity request
    ACTIVITY_REQUEST_QUERY = 41000;
    ACTIVITY_REQUEST_MAX = 41100;

    // mepx request
    CLIENT_USERS_FLOW_ACTION_SUMMARY = 51000;
    INT_USERS_FLOW_ACTION_SUMMARY = 51010;
    FLOW_ACTION_LIST = 51020;
    FLOW_BINDER_LIST = 51030;
    EXTERNAL_USERS = 51040;
    INTERNAL_USERS = 51050;
    WORKSPACES = 51060;
    WORKSPACE_ACTIONS = 51070;
    GROUP_REQUEST_EXPORT_WORKSPACE_LIST = 51080;
    GROUP_REQUEST_EXPORT_ACTION_LIST = 51090;
    USERS_WITH_OPEN_ACTIONS_CNT = 51100;
    GROUP_REQUEST_EXPORT_MEETING_USAGE = 51110;
    GROUP_REQUEST_EXPORT_MEETING_LIST = 51120;
    GROUP_REQUEST_EXPORT_SR_LIST = 51130;
}

enum ClientResponseCode {
    // Webocket response on connection success without user auth
    RESPONSE_CONNECT_SUCCESS = 10;
    // Webocket response on connection with XeBinder version not supported
    RESPONSE_ERROR_UPGRADE_REQUIRED = 20;
    // OK: Success!
    RESPONSE_SUCCESS = 200;
    //ask client to query transaction status by returned client uuid util queued
    RESPONSE_ACCEPTED = 202;
    // No Content
    RESPONSE_NO_CONTENT = 204;

    RESPONSE_ERROR_STATUS_MOVED = 302;

    // Switch Proxy: No longer used in http standard. Reuse it for biz worker 0 to do X-Accel-Redirect
    RESPONSE_ERROR_X_ACCEL_REDIRECT = 306;
    // Temporary Redirect: The requested resource resides temporarily under a different URI.
    RESPONSE_ERROR_TEMPORARY_REDIRECTION = 307;
    // Bad Request: The request was invalid
    RESPONSE_ERROR_INVALID_REQUEST = 400;
    // Unauthorized: Authentication credentials were missing or invalid
    RESPONSE_ERROR_INVALID_TOKEN = 401;
    // Payment Required: pass stripe 402 response to client
    RESPONSE_ERROR_PAYMENT_REQUIRED = 402;
    // Forbidden: The request is understood, but it has been refused.
    RESPONSE_ERROR_PERMISSION = 403;
    // Not Found: The requested object does not exists.
    RESPONSE_ERROR_NOT_FOUND = 404;
    // Not Acceptable: The object specified in request is in an malformed format
    RESPONSE_ERROR_INVALID_OBJECT = 406;
    // Request timeout
    RESPONSE_ERROR_TIMEOUT = 408;
    // Conflict: The email to register is already in use.
    RESPONSE_ERROR_CONFLICT = 409;
    // Precondition Failed
    RESPONSE_ERROR_PRECONDITION_FAILED = 412;
    // Request Entity Too Large
    RESPONSE_ERROR_EXCEED_LIMIT = 413;
    // Rate limit: Too Many Requests
    RESPONSE_ERROR_TOO_MANY_REQUESTS = 429;
    // Internal Server Error
    RESPONSE_ERROR_FAILED = 500;
    // Bad Gateway
    RESPONSE_ERROR_BAD_GATEWAY = 502;
    // Service Unavailable
    RESPONSE_ERROR_SERVICE_UNAVAILABLE = 503;

    // WebSocket Only Subscription Data
	RESPONSE_SUBSCRIPTION_DATA = 1000;
    RESPONSE_CONNECTION_TOKEN_VERIFIED = 1010;

    // SDK use only
    RESPONSE_ERROR_DISCONNECTED = 3000;
}

// NOTE: detail code should not has the same value as ClientResponseCode
enum ClientResponseDetailCode {
    DETAIL_CODE_NO_DETAILS = 0;
    DETAIL_CODE_CLIENT_UPGRADE_RECOMMENDED = 10;

    EXCEED_USER_BOARDS_MAX = 100;
    EXCEED_BOARD_PAGES_MAX = 110;
    EXCEED_BOARD_USERS_MAX = 120;
    EXCEED_SESSION_USERS_MAX = 130;
    EXCEED_GROUP_BOARDS_MAX = 140;
    EXCEED_GROUP_USERS_MAX = 145;
    //EXCEED_MONTHLY_UPLOAD_MAX = 150;
    EXCEED_UPLOAD_CLIENT_BODY_MAX = 160;
    EXCEED_USER_CLOUD_MAX = 170;
    EXCEED_NAME_LENGTH_MAX = 180;

    ERROR_USER_DISABLED = 2000;
    ERROR_GROUP_SUBSCRIPTION_EXPIRED = 2010;
    ERROR_SSO_ENFORCED = 2020;
    ERROR_INVALID_BOARD_ID = 2030;
    ERROR_VIRUS_DETECTED = 2040;
    ERROR_FILE_TYPE_NOT_SUPPORTED = 2050;
    ERROR_PASSWORD_RULE_CONFLICT = 2060;
    ERROR_VERIFICATION_CODE_EXPIRED = 2070;
    ERROR_BOARD_VIEW_TOKEN_EXPIRED = 2071;
    ERROR_LOGIN_LOCKED = 2080;
    ERROR_USER_NOT_REGISTERED = 2081;
    ERROR_USER_NOT_GROUP_MEMBER = 2082;
    ERROR_USER_NOT_AUTHORIZED = 2083;
    ERROR_NOT_EMPTY = 2084;
    ERROR_USER_NOT_LOGIN = 2085;
    ERROR_INVALID_USER_TYPE = 2086;
    ERROR_2FA_REQUIRED = 2087;

    // csv import error
    EXCEED_FILE_SIZE_MAX = 3010;
    ERROR_INVALID_FILE_ENCODING = 3020;
    EXCEED_FILE_LINES_MAX = 3030;
    ERROR_INVALID_FILE_FORMAT = 3035;
    ERROR_INVALID_FILE_HEADER = 3040;

    ERROR_INVALID_FIELD = 3050;
    ERROR_DUPLICATE_FIELD = 3051;
    ERROR_EXPECTED_FIELD = 3052;

    AGENT_ERROR_INVALID_PASSCODE = 40301;
    AGENT_ERROR_INVALID_TIMESTAMP = 40302;
    AGENT_ERROR_INVALID_PATH = 40303;

    // group/team is has expired subscription status
    ERROR_INTEGRATION_INVALID_GROUP = 50010;
    // group/team is has expired subscription status
    ERROR_INTEGRATION_EXPIRED_GROUP_SUBSCRIPTION = 50020;
    // failed to obtain information from 3rd integration party with token
    ERROR_INTEGRATION_INVALID_EXTERNAL_RESPONSE = 50030;

    // group/team auto provision is not enabled
    ERROR_INTEGRATION_NOT_GROUP_MEMBER = 50100;
    // group/team auto provision is not enabled
    ERROR_INTEGRATION_EXCEED_GROUP_MEMBER_QUANTITY = 50110;
    // user is pending group member
    ERROR_INTEGRATION_PENDING_GROUP_MEMBER = 50120;

    ERROR_SESSION_NOT_STARTED = 60010;
    ERROR_SESSION_ENDED = 60020;
    ERROR_SESSION_PASSWORD_PROTECTED = 60030;
    ERROR_SESSION_WAITING_LIST_ENABLED = 60040;
    ERROR_SESSION_LOCKED = 60050;

    ERROR_FLOW_STEP_INVALID_STATUS = 60100;
}

// =============================================
// User Activity data structures begin
// =============================================

message UserActivityRoutingDetail {
    optional string board_id = 10;
    optional uint64 duration = 20;  // used to report a duration of time (i.e from customer creating SR to SR assigned )
    optional uint64 count = 30; // used for service request daily report, for number of unassigned request on each day
}

message UserActivityLogDetail {
    optional CacheObject object = 10;
    optional UserActivityRoutingDetail routing_detail = 20;  // New field to support ACD & SR reporting
}

message UserActivityLogEntry {
    // AG_ID, eg. USER_MANAGEMENT
    optional string action_group_id = 10;
    optional string action_group = 20;
    optional string action_type_id = 30;
    optional string action_type = 40;
    optional string action_description = 50;

    optional string platform = 90;
    optional string browser = 100;
    optional string device_model = 110;
    optional string client_private_ip = 120;
    optional string client_public_ip = 130;
    optional string client_version = 140;

    optional UserActivityLogDetail detail = 150;

    optional uint64 created_time = 160;
}

message UserEngagementQuery {
    optional string actor_id = 10;
    repeated string peer_actor_id = 20;
}

message QueryFilter
{
    optional string key = 10;
    optional string value = 20;
    optional string op = 30;
}

message PaginationFilter
{
    repeated string conditions = 10;
}

message UserActivityLog {
    optional string actor_org_id = 10;
    optional string actor_id = 20;
    optional string actor_email = 30;
    optional string actor_unique_id = 40;
    optional string actor_phone_number = 80;
    optional UserType actor_type = 50;
    optional User actor = 200;
    optional bool suppress_stats = 70;  // supporess the statistics data on log server
    repeated UserActivityLogEntry activities = 60;

    // parameter for query activity logs from log server
    repeated string actor_ids = 90; // multiple user search support
    optional uint64 from_date = 100;
    optional uint64 to_date = 110;
    repeated string action_group_id = 120;
    repeated string action_type_id = 130;
    repeated UserEngagementQuery user_engagement_query = 140;
    //pagination
    optional uint64 page_start = 201;
    optional uint64 page_size = 202;
    optional uint64 page_number = 203;
    optional string sort_field_name = 204;
    optional string sort_method = 205;
    repeated QueryFilter filters = 206;
}

message Engagement {
    optional uint64 timestamp = 10;
    optional uint64 active_client_count = 20;

    optional uint64 binder_active_client_count = 21;
    optional uint64 p2p_active_client_count = 22;
    optional uint64 group_active_client_count = 23;

    optional uint64 session_count=30;
    optional uint64 doc_shared_count = 40;
    optional uint64 chat_count = 50;
    optional uint64 meet_count = 60;

    optional uint64 anotation_count = 70;
}

message SocialEngagement
{
    optional uint64 timestamp = 10;
    optional uint64 social_binder_create_count = 20;
    optional uint64 social_binder_active_count = 21;
    optional uint64 client_chat_count = 30;
    optional uint64 internal_chat_count = 31;
    optional uint64 client_doc_shared_count = 40;
    optional uint64 internal_doc_shared_count = 41;

    optional uint64 unboard_client_count = 50;
    optional uint64 unreply_message_count = 51;
}

//only internal use in log server
message SocialEngagementRecord
{
    optional uint64 timestamp = 10;
//wechat
    optional uint64 wechat_binder_create_count = 20;
    optional uint64 wechat_binder_active_count = 21;
    optional uint64 wechat_client_chat_count = 30;
    optional uint64 wechat_internal_chat_count = 31;
    optional uint64 wechat_client_doc_shared_count = 40;
    optional uint64 wechat_internal_doc_shared_count = 41;

//whatsapp
    optional uint64 whatsapp_binder_create_count = 120;
    optional uint64 whatsapp_binder_active_count = 121;
    optional uint64 whatsapp_client_chat_count = 130;
    optional uint64 whatsapp_internal_chat_count = 131;
    optional uint64 whatsapp_client_doc_shared_count = 140;
    optional uint64 whatsapp_internal_doc_shared_count = 141;
}

message ClientCoverage {
    optional User user = 10;
    optional uint64 client_count = 20;
    optional uint64 engaged_client_count = 30;
    optional uint64 total_engaged_client_count = 31;
    optional uint64 doc_shared_count = 40;
    optional uint64 chat_count = 50;
    optional uint64 meet_count = 60;
    optional uint64 left_client_count = 70;
    optional uint64 assigned_client_count = 80;
    optional uint64 client_coverage = 90;
    optional uint64 client_daily_coverage = 100;
}

message UserEngageMent {
    optional User user = 10;
    optional User peer_user = 20;
    optional uint64 msg_count = 50;
    optional uint64 peer_msg_count = 51;
    optional uint64 doc_shared_count = 60;
    optional uint64 peer_doc_shared_count = 61;
    optional uint64 meet_count = 70;
    optional uint64 esign_count = 80;
    optional uint64 peer_esign_count = 81;
    optional uint64 active_relation_total = 82;
    optional uint64 todo_count = 90;
    optional uint64 peer_todo_count = 91;
    optional uint64 timestamp = 100;
}

message ACDSummary
{
    optional uint64 timestamp = 1;

    optional uint64 new_acd_number = 10;
    optional uint64 accepted_acd_number = 20;
    optional uint64 request_timeout_acd_number = 30;
    optional uint64 leave_msg_acd_number = 40;
    optional uint64 total_wait_time = 50;
    optional uint64 has_guest_number = 60;
    optional uint64 has_meet_number = 70;
    optional uint64 has_file_number = 80;
    optional uint64 total_msg_file_number = 90;
    optional uint64 total_acd_duration = 100;
    optional uint64 new_anonymous_acd_number = 110;
    optional uint64 closed_acd_number = 120;
}

message ACDAgentSummary
{
    optional uint64 timestamp = 1;
    optional User user = 2;

    optional uint64 received_acd_number = 10;
    optional uint64 accepted_acd_number = 20;
    optional uint64 rejected_acd_number = 30;
    optional uint64 missed_acd_number = 40;
    optional uint64 has_guest_number = 50;
    optional uint64 has_meet_number = 60;
    optional uint64 has_file_number = 70;
    optional uint64 total_msg_file_number = 80;
    optional uint64 total_acd_duration = 90;
    optional uint64 closed_acd_number = 100;
}

message SRSummary
{
    optional uint64 timestamp = 1;

    optional uint64 new_sr_number = 10;
    optional uint64 new_unassigned_number = 20;
    optional uint64 return_to_inbox_number = 30;
    optional uint64 assigned_number = 40;
    optional uint64 assigned_no_agent_response_number = 50;
    optional uint64 total_assignment_time = 60;
    optional uint64 total_resolution_time = 70;
    optional uint64 active_sr_number = 80;
    optional uint64 total_msg_file_number = 90;
    optional uint64 agent_resolve_number = 100;
    optional uint64 client_close_number = 110;
    optional uint64 client_reopen_number = 120;
    optional uint64 reassigned_number = 121;
    optional uint64 unique_client_number = 130;
}

message SRAgentSummary
{
    optional uint64 timestamp = 1;
    optional User user = 2;

    optional uint64 assigned_to_other_number = 10;
    optional uint64 assigned_to_self_number = 20;
    optional uint64 total_assignment_time = 30;
    optional uint64 assigned_no_response_number = 40;
    optional uint64 return_to_inbox_number = 50;
    optional uint64 resolve_by_self_number = 60;
    optional uint64 close_by_client_number = 70;
    optional uint64 total_msg_file_number = 80;
    optional uint64 total_resolution_time = 90;
    optional uint64 active_sr_number = 100;
    optional uint64 client_reopen_number = 110;
    optional uint64 assigned_to_self_number_new = 120;
    optional uint64 reassigned_number = 121;
}

message GroupReport
{
    repeated GroupUser members = 10;
    optional uint64 total_number = 20;
}

message MoxoReport {
    repeated Engagement client_engagement = 10;
    repeated Engagement internal_user_engagement = 20;
    repeated ClientCoverage client_coverage = 30;
    repeated SocialEngagement social_engagement = 40;
    repeated UserEngageMent user_engagement = 50;
    repeated UserEngageMent user_activity_summary = 60;
    repeated ClientCoverage client_changes = 70;
    repeated ACDSummary acd_summary = 100;
    repeated ACDAgentSummary acd_agent_summary = 110;
    repeated SRSummary sr_summary = 120;
    repeated SRAgentSummary sr_agent_summary = 130;
    optional GroupReport group_report = 140;
}

// =============================================
// User Activity data structures end
// =============================================

// for client use only
message ClientRequests {
    repeated ClientRequest requests = 10;
}

message ClientRequest {
    optional ClientRequestType type = 10;
    optional ClientRequestType original_type = 11;
    optional string sequence = 20;
    repeated ClientParam params = 30;
    optional CacheObject object = 100;

    // request body for upload type request
    optional string request_body = 110;
    // if specified, use request body from the file
    optional string request_body_file_path = 120;
    // content type of the request body
    optional string request_body_content_type = 130;

    // the session request, deprecated, use object.session instead
    //optional ActionObject action = 200;

    //optional SearchObject search = 250;

    // optional Entry entry = 400;
    // CACHE_REQUEST_MESSAGE through presence server
    optional CacheMessage message = 600;
    //optional TwilioRequestParam twilio_param = 700;
    optional UserActivityLog user_activity = 800;
    // client request note, which can be used to debug
    optional string note = 1000;
}

message ClientResponse {
    // code is designed to be compatiable with HTTP response code
    optional ClientResponseCode code = 10;
    // detail_code is to provide details if code is RESPONSE_ERROR_EXCEED_LIMIT
    optional ClientResponseDetailCode detail_code = 12;
    // for redirect response, the new url for the redirection
    optional string redirect_url = 13;

    // response request for client to connect to the original request
    optional string sequence = 20;
    // error message, if an error occurs
    optional string message = 30;
    // data output, for sepcial response
    optional string data = 31;
    // in case of access token in data, the seconds before the token expires
    optional uint64 expires_in = 32;
    // server timestamp of the response
    optional uint64 timestamp = 33;
    // the server who provide the response
    optional string server = 34;
    // for subscription data only, the session id of the data change comes from
    optional string session_id = 35;
    // for subscription data only, the original sequence of the data change comes from
    optional string request_sequence = 36;
    // for compatibility with web, will be removed later
    optional string connection_id = 37;
    // the availability zone of the server who provide the response
    optional string zone = 38;
    // the domain of the cluster which provide the response
    optional string domain = 39;

    // data list has succeeding response
    optional bool is_truncated = 40;

    // marker in the request
    optional string marker = 41;

    // next marker for next request
    optional string next_marker = 42;

    // the corresponding client request type of this response
    optional ClientRequestType type = 50;
    // main object of user or board, etc
    optional CacheObject object = 100;
    // response http headers
    repeated HttpHeader headers = 120;
    // response params
    repeated ClientParam params = 121;

    // the session response for the request, deprecated, use object.session instead
    //optional ActionObject action = 200;

    // search result statistics
    optional uint64 hits = 210;
    optional uint64 start = 211;
    optional uint64 size = 212;

    //optional SearchObject search = 250;
    //for probe object
    // repeated MetaInfo syncs = 260;

    // available sessions of a board, deprecated, use object.session instead
    //repeated ActionObject sessions = 300;
    // XeAgent file/folder structure
    // optional Entry entry = 400;
    // Probe result
    // optional ProbeResult probe = 500;
    // Crash report query results
    // repeated CrashReport crash_reports = 600;
    // Object Read results
    // repeated ServerCachedObject server_objects = 700;
    // for playback
    // recording result
    repeated ObjectRecording recordings = 800;
    // recording indexes
    //repeated RecordingIndex indexes = 810;
    // audio recordings for playback
    repeated AudioRecording audios = 820;
    repeated VideoRecording videos = 840;
    repeated DsRecording dss = 830;

    // output decoded token
    optional PublicViewToken token = 900;

    optional CacheMessage client_message = 1000;
    // return multiple client messages in http long polling
    repeated CacheMessage client_messages = 1001;

    repeated UserActivityLog user_activities = 1100;

    optional MoxoReport report = 1200;
    // group capability
    optional GroupCapability group_capability = 1300;
}

message GroupCapability {
    optional bool has_sms_config = 10;
}

enum ClientRequestParameter {
    option allow_alias = true;

    // do not create quick start boards
    USER_REQUEST_REGISTER_NO_QS_BOARDS = 10;

    // url to access sales force open id rest api
    USER_REQUEST_SALES_FORCE_CONNECT_URL = 13;

    USER_REQUEST_READ_SET_COOKIE = 20;
    USER_REQUEST_GET_ACCESS_TOKEN = 21;

    // user read feeds/sessions start timestamp (not included)
    // 0 for most recent
    // HTTP Parameter: ts=
    USER_REQUEST_READ_TIMESTAMP = 26;
    BOARD_REQUEST_READ_TIMESTAMP = 26;
    SERVER_OBJECT_READ_TIMESTAMP = 26;
    GROUP_REQUEST_READ_USAGE_TIMESTAMP = 26;
    GROUP_REQUEST_READ_TASKS_TIMESTAMP = 26;
    // no longer used
    //WEBAPP_REQUEST_READ_USAGE_TIMESTAMP = 26;

    // user read feeds/sessions count earlier than start timestamp
    // default: 21, 0 for read most recent items until ts
    // HTTP Parameter: count=
    USER_REQUEST_READ_COUNT = 27;
    BOARD_REQUEST_READ_COUNT = 27;

    // user read sessions timestamp from (included)
    // HTTP Parameter: from=
    USER_REQUEST_READ_TIMESTAMP_FROM = 28;
    GROUP_REQUEST_READ_TIMESTAMP_FROM = 28;

    // user read sessions timestamp to (not included)
    // HTTP Parameter: to=
    USER_REQUEST_READ_TIMESTAMP_TO = 29;
    GROUP_REQUEST_READ_TIMESTAMP_TO = 29;

    // client local time's diff with GMT, unit: milliseconds
    // HTTP Parameter: offset=
    USER_REQUEST_READ_TIMESTAMP_OFFSET = 31;
    GROUP_REQUEST_READ_TIMESTAMP_OFFSET = 31;

    // export group report parameters
    GROUP_REQUEST_EXPORT_REPORT_HEADERS = 32;

    GROUP_REQUEST_EXPORT_DATE_LOCALE = 33;

    USER_REQUEST_REPORT_RUN_BY = 35;

    // generic output filtering string to reduce the output amount
    OUTPUT_FILTER_STRING = 30;
    // login output filtering
    LOGIN_OUTPUT_USER_FILTER_STRING = 33;

    // user login remember
    // HTTP Parameter: remember=
    USER_REQUEST_LOGIN_REMEMBER = 40;
    USER_REQUEST_LOGIN_OUTPUT_BASIC = 41;
    USER_REQUEST_LOGIN_LOCAL_USER_EXPECTED = 42;
    GROUP_REQUEST_LOCAL_USER_EXPECTED = 42;
    // expect partner admin or super admin in login request
    USER_REQUEST_LOGIN_PARTNER_ADMIN_EXPECTED = 43;
    // keep device token for keep receive notification in the futhure
    USER_REQUEST_LOGOUT_KEEP_DEVICE_TOKEN = 44;
    USER_REQUEST_REMEMBER_DEVICE = 45;
    USER_REQUEST_RESET_PASSWORD_PARTNER_ADMIN_EXPECTED = 46;
    USER_REQUEST_PASSWORD_ENCODED = 49;

    USER_REQUEST_KEEP_TOKEN = 50;

    // an option in search, indicates to search in user's all binders
    USER_REQUEST_ALL_BOARDS = 55;
    // an option in search, indicates to search in user's old binders
    USER_REQUEST_OLD_BOARDS = 56;

    USER_REQUEST_FEEDBACK_MESSAGE = 500;
    USER_REQUEST_FEEDBACK_SUBJECT = 501;

    USER_SUBSCRIBE_FILTER_MEET = 510;

    USER_REQUEST_FILTER_ACD = 511;
    USER_REQUEST_FILTER_SERVICE_REQUEST = 512;

    USER_REQUEST_SSO_REDIRECT_URL = 630;

    USER_REQUEST_CODE_TO_REGISTER = 640;
    USER_REQUEST_CODE_TO_RESET_PASSWORD = 641;

    USER_REQUEST_HTML_URL = 650;

    USER_REQUEST_EMAIL_CODE = 660;
    USER_REQUEST_SMS_CODE = 661;
    USER_REQUEST_APPLE_JWT = 662;
    USER_REQUEST_GOOGLE_JWT = 663;

    USER_REQUEST_DEVICE_TOKEN_VENDOR = 2012;

    USER_REQUEST_DEVICE_TOKEN_VENDOR_EXT = 1990;

    // HTTP Get Parameter: t=
	USER_REQUEST_CONTACT_INVITE_TOKEN = 2011;

	GROUP_REQUEST_INVITE_MESSAGE = 310;
    // HTTP Get Parameter: t=
	GROUP_REQUEST_INVITE_TOKEN = 2011;
    // HTTP Get Parameter: t=
    GROUP_REQUEST_USER_TOKEN = 2011;
    // HTTP Get Parameter: t=
    PARTNER_REQUEST_INVITE_TOKEN = 2011;

    // HTTP Parameter: eid=
    BOARD_REQUEST_EXTERNAL_ID = 1990;

    // create the board, but do not add to user boards
    BOARD_REQUEST_CREATE_AS_TEMP = 2000;
    BOARD_REQUEST_CREATE_AS_DEFAULT = 2001;

    BOARD_REQUEST_CLEAR_VIEW_TOKEN = 2002;
    BOARD_REQUEST_NEW_BOARD = 2003;

    // create subscription channel board
    BOARD_REQUEST_CREATE_AS_SUBSCRIPTION_CHANNEL = 2004;

    // create template signature
    BOARD_REQUEST_CREATE_SIGNATURE_AS_TEMPLATE = 2005;

    // submit signature without status change
    BOARD_REQUEST_SUBMIT_SIGNATURE_KEEP_STATUS_UNCHANGED = 2006;

    // the upload sequence to read selected pages generated from
    // HTTP Parameter: from=
	BOARD_REQUEST_READ_UPLOAD_SEQUENCE = 2010;

    // the token for read board pages, or download resources
    // HTTP Get Parameter: t=
	BOARD_REQUEST_VIEW_TOKEN = 2011;

    // the token for session roster
    // HTTP Get Parameter: t=
    SESSION_REQUEST_ROSTER_TOKEN = 2011;

    // the text for board search
    BOARD_REQUEST_SEARCH_TEXT = 2012;
    BOARD_REQUEST_SEARCH_START = 2013;
    BOARD_REQUEST_SEARCH_SIZE = 2014;
    // allow to fill multiple parameters to search batch creators
    BOARD_REQUEST_SEARCH_CREATOR = 2023;
    BOARD_REQUEST_SEARCH_ID = 2024;
    BOARD_REQUEST_SEARCH_BOARD_TYPE = 2027;
    BOARD_REQUEST_KEEP_UNREAD_FEED_TIMESTAMP = 2025;

    // with this parameter, OA can read his managed board/session
    //BOARD_REQUEST_READ_MANAGEMENT_BOARD = 2025;

    GROUP_REQUEST_SEARCH_TEXT = 2012;
    GROUP_REQUEST_SEARCH_START = 2013;
    GROUP_REQUEST_SEARCH_SIZE = 2014;
    GROUP_REQUEST_SEARCH_PAGE_NUMBER = 2015;
    GROUP_REQUEST_SEARCH_SORT_FIELD = 2016;
    GROUP_REQUEST_SEARCH_SORT_METHOD = 2017;

    PARTNER_REQUEST_SEARCH_TEXT = 2012;
    PARTNER_REQUEST_SEARCH_START = 2013;
    PARTNER_REQUEST_SEARCH_SIZE = 2014;

    //GROUP_REQUEST_SEARCH_UNIT = 2030;
    // activity action group id to read user activity log
    GROUP_REQUEST_ACTION_GROUP = 2031;
    // activity action type id to read user activity log
    GROUP_REQUEST_ACTION_TYPE = 2032;
    // allow to fill multiple parameters to search org members
    GROUP_REQUEST_SEARCH_MEMBER = 2033;
    // supress statistics on user activity
    GROUP_REQUEST_SUPPRESS_STATISTICS = 2034;

    // user search
    USER_REQUEST_SEARCH_TEXT = 2012;
    USER_REQUEST_SEARCH_START = 2013;
    USER_REQUEST_SEARCH_SIZE = 2014;
    USER_REQUEST_SEARCH_PAGE_NUMBER = 2015;
    USER_REQUEST_SEARCH_CREATOR = 2023;
    USER_REQUEST_SEARCH_ID = 2024;
    USER_REQUEST_SEARCH_SORT_BY_TIME = 2025;

    USER_REQUEST_SEARCH_DUE_FROM = 2101;
    USER_REQUEST_SEARCH_DUE_TO = 2102;
    USER_REQUEST_SEARCH_EXCLUDE_CREATOR = 2103;
    USER_REQUEST_SEARCH_CREATED_OR_ASSIGNED = 2104;
    USER_REQUEST_SEARCH_CREATED_OR_SUBMITTED = 2105;
    USER_REQUEST_SEARCH_INCLUDE_CANCELED = 2106;
    USER_REQUEST_SEARCH_TIMELINE = 2107;
    USER_REQUEST_SEARCH_ARCHIVED = 2108;
    USER_REQUEST_SEARCH_INCLUDE_EDITING = 2109;

    // output feeds in indexed format
    BOARD_REQUEST_READ_FEEDS_INDEXED = 2015;
    // output original feeds, in which feed content is not filled
    BOARD_REQUEST_READ_FEEDS_ORIGINAL = 2017;
    // read board pages includes its content instead of just local
    // also read all resources
    BOARD_REQUEST_READ_WITH_DETAIL = 2016;
    BOARD_REQUEST_READ_WITHOUT_MEMBERS = 2018;
    GROUP_REQUEST_READ_GROUP_MEMBER = 2019;
    // read group member access time
    GROUP_REQEUEST_READ_MEMBER_PRESENCE = 2020;

    // removed update binder cover flag as server will detect
    //BOARD_REQUEST_UPDATE_COVER = 2020;

    // add this parameter to include comments when copy pages
    BOARD_REQUEST_COPY_PAGES_WITH_COMMENTS = 2021;
    // add this parameter to exclude annotations when copy pages
    BOARD_REQUEST_COPY_PAGES_WITHOUT_ANNOTATIONS = 2022;
    BOARD_REQUEST_COPY_TODOS_WITH_COMMENTS = 2026;
    // add this parameter to not change creator in copy signature
    BOARD_REQUEST_COPY_SIGNATURE_KEEP_CREATOR = 2028;
    BOARD_REQUEST_COPY_TRANSACTION_KEEP_CREATOR = 2028;
    // add this parameter to copy total_used_count when copy file/signatures
    BOARD_REQUEST_COPY_WITH_TOTAL_USED_COUNT = 2035;
    // add this parameter to copy last_modified_time when copy file/signatures
    BOARD_REQUEST_COPY_WITH_LAST_MODIFIED_TIME = 2036;
    // duplicate board with signature
    BOARD_REQUEST_DUPLICATE_WITH_SIGNATURES = 2029;
    BOARD_REQUEST_DUPLICATE_WITH_TRANSACTIONS = 2037;

    // download/upload resource type
    // HTTP Parameter: type= orignal/vector/background/thumbnail/ppt/pdf
	RESOURCE_REQUEST_RESOURCE_TYPE = 2040;
    // for upload thumbnail with rotation applied
	RESOURCE_REQUEST_RESOURCE_ROTATION = 2045;

    // resource width/height param when upload resource
    RESOURCE_REQUEST_RESOURCE_WIDTH = 2047;
    RESOURCE_REQUEST_RESOURCE_HEIGHT = 2048;

    // indicate the page number in page, and order_number in group are relative
    // it should be added at a base of existing max page number
    BOARD_REQUEST_RELATIVE_ORDER_NUMBER = 2049;

    // board upload resource media length
    // HTTP Parameter: medialength=time in ms
	RESOURCE_UPLOAD_RESOURCE_MEDIA_LENGTH = 2042;
    // board upload resource description for audio/video comments
    // HTTP Parameter: desc=
	RESOURCE_UPLOAD_RESOURCE_DESCRIPTION = 2043;
    // board download Content-Disposition
    // HTTP Get Parameter: d=
    RESOURCE_DOWNLOAD_RESOURCE_CONTENT_DISPOSITION = 2050;
    //upload resource by url
    RESOURCE_UPLOAD_RESOURCE_URL = 2046;
    //http header 'Authorization' used to download resource
    RESOURCE_UPLOAD_RESOURCE_AUTHORIZATION = 2051;
    //upload resource to destination folder: <folder_seq>,<sub_folder_seq>,...
    RESOURCE_UPLOAD_RESOURCE_DESTINATION_FOLDER = 2052;
    RESOURCE_UPLOAD_RESOURCE_CONTENT_TYPE = 2053;

    // user email to send public view invitation
    BOARD_REQUEST_INVITEE_EMAIL = 2059;
    // invite user invitation message
    BOARD_REQUEST_INVITE_MESSAGE = 2060;
    // push notification message
    BOARD_REQUEST_PUSH_NOTIFICATION_MESSAGE=2061;
    // turn off push notification for current request
    BOARD_REQUEST_PUSH_NOTIFICATION_OFF=2062;
    GROUP_REQUEST_PUSH_NOTIFICATION_OFF=2062;
    // not send email for current board request
    BOARD_REQUEST_EMAIL_OFF=2063;
    // not send email for current user request
    USER_REQUEST_EMAIL_OFF=2063;
    // not send email for current group request
    GROUP_REQUEST_EMAIL_OFF=2063;
    BOARD_REQUEST_SMS_OFF=2064;
    GROUP_REQUEST_SMS_OFF=2064;
    // add the invitee as board member directly
    BOARD_REQUEST_INVITE_ADD_DIRECTLY = 2065;
    // signee can add a message when submit
    BOARD_REQUEST_SIGNEE_MESSAGE = 2066;
    GROUP_REQUEST_RESEND_EMAIL = 2067;
    GROUP_REQUEST_RESEND_SMS = 2068;
    // invite user and copy user info (RSVP/etc) from the given binder
    BOARD_REQUEST_INVITE_WITH_INFO_FROM = 2069;

    // delete folder recursively
    BOARD_REQUEST_DELETE_FOLDER_RECURSIVELY = 2070;
    // internal use only
    BOARD_REQUEST_UPDATE_FILE_COVER = 2075;
    // create file when create new page
    BOARD_REQUEST_CREATE_NEW_FILE = 2080;
    // set the new file name with sequence
    BOARD_REQUEST_NEW_FILE_NAME_SEQUENCE = 2084;
    // set the new file name
    BOARD_REQUEST_NEW_FILE_NAME = 2085;

    // for incoming webhook to have customized name and picture url
    BOARD_REQUEST_ACTOR_NAME = 2086;
    BOARD_REQUEST_ACTOR_PICTURE_URL = 2087;

    BOARD_REQUEST_CUSTOM_INFO = 2090;
    BOARD_REQUEST_CUSTOM_INFO_SEQUENCE = 2091;
    BOARD_REQUEST_ORDER_NUMBER_SEQUENCE = 2092;
    BOARD_REQUEST_SOCIAL_CUSTOM_INFO = 2095;

    // create view token for representing a board member
    BOARD_REQUEST_MEMBER_VIEW_TOKEN = 2093;
    // reset board member status to BOARD_INVITED
    BOARD_REQUEST_RESET_MEMBER_STATUS = 2094;

    // auto complete transaction when all steps are submitted
    BOARD_REQUEST_AUTO_COMPLETE_TRANSACTION = 2100;

    // the token to update board
    BOARD_REQUEST_VIEW_TOKEN_TO_UPDATE_BOARD = 2111;

    BOARD_REQUEST_DUPLICATE_WITH_WORKFLOW = 2112;
    BOARD_REQUEST_DUPLICATE_AS_TEMPLATE = 2113;
    BOARD_REQUEST_CREATE_FLOW_VARIABLE = 2114;
    // old app read/subscribe without this param, server will filter and only output workflow var which has no type or empty type
    // new app read/subscribe with this param, server won't filter workflow var list any more
    BOARD_REQUEST_NOT_FILTER_FLOW_VARIABLE = 2115;
    BOARD_REQUEST_COPY_KEEP_FLOW_STEP_UUID = 2116;
    BOARD_REQUEST_COPY_BOARD_THUMBNAIL = 2117;
    BOARD_REQUEST_COPY_BOARD_BANNER = 2118;

    // skip assignee checking in copy request
    BOARD_REQUEST_COPY_SKIP_ASSIGNEE_CHECKING = 2119;
    BOARD_REQUEST_COPY_WITH_COMPLETED_TODOS = 2121;
    BOARD_REQUEST_DUPLICATE_MILESTONE_FROM = 2122;
    BOARD_REQUEST_DUPLICATE_MILESTONE_TO = 2123;
    BOARD_REQUEST_COPY_BOARD_PROPERTY = 2124;

    BOARD_REQUEST_UPDATE_WORKFLOW_WITHOUT_LAST_MODIFIED_TIME = 2125;
    BOARD_REQUEST_REPLACE_TRANSACTION_WITH_STEPS = 2126;
    BOARD_REQUEST_COPY_WITH_WORKFLOW = 2127;

    // invite user to session invitation message
    SESSION_REQUEST_INVITE_MESSAGE = 2140;
    // start session with recording enabled
    SESSION_REQUEST_RECORDING = 2141;
    // join session as invisible roster
    SESSION_REQUEST_JOIN_INVISIBLE = 2142;
    // attendee can grab presenter when no ds/file sharing
    GRAB_PRESENTER_WHEN_NOT_SHARING = 2143;
    // mapping new session board to specified session key
    SESSION_REQUEST_SESSION_KEY = 2144;

    // suppress feed generation for this request
    BOARD_REQUEST_SUPPRESS_FEED = 2150;
    // suppress job generation for this request
    BOARD_REQUEST_SUPPRESS_JOB = 2151;
    // suppress user activity generation for this request
    BOARD_REQUEST_SUPPRESS_USER_ACTIVITY = 2152;

    // subscribe request return immediately if no update
    SUBSCRIBE_REQUEST_NOHANG = 2160;
    BOARD_REQUEST_DOWNLOAD_BOARD_NO_WAIT = 2160;

    // subscribe request returen without group members
    SUBSCRIBE_REQUEST_NO_GROUP_MEMBERS = 2161;
    // subscribe request returen without user boards
    SUBSCRIBE_REQUEST_NO_USER_BOARDS = 2162;

    // skip subscribe response filter
    SUBSCRIBE_REQUEST_RESPONSE_FILTER_OFF = 2163;

    // disable push notification on this connection
    CLIENT_CONNECTION_PUSH_NOTIFICATION_OFF = 2170;
    // disable subscription data on this connection
    CLIENT_CONNECTION_SUBSCRIPTION_DATA_OFF = 2171;

    // use the param to generate feed in flow instead of chat, or to update flow follower list
    //BOARD_REQUEST_FLOW = 2180;
    // use the param to generat chat feed besides flow feed
    //FLOW_REQUEST_NOTIFY_ALL = 2181;
    // upload clip attachment
    //FLOW_REQUEST_ATTACH_CLIP = 2182;

    BOARD_REQUEST_FILE_REPLY = 2190;
    BOARD_REQUEST_SET_LAST_MODIFIED_TIME = 2191;

    // reassign action
    BOARD_REQUEST_REASSIGN = 2192;
    // reassign from a team group
    BOARD_REQUEST_REASSIGN_FROM_GROUP_ID = 2193;
    // reassign to a team gorup
    BOARD_REQUEST_REASSIGN_TO_GROUP_ID = 2194;

    // read internal org member (user whose type is not USER_TYPE_LOCAL)
    GROUP_REQUEST_READ_MEMBER_INTERNAL = 2200;
    GROUP_REQUEST_READ_MEMBER_LOCAL = 2201;
    GROUP_REQUEST_READ_GROUP_INVITED = 2210;
    GROUP_REQUEST_READ_GROUP_ADMIN = 2211;
    GROUP_REQUEST_READ_FILTER_ROLE = 2215;
    GROUP_REQUEST_READ_INCLUDE_RELATION_USER = 2216;
    GROUP_REQUEST_READ_INCLUDE_SUGGESTED_USER = 2222;
    GROUP_REQUEST_READ_DEACTIVED_USER = 2217;
    GROUP_REQUEST_READ_DELETED_USER = 2202;
    GROUP_REQUEST_READ_MEMBER_OUTPUT_COUNT = 2203;
    GROUP_REQUEST_READ_MEMBER_OUTPUT_CLIENT_TEAM = 2204;
    GROUP_REQUEST_READ_MEMBER_OUTPUT_TEAM = 2205;

    GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_INTERNAL = 2218;
    GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_LOCAL = 2219;
    GROUP_REQUEST_OUTPUT_USER_ACTIVITIES = 2220;
    GROUP_REQUEST_READ_MANAGEMENT_TEAM_MEMBERS = 2221;
    GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_REPORT = 2222;
    GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_WITH_CLIENT_TEAMS = 2223;

    GROUP_REQUEST_READ_MEMBER_FOR_AUDIT = 2230;

    GROUP_REQUEST_EXPORT_REPORT_FORMATTED_RUN_ON = 2240;
    GROUP_REQUEST_EXPORT_REPORT_FORMATTED_FROM = 2250;
    GROUP_REQUEST_EXPORT_REPORT_FORMATTED_TO = 2260;

    // create relation and bypass RM invitation
    // bypass RM invitation automatically if any of internal/local user has unique_id
    //GROUP_REQUEST_DIRECT_RELATION_CREATION = 2220;

    USER_REQUEST_SEARCH_FILTER_MIN = 2309;
    USER_REQUEST_SEARCH_BOARD_NAME = 2310;
    USER_REQUEST_SEARCH_BOARD_USER = 2311;
    USER_REQUEST_SEARCH_BOARD_COMMENT = 2312;
    USER_REQUEST_SEARCH_BOARD_FILE = 2313;
    USER_REQUEST_SEARCH_BOARD_FOLDER = 2314;
    USER_REQUEST_SEARCH_BOARD_PAGE = 2315;
    USER_REQUEST_SEARCH_BOARD_PAGE_COMMENT = 2316;
    USER_REQUEST_SEARCH_BOARD_TODO = 2317;
    USER_REQUEST_SEARCH_BOARD_TODO_COMMENT = 2318;
    USER_REQUEST_SEARCH_BOARD_SESSION = 2319;
    USER_REQUEST_SEARCH_BOARD_SIGNATURE = 2320;
    USER_REQUEST_SEARCH_BOARD_SIGNATURE_PAGE = 2321;
    USER_REQUEST_SEARCH_BOARD_TRANSACTION = 2322;
    USER_REQUEST_SEARCH_BOARD_MENTION_COMMENT = 2323;
    USER_REQUEST_SEARCH_BOARD_APPROVAL = 2324;
    USER_REQUEST_SEARCH_BOARD_ACKNOWLEDGE = 2325;
    USER_REQUEST_SEARCH_BOARD_FILE_REQUEST = 2326;
    USER_REQUEST_SEARCH_BOARD_MEET_REQUEST = 2327;
    USER_REQUEST_SEARCH_BOARD_FORM_REQUEST = 2328;
    USER_REQUEST_SEARCH_BOARD_DOCUSIGN = 2329;
    USER_REQUEST_SEARCH_BOARD_WEBHOOK = 2330;
    USER_REQUEST_SEARCH_BOARD_LAUNCH_WEB_APP = 2331;
    USER_REQUEST_SEARCH_BOARD_INTEGRATION = 2332;
    USER_REQUEST_SEARCH_BOARD_TODO_TRANSACTION = 2333;
    USER_REQUEST_SEARCH_BOARD_WORKFLOW = 2334;
    USER_REQUEST_SEARCH_BOARD_DECISION = 2335;
    USER_REQUEST_SEARCH_BOARD_AWAIT = 2336;
    USER_REQUEST_SEARCH_BOARD_PDF_FORM = 2337;
    USER_REQUEST_SEARCH_FILTER_MAX = 2349;

    SERVER_REQUEST_INDEX_USER_INDEX = 2357;
    SERVER_REQUEST_INDEX_ORG_INDEX = 2358;
    SERVER_REQUEST_INDEX_FILTER = 2359;
    SERVER_REQUEST_INDEX_BOARD_NAME = 2360;
    SERVER_REQUEST_INDEX_BOARD_USER = 2361;
    SERVER_REQUEST_INDEX_BOARD_COMMENT = 2362;
    SERVER_REQUEST_INDEX_BOARD_FILE = 2363;
    SERVER_REQUEST_INDEX_BOARD_FOLDER = 2364;
    SERVER_REQUEST_INDEX_BOARD_PAGE = 2365;
    SERVER_REQUEST_INDEX_BOARD_PAGE_COMMENT = 2366;
    SERVER_REQUEST_INDEX_BOARD_TODO = 2367;
    SERVER_REQUEST_INDEX_BOARD_TODO_COMMENT = 2368;
    SERVER_REQUEST_INDEX_BOARD_SESSION = 2369;
    SERVER_REQUEST_INDEX_BOARD_SIGNATURE = 2370;
    SERVER_REQUEST_INDEX_BOARD_SIGNATURE_PAGE = 2371;
    SERVER_REQUEST_INDEX_BOARD_TRANSACTION = 2372;
    // use group index job to create action items
    SERVER_REQUEST_INDEX_BOARD_ACTIONITEM = 2373;
    SERVER_REQUEST_INDEX_GLOBAL_USER_ORG_MAPPING = 2380;

    USER_REQUEST_READ_START = 2400;
    USER_REQUEST_READ_SIZE = 2401;
    USER_REQUEST_READ_PAGE_NUMBER = 2402;
    USER_REQUEST_READ_BEFORE = 2403;
    USER_REQUEST_READ_AFTER = 2404;

    USER_REQUEST_READ_TIMELINE = 2500;
    USER_REQUEST_READ_SR_OPEN = 2501;
    USER_REQUEST_READ_SR_COMPLETE = 2502;
    USER_REQUEST_READ_ARCHIVED = 2503;
    USER_REQUEST_READ_SUBSCRIPTION_CHANNEL = 2504;
    USER_REQUEST_READ_INBOX = 2505;

    //P2P board:
    //1. 1 to 1 conversation
    //2. relation board
    USER_REQUEST_LOOKUP_P2P_BOARD = 2510;

    USER_REQUEST_READ_UNREAD_BOARD = 2410;
    USER_REQUEST_READ_CONVERSATION_BOARD = 2411;
    USER_REQUEST_READ_PROJECT_BOARD = 2412;
    USER_REQUEST_READ_SOCIAL_BOARD = 2413;
    USER_REQUEST_READ_SCHEDULE_BOARD = 2414;

    USER_REQUEST_USER_AGENT_EXT = 2421;

    //BOARD_REQUEST_READ_START = 3000;
    BOARD_REQUEST_READ_SIZE_BEFORE = 3001;
    BOARD_REQUEST_READ_SIZE_AFTER = 3002;
    BOARD_REQUEST_READ_FIRST_SUB_FOLDER_FILE = 3003;
    BOARD_REQUEST_READ_POSITION_COMMENT_FEED = 3004;

    // internal only editor type
    BOARD_REQUEST_EDITOR_TYPE_INTERNAL_ONLY = 3010;

    BOARD_REQUEST_REOPEN = 3020;
    BOARD_REQUEST_UPDATE_FEED = 3021;
    BOARD_REQUEST_KEEP_CURRENT_STATUS = 3022;
    BOARD_REQUEST_REOPEN_FEED = 3023;
    BOARD_REQUEST_REOPEN_EVENT = 3024;
    BOARD_REQUEST_READY_FEED = 3025;
    BOARD_REQUEST_RESET_REVIEWER_STATUS = 3026;
    BOARD_REQUEST_CREATE_FEED = 3027;
    BOARD_REQUEST_CREATE_NO_OWNER = 3036;
    BOARD_REQUEST_START_FLOW_WITHOUT_OWNER = 3037;

    BOARD_REQUEST_RESET_CODE = 3041;
    BOARD_REQUEST_VIEW_TOKEN_CODE = 3042;
    BOARD_REQUEST_MEET_REQUEST = 3043;
    BOARD_REQUEST_SET_RSVP_ACCEPTED = 3044;
    BOARD_REQUEST_CREATE_ATTACHMENT_FOLDER = 3045;
    BOARD_REQUEST_SET_ASSIGNEE_TO_OWNER = 3049;
    BOARD_REQUEST_COPY_WITH_COMPLETED_TRANSACTIONS = 3050;
    // flag to enable filters
    BOARD_REQUEST_FILTER = 3051;
    // fileters
    BOARD_REQUEST_FILTER_INCLUDE_TRANSACTION_TODO = 3052;
    BOARD_REQUEST_FILTER_INCLUDE_CUSTOM_FOLDER = 3053;

    BOARD_REQUEST_IS_INSTANT_MEET = 3066;

    // server probe server name
    SERVER_PROBE_SERVER_NAME = 4100;
    // object read server name
    SERVER_OBJECT_READ_SERVER_NAME = 4100;
    // object read log server name
    SERVER_OBJECT_READ_LOG_SERVER_NAME = 4105;
    //for SERVER_FORWARD_REQUEST, store real request type
    SERVER_OBJECT_FORWARD_REQUEST_TYPE = 4108;
    //use group id to distrbute request instead of key
    SERVER_OBJECT_GROUP_ID = 4109;
    // object read id
    SERVER_OBJECT_READ_ID = 4110;
    // object read type
    SERVER_OBJECT_READ_TYPE = 4111;
    // object read id
    SERVER_OBJECT_READ_QUERY_STRING = 4112;
    // session key to read meet logs
    SERVER_OBJECT_READ_SESSION_KEY = 4113;
    // object read user by email
    SERVER_OBJECT_READ_USER_WITH_EMAIL = 4114;
    // object read device token mapping
    SERVER_OBJECT_READ_DEVICE_TOKEN_MAPPING = 4115;
    // object read log with detail
    SERVER_OBJECT_READ_WITH_DETAIL = 2050;
    // object write id
    SERVER_OBJECT_WRITE_ID = 4120;
    SERVER_OBJECT_HASH_ID = 4125;
    // resource download key
    SERVER_RESOURCE_DOWNLOAD_KEY = 4130;
    SERVER_RESOURCE_UPLOAD_KEY = 4130;
    SERVER_RESOURCE_DOWNLOAD_BUCKET = 4131;
    SERVER_RESOURCE_UPLOAD_BUCKET = 4131;
    SERVER_RESOURCE_DOWNLOAD_OBJECT_ID = 4132;
    SERVER_RESOURCE_UPLOAD_OBJECT_ID = 4132;

    // preview params
    SERVER_PREVIEW_RESOURCE_URL = 4133;
    SERVER_PREVIEW_RESOURCE_HASH = 4134;
    SERVER_PREVIEW_RESOURCE_EXTENSION = 4135;

    // redo job id
    SERVER_REDO_JOB_ID = 4144;
    // target zone when migrate object
    SERVER_MIGRATE_ZONE = 4145;
    // domain used in email job
    SERVER_JOB_DOMAIN = 4146;
    SERVER_JOB_USER_ID = 4147;
    SERVER_JOB_FORWARDED_URI = 4148;

    SERVER_OBJECT_READ_PARAM_BEFORE = 4150;
    SERVER_OBJECT_READ_PARAM_AFTER = 4151;
    SERVER_OBJECT_READ_BOARD_FEEDS = 4160;
    SERVER_OBJECT_READ_BOARD_FEEDS_PAGE = 4161;
    SERVER_OBJECT_READ_BOARD_THREAD = 4162;
    SERVER_OBJECT_READ_BOARD_FOLDER = 4163;
    SERVER_OBJECT_READ_BOARD_FILE = 4164;

    SERVER_UPLOAD_CRASH_REPORT_NAME = 4200;
    SERVER_DOWNLOAD_CRASH_REPORT_NAME = 4220;

    SERVER_REQUEST_SERVER_TOKEN = 4230;
    SERVER_REQUEST_EXPIRES_AFTER = 4231;
    SERVER_REQUEST_SERVER_DOMAIN = 4232;

    GROUP_REQUEST_CANCEL_MESSAGE = 7030;
    GROUP_REQUEST_INVITE_PREVIEW = 7105;
    // update existing group users, type/email/unique_id are id field and will not be updated
    GROUP_REQUEST_INVITE_UPDATE_EXISTING = 7106;
    // stripe card token for subscribe
    GROUP_REQUEST_STRIPE_SUBSCRIBE_TOKEN = 7620;
    // stripe coupon code for read coupon and subscribe
    GROUP_REQUEST_STRIPE_COUPON_CODE = 7650;

    GROUP_REQUEST_READ_MEET = 7660;

    // when expel org member, delete user's owned board
    GROUP_REQUEST_DELETE_BOARD = 7670;

    GROUP_REQUEST_READ_RELATED_USER_RELATION = 7680;
    GROUP_REQUEST_READ_RELATED_USER_FLOW_TEMPLATE = 7685;
    GROUP_REQUEST_READ_RELATED_USER_CONTENT_LIBRARY = 7686;
    GROUP_REQUEST_READ_RELATED_USER_FLOW_TEMPLATE_LIBRARY = 7687;

    GROUP_REQUEST_NO_RELATION_BOARD = 7681;
    GROUP_REQUEST_CREATE_RELATION_BOARD = 7682;
    GROUP_REQUEST_CREATE_PENDING_RELATION = 7683;
    GROUP_REQUEST_WELCOME_MESSAGE = 7684;

    // expired after time in seconds
    GROUP_REQUEST_JWT_EXPIRES_AFTER = 4231;
    GROUP_REQUEST_JWT_PAYLOAD = 2012;

    GROUP_REQUEST_BOX_PAYLOAD = 7690;

    GROUP_REQUEST_REFRESH_DEFAULT_MEET_PASSWORD = 7700;

    GROUP_REQUEST_CREATE_CREATOR_AS_GROUP_OWNER = 7800;

    GROUP_REQUEST_READ_CONTENT_LIBRARY_INCLUDE_DEFAULT = 7900;

    // request param by its name
    CLIENT_PARAM_REVISION = 8001;
    CLIENT_PARAM_JWT = 8100;
    // it can be the breakout session's board id which the user has joined;
    // use it to authorize to read another breakout session in the same meet
    CLIENT_PARAM_FROM_BOARD_ID = 8111;
    CLIENT_PARAM_SET_USER_ID_TO_VARIABLE = 8112;
    CLIENT_PARAM_CHECK_BOARD_ACCESS = 8113;
    CLIENT_PARAM_ORIGINAL_BOARD_ID = 8114;
    CLIENT_PARAM_PARENT_BOARD_ID = 8115;
    CLIENT_PARAM_KEEP_BOARD_OWNER = 8116;
    CLIENT_PARAM_EMAIL_SMS_SKIP_USER_ID = 8117;
    CLIENT_PARAM_CUSTOM_DATA = 8118;
    CLIENT_PARAM_REFERENCE_TYPE = 8119;
    CLIENT_PARAM_BROADCAST_TO_USER_ID = 8124;

    PRESENCE_PARAM_USER_ONLINE = 9001;
    PRESENCE_PARAM_USER_CLIENT = 9002;

    OUTPUT_INCLUDE_STRING = 9030;

    BOARD_REQUEST_ACD_SESSION_END_FEED = 9040;

    BOARD_REQUEST_MEETING_TRANSCRIPTION = 9050;

    BOARD_REQUEST_MEETING_SUMMARY = 9060;

    // response param
    CLIENT_PARAM_TIMELINE_BOTTOM_FEED_TIMESTAMP = 20001;

    // mepx param
    USER_REQUEST_READ_TIMEZONE = 30010;
    GROUP_REQUEST_REPORT_NAME = 30020;
    USER_REQUEST_INCL_WORKSPACE_TAGS = 30030;
    USER_REQUEST_INCL_ADDITIONAL_COLUMNS = 30040;
}

message ClientParam {
    optional ClientRequestParameter name = 10;
    optional string string_value = 20;
    optional uint64 uint64_value = 30;
}

message ActionPageSwitch {
    // user roster id who made the last page switch
    optional string id = 10;
    optional uint64 page_sequence = 30;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
    optional uint64 revision = 1020;

    optional string assignments = 1200;
}

message ActionLaserPointer {
    // user roster id who made the last laser pointer
    optional string id = 10;
    optional bool is_deleted = 20;
    optional uint64 page_sequence = 30;
    optional uint64 px = 40;
    optional uint64 py = 50;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
    optional uint64 revision = 1020;
}

message ActionUserPointer {
    // user roster id
    optional string id = 10;
    optional bool is_deleted = 20;
    optional uint64 page_sequence = 30;
    optional uint64 px = 40;
    optional uint64 py = 50;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
    optional uint64 revision = 1020;
	optional uint64 sequence = 1030;
}

message AudioWebrtcChannel {
    optional uint64 channel_id = 10;
    optional string webrtc_offer = 20;
    optional string webrtc_answer = 30;
}

enum AudioStatusRequest {
    AUDIO_STATUS_REQUEST_NONE = 0;
    AUDIO_STATUS_REQUEST_MUTE = 10;
    AUDIO_STATUS_REQUEST_UNMUTE = 20;
    AUDIO_STATUS_REQUEST_LEAVE_TELEPHONY = 30;
}

message AudioStatus {
    optional bool is_in_session = 10;
    optional bool is_mute = 20;
    optional uint64 ssrc = 30;
    repeated AudioWebrtcChannel channels = 40;
}

message TelephoneStatus {
    optional bool is_in_session = 10;
    optional bool is_mute = 20;
    optional bool is_pure_telephone_user = 30;
    optional uint64 ssrc = 40;
    optional TwilioRequestParam twilio_param = 100;
    // the server timestamp when this telephone join
    optional uint64 join_timestamp = 110;
    optional uint64 leave_timestamp = 120;
}

message RosterVideoStatus {
    optional bool is_in_session = 10;
    optional bool is_broadcast = 20;
}


enum BoardWaitingUserStatus {
    BOARD_WAITING_USER_STATUS_NONE = 0;
    BOARD_WAITING_USER_STATUS_PENDING = 10;
    BOARD_WAITING_USER_STATUS_APPROVED = 20;
    BOARD_WAITING_USER_STATUS_DENIED = 30;
    BOARD_WAITING_USER_STATUS_BLOCKED = 40;
}

// ActionUserRoster can not be a container, in order to support filter invisible roster
message ActionUserRoster {
    // user roster id, is the connection id for websocket, or session id for long polling
    optional string id = 10;
    // user roster id of who made the change/action
    // it is used at client side to tell whether the change/action is made by host/presenter
    optional string requestor_id = 11;

    optional User user = 30 [lazy = true];
    // group can also be a meet invitee
    optional Group group = 35 [lazy = true];
    // roster's audio status
    optional AudioStatus audio_status = 40;
    // pending mute/unmute request on roster's audio status
    optional AudioStatusRequest audio_status_request = 41;

    // used when this roster is in board waiting list
    optional BoardWaitingUserStatus waiting_user_status = 45;

    optional bool is_host = 50;
    optional bool is_presenter = 60;
    optional bool is_invisible = 70;
        // indicate a user joined from team
    optional bool is_from_team = 80;

    optional uint64 participant_number = 100;
    optional TelephoneStatus telephone_status = 110;

    optional RosterVideoStatus video_status = 200;

    optional string roster_tag = 220;

    optional bool is_deleted = 20;
    //optional uint64 local_revision = 130;

    // mark field to keep content on deletion, do not fill
    optional bool keep_deleted = 121;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
    optional uint64 revision = 1020;
    optional uint64 sequence = 1030;
    optional string client_uuid = 1040;
    optional string assignments = 1050;
}

message ActionUserRosterEventItem {
    optional string data = 10;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message ActionUserRosterEvent {
    // user roster id
    optional string id = 10;
    repeated ActionUserRosterEventItem items = 20;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional uint64 local_revision = 130;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message ActionUserRosterKeepAliveInfo {
    // user roster id, is the connection id for websocket, or session id for long polling
    optional string id = 10;

    // long polling subscribe and websocket will update this timestamp every 30 seconds
    // user will be removed from roster if no keepalive for 150 seconds
    // this timestamp was only used in cache and do not output to biz
    optional uint64 keepalive_timestamp = 21;

    optional bool is_deleted = 20;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
    optional uint64 revision = 1020;
	optional uint64 sequence = 1030;
}

enum VideoStatus {
    VIDEO_STOPPED = 0;
    VIDEO_PLAYING = 10;
    VIDEO_PAUSED = 20;
}

message ActionVideoState {
    // user roster id who made the last video status change
    optional string id = 10;
    optional bool is_deleted = 20;
    // for audio/video in page vector, video_sequence is the same the page_sequence
    // for audio/video in page element, video_sequence is the element sequence
    optional uint64 video_sequence = 25;
    optional uint64 page_sequence = 30;
    optional VideoStatus status = 40;
    // video position offset in milliseconds
    optional uint64 action_timestamp = 50;

    optional uint64 revision = 1020;
	optional uint64 sequence = 1030;
    optional uint64 created_time = 1000;
    // the timestamp action was updated
    optional uint64 updated_time = 200;
}

message AudioEdgeServer {
    optional string availability_zone = 5;
    optional string server_url = 6;
    // server ip address
    optional string server_addr = 10;
    // actual dns name for ipv6 support
    optional string server_name = 15;
    optional uint64 port = 20;
    optional uint64 tcp_port = 21;
}

message AudioConf {
    // primary audio server address
    optional string availability_zone = 5;
    optional string server_url = 6;
    // server ip address
    optional string server_addr = 10;
    // actual dns name for ipv6 support
    optional string server_name = 15;
    // global domain name for geo support
    optional string geo_domain = 16;

    optional uint64 port = 20;
    optional uint64 tcp_port = 21;
    optional string fingerprint = 22;

    // edge audio server addresses
    repeated AudioEdgeServer edge_servers = 25;

    optional string conf_id = 30;
    optional string token = 40;

    // when audio server is down and no replacement is available
    optional bool is_deleted = 50;

    //optional string recording_server_addr = 60;
    //optional string internal_server_addr = 62;

    optional string client_uuid = 101;
    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
    optional uint64 revision = 1020;
    optional uint64 sequence = 1030;
}

message TelephonyConf {
    optional string telephony_domain_id = 90;
    repeated TelephoneNumber numbers = 100;

    optional bool is_deleted = 50;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
    optional uint64 revision = 1020;
    optional uint64 sequence = 1030;
}

//message TelephoneNumber
//{
//    optional string number = 10;
//    optional string location = 20;
//}

enum SessionStatus {
    SESSION_ENDED = 0;
    SESSION_SCHEDULED = 10;
    SESSION_STARTED = 20;
    SESSION_EXPIRED = 100;
}

enum ExtCalType {
    CAL_TYPE_OUTLOOK = 0;
    CAL_TYPE_GOOGLE = 10;
}

enum VendorServiceType {
    SERVICE_DEFAULT = 0;
    SERVICE_ZOOM = 1;
    SERVICE_OFFLINE = 2;
    SERVICE_MSTEAM = 3;
    SERVICE_OTHER = 20;
}

// action are inside one board and there is no actions like switch boards
// session
message ActionObject {
    // the board id of the session
    optional string board_id = 10;
    optional string original_board_id = 11;
    // if this is an exception to a recurring board, parent_board_id points to the parent recurring board
    // if this is an instance of a recurring/exceptional board, parent_board_id points to the parent recurring/exceptional board
    optional string parent_board_id = 12;
    // the session key of the session, which is also the unique key of the object
    optional string session_key = 20;
    optional string session_password = 21;
    optional bool password_protected = 22;
    optional string ext_cal_event_id = 23; // indicate google/outlook calendar event id
    optional ExtCalType ext_cal_type = 24; // indicate external calender type: google/outlook

    // the session topic
    optional string topic = 25;
    // the session agenda
    optional string agenda = 26;
    // copied from Board.isnote for meet in binder to distinguish note from meet
    optional bool isnote = 27;
    // is private meeting that only invitee can join in
    optional bool is_private = 28;

    // current page of of the session (one and only in session)
    optional ActionPageSwitch page_switch = 30;
    // the laser pointer position in the session
    optional ActionLaserPointer laser_pointer = 40;
    // user pointers
    repeated ActionUserPointer user_pointer = 50;
    repeated ActionUserRoster team_roster = 55;
    // users in the session
    repeated ActionUserRoster user_roster = 60;
    repeated ActionUserRosterKeepAliveInfo keepalive_info = 61;
    // meet log in a seperate object
    repeated ActionUserRosterEvent events = 62;
    // output only field when list user live sessions
    optional uint64 total_rosters = 65;
    repeated ActionVideoState video_state = 70;
    optional AudioConf audio_conf = 80;
    // the url host when make sip call
    //optional string sip_url_host = 81;
    // the Twilio CallSid of sip gateway
    //optional string sip_gateway_callsid = 82;
    // the availability zone of the session, it is assigned when session started
    // audio/ds server will be assigned based on the zone
    optional string zone = 83;

    // exceed meet duration limit
    optional bool is_expired = 90;

    // lock meet flag
    optional bool is_locked = 100;
    // when the meet time is modified after creation
    optional uint64 last_modified_time = 103;

    optional uint64 revision = 110;
    // for partial subscription data, the base revision which the partial data can be applied on
    // this is a output only field
    //optional uint64 previous_revision = 111;
    // if the session is ended
    optional bool is_deleted = 120;
    // ActionObject maybe reused when restart an ended session
    // on client side, if ActionObject.revision < last_deletion_revision, it need clear the ActionObject before merge changes into
    optional uint64 last_deletion_revision = 121;
    optional uint64 local_revision = 130;
    optional string assignments = 131;

    // the server timestamp when send this object to client
    optional uint64 timestamp = 200;

    // desktop share info
    optional DesktopShareConf ds_conf = 300;
    repeated DesktopShareState ds_state = 310;

    // session recording state
    repeated SessionRecordingState recording_state = 350;
    // session audio state
    repeated SessionAudioState audio_state = 360;
    // session video state
    repeated SessionVideoState session_video_state = 370;

    optional TelephonyConf telephony_conf = 380;

    // session scheduler information, non recurring session only
    // invitees are in board.users
    optional uint64 scheduled_start_time = 400;
    optional uint64 scheduled_end_time = 410;
    // the session status
    optional SessionStatus session_status = 420;
    // the session start/end time
    optional uint64 start_time = 430;
    optional uint64 end_time = 440;
    // JBH setting, allow attendees to join before session start
    optional uint64 milliseconds_allowed_to_join_before_start = 450;
    // indicate whether to recording multiple video channel
    optional bool record_multiple_video_channel = 460;
    // indicate whether to auto start recording
    optional bool auto_recording = 470;
    optional bool enable_ringtone = 480;
    // milliseconds to remind before an event, -1 means do not remind
    optional int64 reminder_interval = 485;

    optional VendorServiceType vendor_service_type = 490;
    optional string vendor_start_url = 491;
    optional string vendor_join_url = 492;
    optional string vendor_meet_id = 493;
    optional string vendor_service_owner = 494;
    optional string vendor_occurrence_id = 495;

    // the session recording video sequence in board resource table
    // there is only one recording for one session
    optional uint64 recording = 500;
    // the session transcription sequence in board resource table
    optional uint64 transcription = 502;
    // the session transcription VTT sequence in board resource table
    optional uint64 transcription_vtt = 503;
    // the session AI transcription summary in text
    optional uint64 meet_summary = 504;
    optional bool meet_summary_edited = 506;

    // the session active speaker resource sequence in board resource table
    optional uint64 audio_speaker = 510;
    optional uint64 meet_chat = 520;

    // the recurring session fields
    optional string timezone = 600 [default = "America/Los_Angeles"];
    optional uint64 dtstart = 610;
    optional string rrule = 620;
    optional string exdate = 621;

    optional string location = 630;

    // meet event size, used to limit total events could be logged
    optional uint64 total_events = 700;

    // the active speaker's roster id
    optional string active_speaker_id = 800;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

// desktop share conf
message DesktopShareConf {
    optional string server_url = 6;
    optional string server_addr = 10;
    optional string server_ip = 15;
    optional uint64 port = 20;
    optional string conf_id = 30;
    optional string token = 40;
    // when ds server is down and no replacement is available
    optional bool is_deleted = 50;

    //optional string recording_server_addr = 60;

    optional string client_uuid = 101;
    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
    optional uint64 revision = 1020;
    optional uint64 sequence = 1030;
}

enum DesktopShareStatus {
    DS_STOPPED = 0;
    DS_STARTED = 10;
    DS_PAUSED = 20;
    DS_RESUMED = 25;
    DS_REMOTECONTROL = 30;
}

message DesktopShareState {
    // user roster id who made the last status change
    optional string id = 10;
    optional bool is_deleted = 20;
    optional uint64 page_sequence = 30;
    // string format of page_sequence, server will not handle/store
    optional string ds_id = 35;
    optional DesktopShareStatus status = 50;
    optional bool is_annotation_enabled = 60;
    optional bool is_cobrowsing = 70;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 200;
    optional uint64 revision = 1020;
    optional uint64 sequence = 1030;

    optional string assignments = 1200;
}

enum SessionRecordingStatus {
    RECORDING_STOPPED = 0;
    RECORDING_STARTED = 10;
    RECORDING_PAUSED = 20;
    RECORDING_RESUMED = 30;
    // the recording is saved, with destination_board_id and recording_name set
    // job server will convert recording to video
    RECORDING_SAVED = 40;
    // we do the convert as long as there is a RECORDING_STARTED
    // however, if user do not want to record after started, use RECORDING_CANCELLED to cancel the effect of the RECORDING_STARTED
    RECORDING_CANCELLED = 50;
}

message SessionRecordingState {
    // user roster id who made the action
    optional string id = 10;
    optional SessionRecordingStatus status = 20;

    // the destination board the recording will be saved to
    optional string destination_board_id = 30;
    // the name will be used to save the recording
    optional string recording_name = 31;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
    optional uint64 revision = 1020;
    optional uint64 sequence = 1030;
}

enum SessionAudioStatus {
    AUDIO_STOPPED = 0;
    AUDIO_STARTED = 10;
}

message SessionAudioState {
    // user roster id who made the action
    optional string id = 10;
    optional SessionAudioStatus status = 20;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
    optional uint64 revision = 1020;
    optional uint64 sequence = 1030;
}

enum SessionVideoStatus {
    SESSION_VIDEO_STOPPED = 0;
    SESSION_VIDEO_STARTED = 10;
}

message SessionVideoState {
    // user roster id who made the action
    optional string id = 10;
    optional SessionVideoStatus status = 20;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
    optional uint64 revision = 1020;
    optional uint64 sequence = 1030;
}

message CapacityReport {
    optional string id = 1;

    repeated AudioServer audioservers = 10;
    repeated DesktopShareServer dsservers = 20;
    repeated VideoServer videoservers = 30;
    repeated PbxServer pbxservers = 40;

    optional uint64 revision = 110;
	//optional uint64 previous_revision = 111;
    optional bool is_deleted = 120;
    optional uint64 local_revision = 130;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message AudioServer {
    optional string availability_zone = 5;
    optional string server_url = 6;
    // server ip address
    optional string server_addr = 10;
    // actual dns name for ipv6 support
    optional string server_name = 15;
    optional uint64 port = 21;
    optional uint64 tcp_port = 22;
    // total rosters/connections the audio server can serve
    optional uint64 capacity = 31;
    optional uint64 keepalive_timestamp = 40;

    // audio server cores
    repeated AudioServerCore cores = 45;

    // edge audio server addresses
    repeated AudioEdgeServer edge_servers = 50;

    // if not empty, use recording_server_addr to get recording data instead of https://{server_addr}/lites3
    // the format is like: https://**************:8443/lites3/
    optional string recording_server_addr = 60;
    // like udp://**********:5000 for sip gateway to connect as sip gateway inside intranet may not able to connect to external address
    optional string internal_server_addr = 62;
    optional string fingerprint = 70;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional uint64 local_revision = 130;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message AudioServerCore {
    // sessions served by the core
    optional uint64 total_sessions = 20;
    // rosters/connections of the meet served by the core
    optional uint64 total_rosters = 30;

    optional uint64 sequence = 100;
    // core name in client_uuid
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message DesktopShareServer {
    optional string availability_zone = 5;
    optional string server_url = 6;
    // ds server dns, required for ssl connection verification
    optional string server_addr = 10;
    // ip address, if provided by ds server report
    optional string server_ip = 15;
    optional uint64 port = 21;
    optional uint64 capacity = 31;
    optional uint64 total_sessions = 32;
    optional uint64 total_rosters = 33;
    optional uint64 keepalive_timestamp = 40;

    // if not empty, use recording_server_addr to get recording data instead of https://{server_addr}/lites3
    // the format is like: https://**************:8443/lites3/
    optional string recording_server_addr = 60;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message VideoServer {
    optional string availability_zone = 5;
    optional string server_addr = 10;
    optional uint64 port = 21;
    optional uint64 capacity = 31;
    optional uint64 keepalive_timestamp = 40;

    // if not empty, use recording_server_addr to get recording data instead of https://{server_addr}/lites3
    // the format is like: https://**************:8443/lites3/
    optional string recording_server_addr = 60;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message PbxServer {
    optional string server_addr = 10;
    optional uint64 port = 20;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

// =============================================
// Telephony Domain
// =============================================

message TelephoneNumber {
    // the formated number for display, like +1(408)863-0100
    optional string number = 10;
    // the number location and/or country, like United States
    optional string location = 20;
    // the number without formating +( )-, like 14088630100
    optional string plain_number = 30;

    // voice prompt resource on different scenario
    optional uint64 prompt_meetid = 100;
    optional uint64 prompt_participantnumber = 110;
    optional uint64 prompt_joined = 120;
    optional uint64 prompt_left = 130;
    optional uint64 prompt_ended = 140;
    optional uint64 prompt_retrymeetid = 150;
    optional uint64 prompt_invalidmeetid = 160;
    optional uint64 prompt_notinprogress = 170;
    optional uint64 prompt_noinput = 180;
    optional uint64 prompt_goodbye = 190;
    // personal meeting room wait for host to join/approve
    optional uint64 prompt_waiting = 200;
    // personal meeting room wait timeout or host decline
    optional uint64 prompt_decline = 210;

    // prompt to participant that he is muted
    optional uint64 prompt_muted = 220;
    // prompt to participant that he is unmuted
    optional uint64 prompt_unmuted = 221;
    // prompt to let participant know how to mute or unmute
    optional uint64 prompt_mute_instruction = 222;

    // prompt if meet need a password and participant number isn't verified
    optional uint64 prompt_password = 230;
    // prompt if the password is wrong
    optional uint64 prompt_password_invalid = 231;
    // prompt if the password is wrong three times
    optional uint64 prompt_password_try_later = 232;

    optional uint64 prompt_recording = 240;

    optional uint64 prompt_first_attendee = 250;

    // resources for voice prompt
    repeated UserResource resources = 300;

    optional bool is_default = 400;

    optional uint64 sequence = 1000;
    optional string client_uuid = 1010;
    optional uint64 revision = 1020;
    optional bool is_deleted = 1030;
    optional uint64 local_revision = 1130;
    optional uint64 created_time = 1040;
    optional uint64 updated_time = 1050;

    optional string assignments = 1200;
}

enum TelephonyDomainSmsProviderType {
    SMS_PROVIDER_INVALID = 0;
    SMS_PROVIDER_TWILIO = 10;
    //SMS_PROVIDER_AWS_SNS = 20;
}

message TelephonyDomainSmsProvider {
    optional TelephonyDomainSmsProviderType type = 10;

    // twilio sms settings
    optional string twilio_api_url = 100;
    optional string twilio_account_sid = 110;
    optional string twilio_auth_token = 120;
    optional string twilio_from_number = 130;

    // aws sns settings
    //optional string aws_sns_api_url = 200;
    //optional string aws_sns_region = 210;
    //optional bool aws_sns_use_ec2_role = 220;
    // if not use ec2 role, access_key and secret are required
    //optional string aws_sns_access_key = 230;
    //optional string aws_sns_secret = 240;
}

// local field for TelephonyDomain
message TelephonyDomainPartner {
    optional Partner partner = 10;
}

message TelephonyDomain {
    // GUID generate from server side to represent the id of the telephone domain
    optional string id = 10;

    // the name of the telephone domain
    optional string name = 20;

    // the description of the telephone domain
    optional string description = 30;

    // the numbers in the telphone domain
    repeated TelephoneNumber numbers = 100;

    // sms provider
    optional TelephonyDomainSmsProvider sms_provider = 110;

    // if the domain is partner owned, fill in partner id
    optional TelephonyDomainPartner partner = 200;

    optional string client_uuid = 1010;
    optional uint64 revision = 1020;
    optional bool is_deleted = 1030;
    optional uint64 local_revision = 1130;
    optional uint64 created_time = 1040;
    optional uint64 updated_time = 1050;
}

// =============================================
// Agent files and folders, and requests
// =============================================

message CacheMessage {
    optional string source_id = 10;
    optional string source_session_id = 20;

    optional string destination_id = 100;
    optional string destination_session_id = 110;

    optional string sequence = 150;
    optional uint64 timestamp = 155;
    optional uint64 updated_time = 160;

    optional CacheObject object = 200;
    // optional Entry entry = 300;

    // DEPRECATED: use apn_payload_json instead
    //optional ApplePushNotificationPayload apn_payload = 400;
    optional string apn_payload_json = 410;
    optional GCMPushNotificationData gcm_payload = 450;
}

message CacheObjectChange {
    optional CacheObject latest_change = 10;
}

// S3 Storage Object, user, board, group, etc...
message CacheObject {
    optional User user = 10;
    optional Group group = 20;
    optional Partner partner = 21;
    optional SystemSamlService saml_service = 22;
    optional TelephonyDomain telephony_domain = 23;
    optional Board board = 30;
    optional WebApp webapp = 40;
    optional ObjectRecording recording = 50;
    optional UsageStatistics usage = 60;
    optional CapacityReport audio_report = 70;
    // desktop share server capacity report
    optional CapacityReport ds_report = 71;
    // video server capacity report
    optional CapacityReport video_report = 72;
    // server list in server cluster
    // optional ClusterConfig cluster_config = 75;
    // system configuration
    optional SystemConfig system_config = 77;

    optional ActionObject session = 80;
    // for playback
    optional AudioRecording audio = 90;

    //for user presence, between biz and presence
    optional Presence presence = 100;
    //between client and biz
    optional Contacts contacts = 101;

    // repeated ServerProbe probes = 200;

    // org management structure, server internal use only
    // optional ManagementGroup management_group = 300;

    // CacheObject cannot be deleted, thus there is no is_deleted field
    optional uint64 revision = 110;
    optional uint64 previous_revision = 111;
    optional uint64 updated_time = 120;
    // local_revision is required to be a container
    optional uint64 local_revision = 130;

    // activity logs recorded by biz layer
    repeated ActivityLog activity_logs = 1000;
    optional ActivityStatistics activity_stats = 1010;
    // SDK use only, to keep offline changes
    repeated ClientRequest local_changes = 3000;
    // used to append changes to cache object
    repeated CacheObjectChange latest_changes = 4000;

    // SDK use only, when used to save object change
    optional string request_id = 5000;
    // SDK use only, when used to save offline internal sdk request to disk
    //repeated LocalRequest local_requests = 5100;
    // repeated PFlatBTree btrees = 8000;
}

// =============================================
// definination for presence
// =============================================

//biz to client, output by user id subscription channel
message Contacts {
    //User:{id,online}
    repeated User contacts = 10;
    optional uint64 revision = 110;
    optional uint64 previous_revision = 120;

    optional string previous_server = 200;
    optional uint64 previous_timestamp = 210;

    optional uint64 updated_time = 1010;
}

//between biz and presence
message Presence {
    //biz update user connection status
    //{id,online}
    optional User issuer = 10;
    //user connection id
    optional string connection_id = 20;

    //presence report user status to biz, also read presence result
    //User:{id,online}
    //also biz batch subsribe its contact status
    //User:{id}
    repeated User buddies = 100;
    optional uint64 uptime = 150;
}

// =============================================
// HttpRequest/HttpResponse to hold request/response to/from external http servers
// =============================================

message HttpHeader {
    optional string name = 10;
    optional string string_value = 20;
}

// =============================================
// User and UserGroup
// =============================================

// sequence-revision control on objects and its child objects:
// sequence: unique and auto-increase number in the scope of the root object (User or Board), root object itself do not have a sequence
// revision: maximum value of the sequence/revision number of all child objects inside the container. This is to detect the changes of the object for in merge process.
// local_revision: the revision of local fields which doesn't have its own sequence/revision. Leaf object do not have a local_revision
// is_deleted: if the object/subject is deleted, deleted object/sub-object will never be un-deleted

enum UserType {
    option allow_alias = true;

    USER_TYPE_NORMAL = 0;
    //to avoid using non-equal or not in for listing org member
    USER_TYPE_INVALID = 1;

    USER_TYPE_DEVICE = 10;
    //USER_TYPE_FACEBOOK = 20;
    USER_TYPE_AGENT = 30;
    //USER_TYPE_FORCE = 40;
    // SSO user are identified by unique id (unique in a group) + group id
    USER_TYPE_SSO = 50;
    // LDAP user is for on-premise only and all LDAP people becomes corporate directory
    //USER_TYPE_LDAP = 51;
    // sina weibo user
    //USER_TYPE_WEIBO = 60;
    // Tencent qq user
    //USER_TYPE_TENCENT = 70;
    //USER_TYPE_GOOGLE = 80;
    //USER_TYPE_GOOGLE_V3 = 81;
    // Service type user, set in BoardViewToken.actor_file_as and used in ObjectFeed.actor
    USER_TYPE_SERVICE = 90;
    // Service type bot user, set in BoardViewToken.actor_file_as and used in ObjectFeed.actor
    USER_TYPE_BOT = 100;
    // WebApp user to represent WebApp in an org
    USER_TYPE_WEBAPP = 100;
    // the opposite of users with global unique emails.
    USER_TYPE_LOCAL = 120;
}

//enum UserDesignation {
//  USER_DESIGNATION_INVALID = 0;
//  USER_DESIGNATION_INTERNAL = 10000;
//  // the big gap is for future sub-types.
//  // designation <= USER_DESIGNATION_INTERNAL will all be internal users.
//  USER_DESIGNATION_EXTERNAL = 20000;
//}

enum UserOSType {
    OS_TYPE_WINDOWS = 40;
    OS_TYPE_WINDOWS_XP = 0;
    OS_TYPE_WINDOWS_VISTA = 10;
    OS_TYPE_WINDOWS_7 = 20;
    OS_TYPE_WINDOWS_8 = 30;

    OS_TYPE_MAC = 100;
    OS_TYPE_MAC_10_6 = 110;
    OS_TYPE_MAC_10_7 = 120;
    OS_TYPE_MAC_10_8 = 130;
    OS_TYPE_MAC_10_9 = 140;
    OS_TYPE_MAC_10_10 = 150;

    OS_TYPE_IOS = 200;
    OS_TYPE_ANDROID = 210;

    OS_TYPE_CLOUD = 300;
}

enum NotificationLevel {
    // all notifications
    NOTIFICATION_LEVEL_ALL = 0;
    // notifications related to me
    NOTIFICATION_LEVEL_RELATED = 10;
    // no notification
    NOTIFICATION_LEVEL_NOTHING = 20;
}

enum UserResourceType {
    USER_RESOURCE_ORINGINAL = 0;
    USER_RESOURCE_PICTURE = 10;
    USER_RESOURCE_PICTURE2x = 20;
    USER_RESOURCE_PICTURE4x = 30;
    USER_RESOURCE_CSV_IMPORT_INPUT = 40;
    USER_RESOURCE_CSV_IMPORT_OUTPUT = 50;
    USER_RESOURCE_STAMP = 60;
}

// external resource/file for the user
message UserResource {
    // resource name
    optional string name = 10;

    // resource type for client use only
    optional UserResourceType type = 20;

    // Resource mime-type
    optional string content_type = 21;

    // Resource content-lenght
    optional uint64 content_length = 22;

    // base16 sha256 hash of the file
    optional string sha256_hash = 29;
    // base16 md5 hash of the file, which is used to identify the file in storage
    optional string hash = 30;

    // for facebook profile image, the original url the image was obtained from
    optional string original_url = 40;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message UserTag {
    optional string name = 10;
    optional string string_value = 20;
    optional uint64 uint64_value = 30;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message UserBoard {
    // if status is BOARD_VISITED, board has a snapshot of cover page
    // else only board id
    optional Board board = 10 [lazy = true];
    optional BoardUserStatus status = 20;
    // only user owned board are tracked in UserBoard.type
    optional BoardAccessType type = 25;
    // point to the sequence of UserBoardCategory
    optional uint64 category = 30;
    // point to the client_uuid of UserBoardCategory
    optional string category_uuid = 31;
    // string format of float number to indicate board display order in certain category
    optional string order_number = 40;
    // default binder of the user is the last non-deleted binder with is_default set
    optional bool is_default = 50;
    // group binder of user's own group, which user is member of
    optional bool is_group = 55;

    // NOT in USE: feed earlier don't display in client, it will compare with feed sequence
    //optional uint64 feed_hidden_index = 57;
    // feed unread count
    optional uint64 feed_unread_count = 58;
    // first unread feed sequence
    optional uint64 first_unread_feed_sequence = 59;

    // last accessed time
    optional uint64 accessed_time = 60;
    // NOT in USE: last chat email timestamp
    //optional uint64 last_email_timestamp = 61;
    // user marked unread feed's created time
    optional uint64 first_unread_feed_timestamp = 63;
    // action item enabled time
    optional uint64 enabled_time = 62;
    optional uint64 dismissed_time = 64;

    // mark as a favorite board
    optional bool is_favorite = 70;
    // mark as archived, will be reset automatically on update member job
    // MOVED to UserBoardCategory
    optional bool is_archive = 71;
    optional uint64 archived_time = 72;

    // for group member who is not board member,
    // save this flag in UserBoard instead of BoardUser
    // flag to turn off push notification
    // deprecated, use push_notification_level instead
    optional bool is_notification_off = 80;
    optional NotificationLevel push_notification_level = 81;

    // signatures that it is current user's turn to sign
    optional uint64 waiting_signatures = 90;

    // When reading user session, server may output multiple UserBoards
    // for the same recurring session.
    // In this case, fill original_sequence instead of sequence
    optional uint64 original_sequence = 200;

    optional uint64 sequence = 100;
    // client_uuid format: Board.id-BoardUser.sequence
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message UserBoardCategory {
    optional string name = 10;
    // boards in this category are archived
    optional bool is_archive = 30;

    optional string order_number = 90;

    optional uint64 sequence = 100;
    optional string client_uuid = 20;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message UserToken {
    optional string token = 10;
    optional string apple_device_token = 20;
    optional string apple_voip_token = 21;
    optional uint64 client_version = 30;
    optional string android_device_token = 40;
    // use 3rd party notification service if set vendor
    optional uint64 vendor = 41;
    // additional info in vendor notification payload
    optional string vendor_ext = 42;
    // oauth access token
    optional bool is_access_token = 50;
    // oauth refresh token
    optional bool is_refresh_token = 51;
    // related access token sequence from this refresh token
    // related token duplicated from this regular token
    optional uint64 access_token = 52;
    optional string client_ua = 60;
    optional string client_accept_language = 70;
    // when access token was created, specify which app generated this token
    optional string client_id = 80;

    // fields to save user id and token sequence in apple/gcm device token mapping
    optional string uid = 200;
    optional uint64 token_sequence = 210;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message UserQRToken {
    optional string token = 10;
    optional BoardActor creator = 20;
    optional uint64 last_updated_time = 30;
    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message UserAgent {
    // only agent id and unique id filled
    optional User agent = 10 [lazy = true];

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum UserContactStatus {
    // established contact relationship when other party accepted
    CONTACT_NORMAL = 0;
    // inviting a user to be a contact
    CONTACT_PENDING = 100;
    // be invited to be a contact
    CONTACT_INVITED = 200;
}

message UserContact {
    optional User user = 10 [lazy = true];
    optional Group group = 15 [lazy = true];
    optional UserContactStatus status = 20;
    // if the user has push notification enabled
    // if true, user are considered online via push notification
    // UserContact.User already contains has_push_notification, set it here to keep compatible with the old version client
    optional bool has_push_notification = 30;
    // is a contact from private conversation
    optional bool is_private = 50;

    // indicate a user joined from team
    optional bool is_from_team = 60;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    // mark field to keep content on deletion, do not fill
    optional bool keep_deleted = 121;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum UserRole {
    // normal user
    USER_ROLE_NORMAL = 0;
    // user can probe server status and read crash reports
    USER_ROLE_CRASH_REPORT_READ = 10;
    // user can probe server status and read crash reports
    USER_ROLE_SERVER_STATUS_READ = 100;
    // user can use superadmin functionality with readonly access
    USER_ROLE_SUPERADMIN_READONLY = 140;
    // user can use superadmin functionality
    USER_ROLE_SUPERADMIN = 150;
    // user can read objects
    USER_ROLE_OBJECT_READ = 200;
    // user can change objects
    USER_ROLE_OBJECT_WRITE = 300;
}

enum UserLevel {
    USER_LEVEL_FREE = 0;
    USER_LEVEL_TRIAL = 10;
    USER_LEVEL_PRO = 20;
    // BETA user are connected to moxtra beta site
    USER_LEVEL_BETA = 100;
}

message UserCap {
    optional uint64 user_boards_max = 10;
    optional uint64 open_flow_boards_max = 12;
    optional uint64 board_users_max = 20;
    optional uint64 board_pages_max = 30;
    // board feed history max in ms
    optional uint64 board_history_max = 31;
    // max board users before stop send push notification
    optional uint64 board_notification_max = 32;
    // meet attendee max
    optional uint64 session_users_max = 40;
    // storage limit in bytes
    optional uint64 user_cloud_max = 50;

    // NOT IN USE: monthly upload max in bytes
    //optional uint64 monthly_upload_max = 60;
    // NOT IN USE: monthly upload used in bytes for this period
    //optional uint64 monthly_upload_current = 61;
    // NOT IN USE: monthly upload period left in ms before reset
    //optional uint64 monthly_upload_time_left = 62;
    // max upload size in bytes
    optional uint64 client_max_body_size = 69;

    // file extensions allowed to upload
    repeated string client_allowed_file_types = 71;

    // meet duration in milliseconds
    optional uint64 meet_duration_max = 70;
    // meet recording
    optional bool allow_meet_recording = 72;
    // meet VoIP
    optional bool allow_meet_voip = 74;
    // meet telephony
    optional bool allow_meet_telephony = 76;

    // remote desktops max
    optional uint64 user_agent_max = 80;

    // max amount of integrations user might have across all binders he owned
    optional uint64 user_integrations_max = 84;

    // require email domain suffix from Partner on SSO email domain lock
    // NOT IN USE
    //optional bool require_domain_suffix = 90;

    optional uint64 group_boards_max = 100;
    optional uint64 team_users_max = 110;

    optional uint64 user_relations_max = 200;
    // allow to automatically archive board
    // optional bool allow_board_archive = 210;
}

message UserFavorite {
    optional Board board = 10 [lazy = true];
    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message UserMentionMe {
    optional Board board = 10 [lazy = true];
    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum CallType {
    CALL_TYPE_INVALID = 0;
    CALL_TYPE_OUTBOUND = 10;
    CALL_TYPE_INBOUND = 20;
}

enum CallStatus {
    CALL_STATUS_INVALID = 0;

    // in-progress status
    CALL_STATUS_INITIALIZED = 100;
    CALL_STATUS_RINGING = 110;
    CALL_STATUS_CONNECTING = 120;
    CALL_STATUS_CONNECTED = 130;

    // finished status
    CALL_STATUS_CANCELLED = 200;
    CALL_STATUS_NOANSWER = 210;
    CALL_STATUS_DECLINED = 220;
    CALL_STATUS_ENDED = 230;
    CALL_STATUS_FAILED = 240;
}

enum ClientType {
    CLIENT_TYPE_INVALID = 0;
    CLIENT_TYPE_UC = 10;        // Moxtra UC Client
    CLIENT_TYPE_PHONE = 20;     // (SIP) Phone
}

message CallUser {
    optional User user = 10 [lazy = true];
    optional ClientType client_type = 20;

    // needed by outbound sip call
    //optional string org_id = 100;
    optional string sip_address = 110;

    // UserCallLog sequence in the User object
    optional uint64 call_sequence = 200;
}

message UserCallLog {
    // uuid, which is the same at peer side
    optional string call_id = 10;

    // basic info
    optional CallType type = 20;
    optional CallStatus status = 30;
    optional CallUser peer = 40;

    // start from connected
    optional uint64 start_time = 50;
    optional uint64 end_time = 60;

    // board id and session key of call(meet) object
    optional string board_id = 200;
    optional string session_key = 210;

    // local info, the peer has different one
    optional string session_id = 300;
    optional ClientType client_type = 310;
    optional string sip_call_id = 320;

    // keypad input after call connected
    // the field is to be used by append more digits on keypad input
    optional string dtmf_digits = 400;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

// ACD(Automatic Call Distribution) Data Structures
enum ACDType {
    ACD_TYPE_INVALID = 0;
    ACD_TYPE_CHAT = 10;
    ACD_TYPE_MEET = 20;
}

enum ACDStatus {
    ACD_STATUS_INVALID = 0;

    // in-progress status
    ACD_STATUS_INITIALIZED = 100;
    ACD_STATUS_QUEUED = 110;
    ACD_STATUS_CONNECTING = 120;
    ACD_STATUS_CONNECTED = 130;

    // finished status
    ACD_STATUS_CANCELLED = 200;
    ACD_STATUS_NOANSWER = 210;
    ACD_STATUS_DECLINED = 220;
    ACD_STATUS_ENDED = 230;
    ACD_STATUS_FAILED = 240;
}

message UserACDLog {
    // uuid, which is the same at peer side
    optional string call_id = 10;

    // basic info
    optional ACDType type = 20;
    optional ACDStatus status = 30;
    optional User peer = 40;

    // start from connected
    optional uint64 start_time = 50;
    optional uint64 end_time = 60;

    // board id and session key of call(meet) object
    optional string board_id = 200;
    optional string session_key = 210;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}
// END OF ACD(Automatic Call Distribution) Data Structures

message UserConnection {
    optional string session_id = 10;
    optional string connection_id = 20;
    optional uint64 updated_time = 100;
}

message DateRange
{
    optional uint64 start_time = 10;
    optional uint64 end_time = 20;
}

message OutOfOfficeStatus {
    optional bool status = 10; // set in ooo job
    optional uint64 start_time = 20;  // out of office start time
    optional uint64 end_time = 30; // out of office end time
    optional string message = 40; // max 256 bytes
    optional User backup = 50 [lazy = true];
    //recurring ooo
    repeated DateRange r_out_of_office = 60;
}

enum UserRelationStatus {
    RELATION_INIT = 0;
    RELATION_PENDING = 100;
    RELATION_NORMAL = 200;
}

message UserRelation {
    optional User user = 10 [lazy = true];
    optional UserRelationStatus status = 20;
    // random numeric code for setup connection with social client user
    optional string invitation_code = 30;
    optional string line_invitation_code = 31;
    optional string whatsapp_invitation_code = 35;
    // whatsapp chat id
    optional string whatsapp_chat_id = 32;
    optional string whatsapp_chat_status = 33;
    optional string whatsapp_invitation_url = 34;

    // last invited time
    optional uint64 invited_time = 80;
    optional string order_number = 90;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message BotUserRelation {
    optional User user = 10 [lazy = true];
    optional UserRelationStatus status = 20;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum HashAlgorithm {
    HASH_ALGORITHM_SHA256 = 0;
    HASH_ALGORITHM_PKCS5_PBKDF2_SHA256 = 10;
}

enum SignatureStyle {
    SIGNATURE_STYLE_NONE = 0;
    SIGNATURE_STYLE_PRESELECTED = 10;
    SIGNATURE_STYLE_DRAWN_ON_DEVICE = 20;
}

message UserDevice {
    optional string device_id = 10;
    optional string client_ua = 20;
    optional uint64 timestamp = 30;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message UserNotification {
    optional ApplePushNotificationPayload payload = 30;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message BoardMemberNotificationSetting {
    optional uint64 not_join_within = 100;
    optional uint64 not_join_within_to_owner = 200;
}

message ActionNotificationSetting {
    optional bool on_due = 100 [default = true];
    optional uint64 before_due = 200;
    optional uint64 after_due = 300;
    optional uint64 after_due_repeat = 310;
}

// for board due only
message BoardNotificationSetting {
    optional bool on_due = 100 [default = true];
    optional uint64 before_due = 200;
    optional uint64 after_due = 300 [default = 86400000];
    optional uint64 after_due_repeat = 310 [default = 100];
}

// S3 Storage Object (as part of CacheObject) with Hash_UserID as Key
// S3 Storage Object (as part of CacheObject) with Hash_Email as Key for email to user id mapping
message User {
	//Description The user's ID
	//Returns A JSON string
	optional string id = 10;
	//optional string migrate_from_id = 91;
	//optional string migrate_to_id = 92;

	//Description The user's full name
	//Returns A JSON string
	optional string name = 11;

	//Description The user's first name
	//Returns A JSON string
	optional string first_name = 12;

	//Description The user's last name
	//Returns A JSON string
	optional string last_name = 13;

    //Description The User's type
    // Return A JSON string
    optional UserType type = 14 [default = USER_TYPE_NORMAL];

    //Description The user's phone number
    //Returns A JSON string
    optional string phone_number = 15;

    //Description
    // online status, if type = USER_TYPE_AGENT
    // presence online status if normal user
    // server generated status
    optional bool online = 16;
    optional bool in_meet = 903;

    //Description The User's os type, if type = USER_TYPE_AGENT
    // Return A JSON boolean
    optional UserOSType os_type = 17;

    //Description If the user's been disable and cannot login
    optional bool disabled = 18;

    //Description If access to agent is passcode protected, if type = USER_TYPE_AGENT
    // Return A JSON boolean
    optional bool passcode_protected = 19;

	//Description The user's email, if type = USER_TYPE_NORMAL
	//Returns A JSON string
	optional string email = 20;

	//Description The user's facebook id, if type = USER_TYPE_FACEBOOK
	//Returns A JSON string
	//optional string facebook_id = 21;

	//Description The user's unique_id, if type = USER_TYPE_DEVICE or USER_TYPE_AGENT
	//Returns A JSON string
	optional string unique_id = 22;

	//Description The device's device token for push notification, input only field to update device token
	//Returns A JSON string
	optional string apple_device_token = 23;

	//Description The agent's delegate user id, if type = USER_TYPE_AGENT
	//Returns A JSON string
	optional string agent_user_id = 24;

	//Description The user's sales force id, if type = USER_TYPE_FORCE
	//Returns A JSON string
	//optional string force_id = 25;

    //Description The user's work phone number
    optional string work_phone_number = 26;

    //Description The user's sip configuration (json format of SIPConfig)
    //Returns A JSON string
    optional string sip_configuration = 27;

    //Description The user's work phone extension number
    optional string extension_phone_number = 28;

    optional string title = 29;

    // information for display only, can be used to display personal email, phone number, etc
    optional string display_email = 1600;
    optional string display_phone_number = 1610;

    // user display id
    optional string display_id = 1620;

    //optional UserDesignation designation = 1630;

    //Description The user's password
    //Returns empty
    optional string pass = 30;

    //Description The hashed value of user's password for persistence
    //Returns empty
    optional bytes hashed_pass = 31;
    optional HashAlgorithm pass_algorithm = 35;

    //Description The user's old password, input only field to change password
    //Returns empty
    optional string old_pass = 32;

    optional string address = 33;

    //User password set or not, output only
    optional bool email_onboarded = 34;

    //Description The user's token for output
    //Returns A JSON string
    repeated string tokens = 40;
    repeated UserToken user_tokens = 41;

    //User qr code tokens
    repeated UserQRToken qr_tokens = 42;

	//Description Email verification tokens
	//Returns A JSON string
	optional string email_verification_token = 50;

	//4 digits verification code when register new email account
	optional string email_verification_code = 51;
	//1. updated timestamp of email verification code
    //2. last login locked timestamp
	optional uint64 code_updated_time = 52;
	//failed attempts with email verification code registration or login
	optional uint64 failed_attempts = 53;
    optional uint64 last_login_timestamp = 54;
    repeated uint64 reset_password_timestamps = 55;
    repeated uint64 lookup_domain_timestamps = 56;
	//Description Readonly before email verified
	//Returns A JSON boolean
	optional bool email_verified = 60;
    optional uint64 token_updated_time = 61;

    //Description Profile picture name refer to resources table
    // different resolution of user profile pictures
    // points to the sequence number in resource table
    optional uint64 picture = 71;
    optional uint64 picture2x = 72;
    optional uint64 picture4x = 73;

    // client use only for temporaly store local path
    optional string picture_path = 74;
    optional string picture2x_path = 75;
    optional string picture4x_path = 76;

    // server saml use only for extern picture url, no store
    optional string picture_url = 77;

    //Description The device's device token for push notification, input only field to update device token
    //Returns A JSON string
    optional string android_device_token = 80;

    //Description The device's voip token for pushkit notification, input only field to update voip token
    //Returns A JSON string
    optional string apple_voip_token = 90;

    // use ios/android app id to find client_id when client update device token
    optional string ios_app_id = 91;
    optional string android_app_pkg_name = 92;

    //Description Boards user can access to
    repeated UserBoard boards = 101;

    //Description User personal meeting rooms
    repeated UserBoard personal_rooms = 150;

    //Description External profile images for the user
    repeated UserResource resources = 200;

    // tags attached to this user
    repeated UserTag tags = 220;

    // agents attached to this user
    repeated UserAgent agents = 230;

    // cloud entry
    // optional Entry cloud = 240;

    // user call log
    repeated UserCallLog call_logs = 250;

    // user acd log
    repeated UserACDLog acd_logs = 270;

    // user subscribed group boards
    repeated UserBoard group_boards = 280;

    //repeated UserFlow flows = 260;

    //Description The User's role
    // internal use only, not a storage field, for read from config file
    // Return A JSON enum
    optional UserRole role = 300;

    //Description The User's cap setting
    // Return A JSON Object
    optional UserLevel level = 310 [default = USER_LEVEL_FREE];

    //Description The User's cap setting
    // Return A JSON Object
    // output only, no store
    optional UserCap cap = 320;

    //Description The User's used cloud space
    // It is calculated when user logged in, and will not be saved
    optional uint64 total_cloud_size = 330;
    optional uint64 boards_owned = 340;
    optional uint64 boards_invited = 341;
    optional uint64 boards_total = 342;
    optional uint64 boards_owned_pages = 343;
    optional uint64 boards_owned_comments = 344;
    optional uint64 boards_owned_todos = 345;
    optional uint64 boards_owned_invitees = 346;
    optional uint64 meet_hosted = 350;
    optional uint64 meet_invited = 351;
    optional uint64 contacts_total = 360;
    // exception: relations_total are maintained in User object when user has relationship changes
    optional uint64 relations_total = 361;
    optional uint64 feed_unread_total = 362;
    optional uint64 feed_unread_sr = 363;
    optional uint64 agents_total = 370;
    optional uint64 accessed_time = 380;

    //Last archive user object job's execution time
    optional uint64 last_archive_time = 390;

    //User last active timestamp, output only
    optional uint64 last_active_time = 391;

    //Description Groups user belongs to
    repeated UserGroup groups = 400;

    //Description WebApps user registered for
    repeated UserWebApp webapps = 410;

    //Description in User Object: Partners user own and manage
    // First partner is primary partner, which was used when create a group
    // in Email Object: partners that user can do single sign on
    repeated UserPartner partners = 420;

    // user managed teams
    repeated UserGroup managed_teams = 430;

    repeated UserGroup collab_teams = 440;

    //Description Categories user defined
    repeated UserBoardCategory categories = 500;

    // Aggregated Board feeds for output only
    repeated ObjectFeed feeds = 600;

    // user settings
    optional bool enable_notification_emails = 701 [default = true];
    optional bool enable_digest_email = 702 [default = true];
    optional uint64 last_digest_email_timestamp = 703;
    // user time zone
    optional string timezone = 710 [default = "America/Los_Angeles"];

    // output only, read from http request header
    // NOTE: it is now an updatable field
    optional string language = 720 [default = "en"];

    //optional string preferred_zone = 730;

    repeated UserContact contacts = 900;
    // presence: status and message
    // user defined status
    optional uint64 customized_presence_status = 901; // 0 is preserved
    //if in ooo,showing ooo_message, or else showing presence message, after ending ooo, clear ooo_message
    optional string customized_presence_message = 902;

    // if the user has push notification enabled
    // if true, user are considered online via push notification
    // deprecated, the online status is removed at client side
    optional bool has_push_notification = 910;

    repeated UserContact collaborators = 930;

    // user connection in biz and provide this infomation by presence server
    repeated UserConnection connections = 950;

    // relationship between internal and external user
    repeated UserRelation relations = 960;

    // relationship between bot user and external user
    repeated BotUserRelation bot_relations = 961;

    optional NotificationLevel board_notification_level = 920;
    optional NotificationLevel session_notification_level = 921;
    optional BoardMemberNotificationSetting board_member_notification_settings = 922;
    optional ActionNotificationSetting action_notification_settings = 923;
    optional BoardNotificationSetting board_notification_settings = 924;

    repeated UserFavorite favorites = 1300;
    repeated UserMentionMe mentionmes = 1310;

    repeated UserBoard action_items = 1320;
    optional uint64 action_accessed_time = 1321;

    repeated UserNotification notifications = 1330;
    optional uint64 notification_accessed_time = 1331;

    // broadcast channels for sending broadcast message to relation users
    repeated UserBroadcast broadcasts = 1350;

    // represent new broadcast
    repeated UserBoard ext_broadcasts = 1360;

    // group user usage info, see above
    //optional uint64 total_boards = 1110;
    //optional uint64 total_pages = 1120;
    //optional uint64 total_meets = 1130;

    // use extra effort to keep the object short, for example, remove all boardusers in userboard
    optional bool keep_short = 1200;

    //Description The user's sip registration status, true means registered successfully
    // available through presence report
    optional bool sip_registration_status = 1400;
    //Description The user's sip registration status message, if it fails to register to PBX
    // available through presence report
    optional string sip_registration_message = 1410;
    optional uint64 sip_unread_voice_mail_count = 1420;
    // some VM system do not provide unread count, bool value to indicate if there is a voice mail
    optional bool sip_has_unread_voice_mail = 1421;

    // different user signatures
    // points to the sequence number in resource table
    optional uint64 signature = 1500;
    optional uint64 initials = 1501;
    optional string initials_text = 1502;
    optional string legal_name = 1503;
    optional SignatureStyle signature_style = 1504;

    // client use only for temporaly store local path
    optional string signature_path = 1510;
    optional string initials_path = 1511;

    // update as a bundle
    optional OutOfOfficeStatus out_of_office = 1521;

    // pending new email and verification token after user email has been changed
    // optional string pending_email = 1530;

    // optional string pending_email_verification_token = 1531;

    // social type, WeChat/WhatsApp etc
    optional string social = 1700;
    optional string social_id = 1701;
    optional string social_avatar = 1702;
    // this field represents customize status
    optional string social_status = 1703;

    // random numeric code for setup connection with social client user
    optional string invitation_code = 1704;
    // group integration sequence
    optional uint64 connector = 1705;
    // social status last updated time
    optional uint64 social_updated_time = 1706;


    // line social id
    optional string line_social_id = 1711;
    // line social avatar
    optional string line_social_avatar = 1712;
    // line social channel status
    optional string line_social_status = 1713;

    // line invitation code
    optional string line_invitation_code = 1714;
    // group integration sequence
    optional uint64 line_connector = 1715;

    // whatsapp social id
    optional string whatsapp_social_id = 1721;
    // whatsapp social avatar
    optional string whatsapp_social_avatar = 1722;
    // whatsapp social status
    optional string whatsapp_social_status = 1725;

    // whatsapp invitation code
    optional string whatsapp_invitation_code = 1723;
    optional uint64 whatsapp_connector = 1724;

    // custom saml fields for verify token output
    optional string custom1 = 1801;
    optional string custom2 = 1802;
    optional string custom3 = 1803;

    repeated UserDevice user_devices = 2001;

    repeated UserBoard flow_templates = 2200;

    repeated RoutingWeekday weekdays = 2210;
    repeated RoutingSpecialDay special_days = 2211;
    optional uint64 meeting_buffer_time = 2220;

    repeated UserBoard self_service_templates = 2300;

    // User object is a main object and it doesn't have a sequence
	optional uint64 revision = 110;
    // for partial subscription data, the base revision which the partial data can be applied on
    // this is a output only field
	//optional uint64 previous_revision = 111;
    optional bool is_deleted = 120;
	optional uint64 local_revision = 130;
    optional string assignments = 131;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

enum GroupUserStatus {
    // user has no relationship with the group
    GROUP_INVALID = 0;
    //don't use value 1, log server use it as deactive user
    //GROUP_MEMBER_DEACTIVE = 1;
    // user requested to join the group and wait for approval
	//GROUP_MEMBER_REQUESTED = 20;
    // invited status is for email user without unique id set
    // without id: user are invited to join the group and wait for accept
    // starting from v6, user with id: user are created for the org, password not set yet
	GROUP_INVITED = 30;
    // user are the group member; user with unique_id are created with group member initially
    GROUP_MEMBER = 40;
}

enum GroupAccessType {
    GROUP_NO_ACCESS = 0;
    GROUP_READONLY_ACCESS = 50;
    // read access
    GROUP_MEMBER_ACCESS = 100;
    GROUP_ADMIN_READONLY_ACCESS = 150;
    GROUP_ADMIN_ACCESS = 200;
    GROUP_OWNER_ACCESS = 300;
}

message UserBroadcast {
    optional string title = 10;
    repeated UserContact contacts = 20; // contact users in boardcast, only user id is set
    optional uint64 msg_count = 30; // how many messages have been sent of this broadcast
    optional uint64 msg_sent_timestamp = 40; // last message sent timestamp of this broadcast
    optional uint64 last_modified_timestamp = 50; // last modified timestamp of this boardcast, update when title/contacts changes
    optional bool is_default = 60; // indicate default broadcast
    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 130;
    optional uint64 local_revision = 140;
    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum BroadcastStatus {
    BROADCAST_QUEUED = 0;
    BROADCAST_SCHEDULED = 10;
    BROADCAST_PROCESSING = 20;
    BROADCAST_COMPLETED = 30;
}

enum BroadcastChannel {
    BROADCAST_CHANNEL_WORKSPACE = 0;
    BROADCAST_CHANNEL_INBOX = 10;
}

enum BroadcastTarget {
    BROADCAST_TARGET_USER_LISTS = 0;
    BROADCAST_TARGET_SPECIFIC_USERS = 10;
    BROADCAST_TARGET_MY_CLIENTS = 20;
    BROADCAST_TARGET_ALL_CLIENTS = 30;
    BROADCAST_TARGET_CDL = 40;
}

message BoardBroadcast {
    optional UserBroadcast user_list = 10;
    optional BroadcastStatus status = 20;
    optional BroadcastTarget target = 30;
    optional BroadcastChannel channel = 40;
    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 130;
    optional uint64 local_revision = 140;
    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message UserGroup {
	optional Group group = 10 [lazy = true];
    optional GroupUserStatus status = 20;
    optional GroupAccessType type = 25;
    // member's role defined in group
    optional uint64 role = 26;
    repeated uint64 roles = 27;
    // indicator user onboarded time, set when status changed to GROUP_MEMBER
    optional uint64 onboarded_time = 28;
    // member sequence in the Group object
	optional uint64 group_sequence = 30;
	// member unit in the Group object
	//optional string group_unit = 31;
	//repeated string group_units = 32;
	// member alias of this Group member
	optional string member_alias = 40;

    // company info of the group user, copied from GroupUser
    //optional string division = 50;
    //optional string department = 60;

    optional string order_number = 90;

    optional uint64 tac_agreed_time = 200;

	optional uint64 sequence = 100;
    // client_uuid format: Group.id-GroupUser.sequence
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

message UserPartner {
	optional Partner partner = 10 [lazy = true];

	optional uint64 sequence = 100;
    // client_uuid format: Partner.id-PartnerUser.sequence
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

// the connection from Group to User
message GroupUser {
	optional User user = 10 [lazy = true];
    optional GroupUserStatus status = 20;
    optional GroupAccessType type = 30;
    // indicator user onboarded time, set when status changed to GROUP_MEMBER
    optional uint64 onboarded_time = 21;

    // user plan code, it should be in the list of Group.plan_codes
    // if not exists, use Group.plan_code as default value
    //removed individual plan code support
	//optional string plan_code = 40;

    // company info of the group user, deprecated
    //optional string division = 50;
    //optional string department = 60;
    // the GroupUnit client_uuid the user belongs to
    // if the unit do not exists or deleted, user are displayed at top level
    //optional string unit = 61;

    // Org member's alias
    optional string alias = 62;

    // output only for CSV import when detect field error, such as empty full name, invalid email or zone, etc
    optional bool is_invalid = 70;
    // first field name that with issue detected, for compatibility
    optional string message = 71;
    // list of fields with issues detected, including first field in above "message"
    repeated string invalid_fields = 72;

    // string format of float number to indicate group user display order in group
    optional string order_number = 80;
    // multiple group units this group user belong to. (with order number in it)
    //repeated GroupUnit units = 81;

    // group member's role defined in group
    optional uint64 role = 90;
    repeated uint64 roles = 95;
    // last role update timestamp
    optional uint64 role_assigned_time = 91;

	optional uint64 sequence = 100;
    // client_uuid format: User.id or empty before user.id becomes available
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;
    // mark field to keep content on deletion, do not fill
    optional bool keep_deleted = 121;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

// organizational hierarchy
//message GroupUnit {
//    optional string name = 10;
//
//    // sub units in this unit
//    repeated GroupUnit units = 200;
//
//    // string format of float number to indicate group unit display order in group
//    optional string order_number = 300;
//
//    optional uint64 sequence = 100;
//    // group member connect to the group unit with client_uuid, just like page group and pages
//    optional string client_uuid = 101;
//    optional uint64 revision = 110;
//    optional uint64 local_revision = 112;
//    optional bool is_deleted = 120;
//
//    optional uint64 created_time = 1000;
//    optional uint64 updated_time = 1010;
//}

message GroupBoard {
    // only board id
	optional Board board = 10 [lazy = true];

	optional uint64 sequence = 100;
    // client_uuid format: Board.id-BoardUser.sequence
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

message GroupEmailConfig {
    // smtp user name
    optional string username = 10;
    // smtp password
    optional string password = 20;
    // smtp server address
    optional string server_address = 30;
    // smtp server port
    optional uint64 server_port = 40;
    // sender/from address in outgoing emails
    optional string from_address = 50;
    // smtp server requires ssl connection
    optional bool is_ssl = 60;
}

enum GroupIntegrationType {
    //GROUP_INTEGRATION_SALESFORCE = 10;
    GROUP_INTEGRATION_SAML = 20;
    GROUP_INTEGRATION_APP = 30;
    GROUP_INTEGRATION_LOCAL = 40; // group contains local users
    GROUP_INTEGRATION_WECHAT = 50;
    GROUP_INTEGRATION_WHATSAPP = 60;
    GROUP_INTEGRATION_LINE = 70;
    GROUP_INTEGRATION_SMTP = 100;
    GROUP_INTEGRATION_CRM = 200;
    GROUP_INTEGRATION_OUTGOING = 300;
}

message GroupIntegration {
    optional GroupIntegrationType type = 10;
    optional bool enable_auto_provision = 210;

    // SalesForce SSO configuration, indexed with prefix "SF_" sf_org_id
	//optional string sf_org_id = 200;

    // Group app client id, index with prefix "ECI_" access_key
	//optional string client_id = 300;
    // for GROUP_INTEGRATION_APP, group enabled/installed WebApp appears here
    optional WebApp webapp = 301;
    // a user to represent the WebApp
    optional GroupUser user = 302;
    // a group to represent integration subscribers
    optional Group group = 303;
    // a board to represent integration board
    optional GroupBoard board = 304;
    // if free subscription board
    optional bool is_free_subscription = 305;
    // represent integration category
    optional string category = 310;

    // SAML IdP configuration
    optional SamlIdentityProviderConfig idp_conf = 400;
    // SAML integration entry apply to user role types
    // if user with the role types has matched a saml integration, user cannot use email/password to login
    repeated GroupUserRoleType saml_user_role_types = 402;

    // SAML email domain lock, index with prefix EMD_ email_domain
    // example.com
    //repeated string saml_email_domain = 410;
    // shared server generated token for all email domains, user need to add to DNS TEXT/CNAME
    //optional string saml_email_domain_token = 420;
    // filled when server verified DNS record
    //optional bool saml_email_domain_verified = 430;

    optional string domain = 500;

    optional GroupEmailConfig email_config = 510;

    // connector info for GROUP_INTEGRATION_WECHAT
    optional string connector = 600;

    // A JSON string
    optional string crm_configuration = 610;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional bool keep_deleted = 121;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum GroupSubscriptionStatus {
    // trial subscription will expire on a set date
	GROUP_TRIAL_SUBSCRIPTION = 0;
    // normal
	GROUP_NORMAL_SUBSCRIPTION = 100;
    // active subscription with payment past due
    GROUP_PAST_DUE_SUBSCRIPTION = 120;
    // user canceled subscription, only admin/owner can login
	GROUP_CANCELED_SUBSCRIPTION = 200;
    // trial expired, or stripe subscription unpaid/canceled, only admin/owner can login
	GROUP_EXPIRED_SUBSCRIPTION = 300;
    // partner/super admin disable the org, all org members cannot login
    GROUP_DISABLED_SUBSCRIPTION = 400;
}

// Local Field for Group
message GroupContact {
    optional User user = 10 [lazy = true];
}

// Local Field for Group
message GroupSupport {
	optional User user = 10 [lazy = true];
}

// Local Field for Group
message GroupPartner {
	optional Partner partner = 10 [lazy = true];
}

// Local Field for Group
message GroupCap {
    // ignore has_saml/has_salesforce if is_enterprise is false
    optional bool is_enterprise = 10;
    // allow org user to use sip configuration
    optional bool has_sip = 12;
    // allow to set board uesr as owner delegate
    optional bool has_board_owner_delegate = 13;
    // turn off emails (including binder/meet/team info) for privacy policy
    optional bool has_email_privacy = 14;
    // use generic push notification for privacy policy
    optional bool has_push_privacy = 15;
    // archive group user's binders
    optional bool has_user_archive = 16;
    // allow saml intergration configuration
    optional bool has_saml = 20 [default = true];
    // whether org is with online billing
    optional bool is_online_billing = 30;
    // hide powered by moxtra logo
    optional bool hide_moxtra_logo = 40;
    // configure app portal
    optional bool has_app_portal = 50;
    // configure app subscription
    optional bool has_app_subscription = 51;
    // flag for turn on/off audit module
    optional bool has_audit = 60;
    // flag for turn on/off report module
    optional bool has_org_report = 61;
    optional bool has_read_receipt = 65;
    // similar as has_board_owner_delegate, but provide board level delegate controlling
    //optional bool is_board_delegate_option_enabled = 66;
    // deprecated, default is false, which need verify 6-digit email verification code when do self register
    optional bool ignore_email_verification = 70;

    // allow branding (color, logo, etc.)
    optional bool has_branding = 100 [default = true];
    // allow configuration (show todo, start meet, etc.)
    optional bool has_configuration = 110 [default = true];

    optional bool disable_invite_client = 200;
    optional bool enable_acd = 210;
    optional bool enable_service_request = 220;

    // allow user to sign up with phone
    optional bool enable_phone_number_sign_up = 300;
    // use phone as primary signup option, only impact signup/login UI
    optional bool primary_sign_up_phone_number = 310;

    optional bool enable_channel_subscription = 320;
    optional bool enable_direct_invitation = 330;
    optional bool enable_hard_deletion = 340;
    // hide saml configuration for org admin, only SA/PA can configure
    optional bool hide_saml_configuration = 350;
    // cannot create user in org admin, only allow saml login
    optional bool disable_user_creation = 360;
    optional bool enable_sfdc_integration = 370;
    optional bool enable_globalrelay_integration = 371;
    optional bool enable_hubspot_integration = 372;
    optional bool enable_dynamics_integration = 373;
    optional bool enable_filevine_integration = 374;
    optional bool enable_redtail_integration = 375;
    optional bool enable_zoho_integration = 376;
    optional bool enable_smarsh_integration = 377;
    optional bool enable_advisorengine_integration = 378;
    optional bool enable_meeting_audio_ringtone = 380;
    // if enabled, we will also generate meet audio transaction when convert meet recording
    optional bool enable_meeting_transcription = 381;

    // enable Xero integration
    optional bool enable_xero_integration = 390;
    optional bool enable_wealthbox_integration = 400;

    optional bool enable_referrer_check = 500;
    optional bool enable_access_control_allow_origin = 510;
    optional bool enable_xsrf_token = 520;

    optional bool enable_2fa = 600;
    optional bool enable_2fa_trust_device = 601 [default = true];

    optional bool keep_identification_for_deleted_user = 700;
    // MV-9859: for desktop app
    optional bool enforce_logout_account_when_quiting_app = 710;
    optional bool enable_client_delete_account = 720;

    optional bool enable_apple_sign_in = 800 [default = true];
    optional bool enable_google_sign_in = 810 [default = true];
    optional bool enable_salesforce_sign_in = 812;
    optional bool enforce_password_login = 820;

    optional bool share_org_flow_templates = 830;

    optional bool is_freemium = 840;

    optional bool share_org_content_library = 850;
    optional bool enable_custom_smtp_integration = 860;

    // PRC-686: client needs a flag to decide if to use new UI frame style.
    // If not set or is false, it means to use V8 frame; else use V9.
    // And V9 org can set the flag to true when creating org by sysadmin/self-signup/rest api.
    optional bool enable_new_frame = 870;

    optional bool enable_chat_workspace = 880 [default = true];
    optional bool hide_client_dashboard = 890;

    // milliseconds, 0 means server session timeout isn't enabled
    optional uint64 server_session_timeout_interval = 900;
    optional bool restrict_new_message_email_sms = 910;
    optional bool enable_share_link = 920 [default = true];
}

message GroupSetting {
    // content editable interval
    optional uint64 content_editable_interval = 10;
    optional uint64 content_editable_interval_for_client = 11;

    // org invitation token expiry
    optional uint64 org_invitation_expiry = 20;

    optional bool enable_private_meet = 40;
    optional bool hide_meet_recording = 50;
    optional bool disable_meet_recording_sharing = 55;
    optional bool enable_meet_auto_recording = 56;
    optional bool enable_meet_password = 60;
    optional string meeting_default_password = 61;
    optional bool enable_mobile_web_meeting_join = 70;
    optional bool enable_workflow_event = 80;
    optional bool enable_digest_email = 90;
    optional uint64 digest_email_start_timestamp = 100;
    optional uint64 digest_email_interval = 110; // 1~24 hours, which is 3600000*N

    optional bool enable_client_self_signup = 120;
    optional bool send_service_request_on_client_self_signup = 121;
    optional bool enable_client_group = 122;
    optional bool enable_content_library = 130 [default = true];
    optional bool enable_client_resources = 131 [default = true];
    optional bool enable_action_library = 132 [default = true];
    optional bool enable_broadcast = 140 [default = true];
    optional uint64 user_logins_max = 150;
    optional bool expose_contact_info_to_clients = 160 [default = true];
    optional bool expose_client_contact_info_to_internals = 161 [default = true];
    optional bool enable_flow_template_library = 170;
    // account lock duration while exceed maximum failed login attempts
    optional uint64 account_lock_duration = 180;
    optional bool enforce_signature_jwt_validation_for_client_users = 190;
    optional bool enable_magic_link = 200 [default = true];
    optional bool enable_magic_link_for_internal_users = 201 [default = true];
    optional uint64 binder_view_token_timeout = 210 [default = *********];
    optional uint64 binder_view_token_timeout_for_internal_users = 211 [default = *********];
    optional bool use_browser_open_jwt = 220;
    optional uint64 user_boards_auto_archive_threshold = 230 [default = 5000];
    optional string terms_group_name = 240;
    optional bool enable_meet_log_uploading = 250;
    optional bool enable_user_data_downloading = 260 [default = false];
    optional bool include_coc_in_signed_file = 270;
    optional bool enable_workspace_report_auditing = 280 [default = true];
    optional bool enable_sms_for_email_based_client_user = 290;
    optional bool enable_client_distribution_list = 300;
    optional bool enable_broadcast_recipient = 310;
    optional bool enable_inbox = 320;
    optional bool enable_ai = 330;
    optional bool enable_client_group_to_internal_users = 340;
}

enum GroupType {
    // an user belongs to only one orgnization
    GROUP_TYPE_ORG = 0;
    // an user can join multiple team groups
    GROUP_TYPE_TEAM = 10;
    GROUP_TYPE_CLIENT_TEAM = 20;
    GROUP_TYPE_TEAM_FLEXIBLE = 30;
}

message GroupPlanCode {
    // Group allowed plan code
    optional string plan_code = 10;
    // Group plan quantity for the plan code
    optional uint64 plan_quantity = 20;
    // Cap the plan quantity for the plan code
    optional bool is_capped = 30;
}

message GroupTelephonyDomain {
    optional TelephonyDomain telephony_domain = 10 [lazy = true];
}

// org plan code definition
message CachePlan {
    optional string plan_code = 10;
    optional UserCap plan_cap = 20;

    // trial plan settings
    optional uint64 trial_end_interval = 30;

    // internal plan code
    optional string plan_code_free_user = 200 [default = "freeuser"];
    optional string plan_code_trial_user = 210 [default = "trialuser"];
    optional string plan_code_pro_user = 220 [default = "prouser"];
    optional string plan_code_beta_user = 230 [default = "betauser"];

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum BoardMemberPrivileges {
    BOARD_DELETE_OTHERS_MSG = 0x1;
    BOARD_DELETE_OTHERS_COMMENT = 0x2;
    BOARD_DELETE_OTHERS_FILE = 0x4;
    BOARD_DELETE_OTHERS_ANNOTATION = 0x8;
    BOARD_HISTORY_FROM_JOIN = 0x10;
    BOARD_INVITE_BOARD_MEMBER = 0x20;
    BOARD_SHARE_CONTENT = 0x40;
    BOARD_SEND_MSG = 0x80;
    BOARD_ADD_COMMENT = 0x100;
    BOARD_UPLOAD_FILE = 0x200;
    BOARD_SIGN_FILE = 0x400;
    BOARD_ADD_ANNOTATION = 0x800;
    BOARD_COPY_TO_OTHER_BINDER = 0x1000;
    BOARD_SAVE_TO_ALBUM = 0x2000;
}

enum GroupUserRoleType {
    ROLE_TYPE_NORMAL = 10;
    ROLE_TYPE_LOCAL = 20;
    ROLE_TYPE_GUEST = 30; // for anonymous user
}

// Rules for role items
// for the new added role item which exists on both internal and local users, we don't need set default value, the default value by user type will be set in server code
// for the new added role item which exists only on internal users for management purpose, we set default value

message ChatPrivilege {
  optional bool can_start_chat = 10 [default = true];
  optional bool can_invite_from_contact = 20 [default = true];
  optional bool can_invite_by_email = 30 [default = true];
  optional bool can_select_delegate = 40 [default = true];
  optional bool can_send_message = 50 [default = true];
  optional bool can_show_read_receipt = 60 [default = true];
  optional bool can_owner_remove_member = 70 [default = true];
}

message MeetPrivilege {
  optional bool can_start_instant_meet = 10 [default = true];
  optional bool can_scheduled_meet = 20 [default = true];
  optional bool can_start_video = 30 [default = true];
  optional bool can_share_screen = 40 [default = true];
  optional bool can_share_file = 50 [default = true];
  optional bool can_do_co_browsing = 60 [default = true];
  optional bool can_turn_on_camera = 70 [default = true];
  optional bool can_download_and_share_meeting_recording = 80 [default = true];
}

message RelationPrivilege {
  optional bool can_add_local_user = 10 [default = true];
}

message FilePrivilege {
  optional bool can_add_file = 10 [default = true];
  optional bool can_create_clip = 20 [default = true];
  optional bool can_share_publiclink = 30 [default = true];
  optional bool can_share_internally = 40 [default = true];
  optional bool can_create_note = 50 [default = true];
  optional bool can_create_location = 70 [default = true];
  optional bool can_download_file = 80 [default = true];
  optional bool can_create_whiteboard = 90 [default = true];
  optional bool can_add_spot_comment = 91 [default = true];
  optional bool can_create_sign = 100 [default = true];
  optional bool can_sign_files = 110 [default = true];
  // optional bool can_create_acknowledge = 120 [default = true];
  // optional bool can_create_approval = 130 [default = true];
  // optional bool can_create_file_request = 140 [default = true];
  optional bool can_create_folder = 150 [default = true];
  optional bool can_create_todo = 160 [default = true];
  optional bool can_create_approval = 170 [default = true];
  optional bool can_create_acknowledge = 180 [default = true];
  optional bool can_create_file_request = 190 [default = true];
  optional bool can_create_meet_request = 200 [default = true];
  optional bool can_create_form = 210 [default = true];
  optional bool can_create_pdf_form = 211 [default = true];
  optional bool can_create_launch_web_app = 220 [default = true];
  optional bool can_rename_file = 400 [default = true];
  optional bool can_move_file = 410 [default = true];
}

message RoutingPrivilege {
  optional bool can_accept_acd = 10 [default = true];
  optional bool can_accept_service_request = 20 [default = true];
}

message GroupPrivilege {
    optional bool can_audit_user = 10 [default = false];
    optional bool can_manage_subscription_channel = 20 [default = false];
    optional bool can_access_org_report = 30 [default = false];
    optional bool can_manage_content_library = 40 [default = false];
    optional bool can_manage_client_resources = 41 [default = true];
    optional bool can_manage_workflow = 50 [default = false];
    optional bool can_create_shared_flow_template = 60 [default = false];
}

message ContactPrivilege {
    // if true, user can read org members and org teams
    // don't set default value since both internal and local user have this field in their roles
    optional bool can_access_business_directory = 10;
    optional bool can_view_all_clients = 20;
}

enum GroupRoleCategory {
    ROLE_CATEGORY_NONE = 0;
    ROLE_CATEGORY_DISTRIBUTION_LIST = 10;
}

message GroupUserRole {
  optional string name = 10;
  optional bool is_default = 11; // default role cannot be modified or deleted
  optional GroupUserRoleType type = 12;
  optional string description = 13;
  optional bool include_all_admins = 14;
  optional bool include_all_internal_users = 15;
  optional GroupRoleCategory category = 20;

  optional ChatPrivilege chat = 200;
  optional MeetPrivilege meet = 210;
  optional RelationPrivilege relation = 220;
  optional FilePrivilege file = 240;
  optional RoutingPrivilege routing = 250;
  optional GroupPrivilege audit = 260;
  optional ContactPrivilege contact = 270;
  optional string role_template = 230;  // UI template, index string value

  optional uint64 users_total = 300;

  optional uint64 sequence = 100;
  optional string client_uuid = 101;
  optional uint64 revision = 110;
  optional bool is_deleted = 120;
  optional string assignments = 131;

  optional uint64 created_time = 1000;
  optional uint64 updated_time = 1010;
}

enum AsyncTaskStatus {
    ASYNC_TASK_STATUS_INVALID = 0;
    ASYNC_TASK_STATUS_QUEUED = 10;
    ASYNC_TASK_STATUS_IN_PROGRESS = 20;
    ASYNC_TASK_STATUS_SUCCESS = 30;
    ASYNC_TASK_STATUS_FAILED = 40;
}

// 1. batch transfer client task
// 2. csv import task
message AsyncTask {
    // from original client request that generated this task
    optional ClientRequest request = 100;
    // ref to resource table from client request, if any
    optional uint64 input_resource = 110;
    optional User actor = 120;
    optional AccessToken token = 130;

    // queued -> in progress -> success/failed
    optional AsyncTaskStatus status = 200;
    optional string message = 202;
    // client can use detail_code and detail_message to show localization error message
    optional ClientResponseDetailCode detail_code = 203;
    optional string detail_message = 204;
    // progress percentage = 100*process_items/total_items
    optional uint64 processed_items = 210;
    optional uint64 total_items = 220;

    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional uint64 local_revision = 35;
    optional bool is_deleted = 40;
    optional string assignments = 41;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message GroupAppConfig {
    optional uint64 ios_app_version_recommended = 100;
    optional uint64 android_app_version_recommended = 200;
}

message RoutingChannel {
    optional string name = 10;
    // channel avatar
    // points to the sequence number in group resources table
    optional uint64 picture = 12;
    optional string description = 20;
    optional string order_number = 30;
    // points to the sequence number in org teams table
    repeated UserGroup teams = 40;
    // include all org admins as agents
    optional bool include_all_admins = 42;
    // a user to represent the WebApp
    optional GroupUser user = 44;
    // a flow template board
    optional Board board = 46;

    // unassigned client only or all client can use this channel
    optional bool unassigned_client_only = 50;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    // mark field to keep content on deletion, do not fill
    optional bool keep_deleted = 121;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message RoutingWeekday {
    optional uint64 day_of_week = 10;
    optional string start_time = 20;
    optional string end_time = 30;
    optional bool is_close = 40;
}

message RoutingSpecialDay {
    optional string date = 10;
    optional string start_time = 20;
    optional string end_time = 30;
    optional bool is_close = 40;
}

message RoutingConfig {
    // acd config
    optional uint64 acd_max_conns_per_agent = 10;
    repeated RoutingChannel acd_channels = 20;
    repeated RoutingChannel sr_channels = 30;

    repeated RoutingWeekday weekdays = 40;
    repeated RoutingSpecialDay special_days = 50;

    optional uint64 acd_connection_timeout = 60 [default = 90000];

    repeated UserTag template_messages = 70;

    repeated UserTag template_messages_sr = 80;

    optional string prompt_leave_message = 200;
    optional bool disable_acd_leave_message = 210;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional uint64 local_revision = 112;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message GroupUserSetting {
    optional bool include_all_admins = 10;
    optional bool include_all_internal_users = 20;
}

enum PropertyType {
    PROPERTY_TYPE_INVALID = 0;
    PROPERTY_TYPE_LIST = 10;
    PROPERTY_TYPE_TEXT = 20;
}

message Property {
    optional string name = 10;
    optional PropertyType type = 20;
    repeated string options = 30;

    optional string order_number = 200;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

// Group of user, like a workgroup, an org, or a salesforce org, etc...
// TODO: flag to indicate self sign org with online billing
message Group {
	//Description The group's ID
	//Returns A JSON string
	optional string id = 10;

	//Description The group's full name
	//Returns A JSON string
	optional string name = 20;

    //Description The group's type
    //Returns A JSON string
    optional GroupType type = 21 [default = GROUP_TYPE_ORG];

    // Org's alias
    optional string alias = 22;

    optional string description = 23;

    // default timezone for the org
    optional string timezone = 24 [default = "America/Los_Angeles"];

    //Description The Group's main contact person, informational only
    //Available if group were create by Super/Partner Admin
    //Only name, phone, etc filled
    optional GroupContact contact = 25;

    //Only super admin/partner member can update
    optional GroupCap group_caps = 26;

    //Org level setting, super admin/partner member/org admin can update
    optional GroupSetting group_settings = 27;

    // Org support email
    optional GroupSupport support = 28;
    optional GroupSupport client_support = 29;

    // Group default plan code for internal users
	optional string plan_code = 30;
    // DEPRECATED: Group allowed plan codes
	//repeated string plan_codes = 31;
    // Group default plan code for client users
    optional string plan_code_local = 32;
    // Group committed plan quantity for internal users
	optional uint64 plan_quantity = 35;
    // Group committed plan quantity for client users
    optional uint64 plan_quantity_local = 36;

    // group allowed plan code and their commit quantity
    //repeated GroupPlanCode allowed_plan_codes = 37;

	//Description The group's member list
    repeated GroupUser members = 40;
    // team managers
    repeated GroupUser managers = 41;
    optional GroupUserSetting managers_setting = 42;
    //Description The group's organizational hierarchy
    //repeated GroupUnit units = 45;

	//Description The group's subscription status
    optional GroupSubscriptionStatus status = 50;

    // group template name, group templates are in Partner.group_templates
    optional string template_name = 60;

    // stripe payment gateway integration customer id
	optional string customer_id = 61;
    // stripe payment gateway integration coupon id
    optional string coupon_id = 62;

    // before current billing period end, cancellation or downgrade can be requested and will take effect at period end
    // stripe payment gateway integration cancel_at_period_end
    optional bool cancel_subscription_at_period_end = 65;
    // scheduled plan code update at current billing period end
    optional string scheduled_plan_code = 66;

    // trial start/end time
    // also billing cycle start/end time with scripe online billing
	optional uint64 trial_start_time = 51;
    // trial grace period start time, NOT IN USE
	//optional uint64 trial_grace_period_time = 52;
	optional uint64 trial_end_time = 53;

    // before commitment end, group cancellation can be requested and will take effect at the end time
    //optional uint64 commitment_start_time = 55;
    optional uint64 commitment_end_time = 56;
    optional uint64 cancellation_request_time = 57;

    // Partner id of the Group if it is Partner Managed
    optional GroupPartner partner = 70;

    // Group avatar
    // points to the sequence number in resource table
    optional uint64 picture = 81;
    // points to the sequence number in resource table, (Terms And Conditions)
    optional uint64 tac = 82;
    // web version path, such as /mercury/
    optional string web_version = 83;

    // Bitwise-OR of BoardPrivileges.
    // owner_default_privilege = BOARD_DELETE_OTHERS_ANNOTATION
    //                          | BOARD_INVITE_BOARD_MEMBER | BOARD_SHARE_CONTENT
    //                          | BOARD_COPY_TO_OTHER_BINDER | BOARD_SAVE_TO_ALBUM
    //                          | BOARD_HISTORY_FROM_JOIN | BOARD_SEND_MSG | BOARD_UPLOAD_FILE
    //                          | BOARD_ADD_COMMENT | BOARD_SIGN_FILE | BOARD_ADD_ANNOTATION;
    // editor_default_privilege = BOARD_DELETE_OTHERS_ANNOTATION
    //                          | BOARD_INVITE_BOARD_MEMBER | BOARD_SHARE_CONTENT
    //                          | BOARD_COPY_TO_OTHER_BINDER | BOARD_SAVE_TO_ALBUM
    //                          | BOARD_SEND_MSG | BOARD_UPLOAD_FILE | BOARD_ADD_COMMENT
    //                          | BOARD_SIGN_FILE | BOARD_ADD_ANNOTATION;
    // viewer_default_privilege = BOARD_SHARE_CONTENT | BOARD_COPY_TO_OTHER_BINDER
    //                          | BOARD_SAVE_TO_ALBUM | BOARD_SIGN_FILE;
    optional uint64 board_owner_privileges = 90 [default = 16376];
    optional uint64 board_editor_privileges = 91 [default = 16360];
    optional uint64 board_viewer_privileges = 92 [default = 13376];

	//Description Boards group can access to, also known as Team Library
    repeated GroupBoard boards = 101;

	//Description The group's billing integration signature for JS call recurly
    //output only, no store
	optional string recurly_signature = 200;

    // Group integration with 3rd party system
    repeated GroupIntegration integrations = 300;

    //Description public teams group can access to
    repeated UserGroup teams = 310;

    // Group Caps for team library max
    // output only, no store
    optional UserCap cap = 320;

    //Description External profile images for the group
    repeated UserResource resources = 400;

    // tags attached to this group, this will also be email template dictionary for branding
    repeated UserTag tags = 500;

    optional GroupTelephonyDomain group_telephony_domain = 600;

    // the group members can not invite global unique email users to org/binder/team
    //optional bool is_restricted = 650;

    repeated GroupUserRole roles = 700;

    repeated AsyncTask tasks = 800;

    optional GroupAppConfig app_config = 810;
    optional RoutingConfig routing_config = 820;

    //Tokens for invite RM users
    repeated UserQRToken invitation_tokens = 900;

    repeated UserTag redeem_urls = 905;
    optional uint64 redeem_url_idx = 906;

    repeated Property board_properties = 910;

    // org id of shared content gallery
    optional string shared_content_library_group_id = 1100;

    // total group members include all user types
    // for non-local members count, use (total_members - total_local_members)
    optional uint64 total_members = 1200;
    // members count with USER_TYPE_LOCAL
    optional uint64 total_local_members = 1210;

    // total content libraries count
    optional uint64 total_content_libraries = 1220;

    // total group managers count
    optional uint64 total_managers = 1230;

    // indicate whether a temp group
    optional bool istemp = 1300;

    // User object is a main object and it doesn't have a sequence
	optional uint64 revision = 110;
    // for partial subscription data, the base revision which the partial data can be applied on
    // this is a output only field
	//optional uint64 previous_revision = 111;
    optional bool is_deleted = 120;
	optional uint64 local_revision = 130;
    optional string assignments = 131;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

// =============================================
// Partner
// =============================================

enum PartnerType {
    PARTNER_TYPE_RESELLER = 0;
    PARTNER_TYPE_APP = 10;
}

message PartnerCap {
    // whether if partner can access telephony domain
    optional bool has_telephony_domain = 10;
    optional bool has_sip = 20;
    // instruct admin portal to disable org meet usage
    optional bool has_org_meet_usage = 30;
}

// Partner, for SSO purpose or Group management, with prefix PARTNER_
message Partner {
	//Description The partner's ID
	//Returns A JSON string
	optional string id = 10;

	//Description The partner's name
	//Returns A JSON string
	optional string name = 20;

	//Description The partner's type
	//Returns A JSON string
	optional PartnerType type = 25 [default = PARTNER_TYPE_RESELLER];

	//Description The partner's description
	//Returns A JSON string
	optional string description = 30;

    //Description The partner's configuration, set and used by developer portal, such as allow long ttl in WebApp, allow partner api usage, etc
    //Returns A JSON string
    optional string configuration = 35;

    //Description The partner's main contact person, informational only
    //Only name, phone, etc filled
    optional PartnerContact contact = 40;

    //Description configuration for the partners
    optional PartnerCap partner_caps = 45;

    //Description The partner's domain suffix, changable by super admin only
    //Certain plan code will require email domain lock to match Partner's domain suffix
    // NOT IN USE
    //optional string email_domain_suffix = 50;

    //Description The partner's trial policy, changable by super admin only
    optional bool allow_trial = 60;
    optional uint64 max_trial_days = 61;

    //Description The partner's upgrade information, to be displayed when click group admin console buy/upgrade button
    optional string upgrade_info = 70;

    // org id of shared content gallery
    optional string shared_content_library_group_id = 80;

	//Description The partner's admin user list
    repeated PartnerUser members = 200;

    // Partner's SSO integration with 3rd party system
    repeated PartnerIntegration integrations = 300;

    // Partner's plan codes list
    repeated PartnerPlanCode plan_codes = 400;
    // Default group plan code for internal users in self signup universal app
    optional string default_plan_code = 410;
    // Default group plan code for client users in self signup universal app
    optional string default_plan_code_local = 412;
    //Description Group template for self signup universal app
    //optional PartnerGroup default_group = 420;
    repeated PartnerGroup group_templates = 422;

    //Description WebApps partner manages
    repeated PartnerWebApp webapps = 500;

    //Description Groups partner manages
    repeated PartnerGroup groups = 600;

    //Description WebApps partner manages
    repeated PartnerTelephonyDomain partner_telephony_domains = 700;

    optional uint64 revision = 110;
    // for partial subscription data, the base revision which the partial data can be applied on
    // this is a output only field
    //optional uint64 previous_revision = 111;
    optional bool is_deleted = 120;
    optional uint64 local_revision = 130;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message PartnerUser {
    optional User user = 10 [lazy = true];

	optional uint64 sequence = 100;
    // client_uuid format: User.id
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;
    // mark field to keep content on deletion, do not fill
    optional bool keep_deleted = 121;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

message PartnerPlanCode {
    optional string plan_code = 10;
    // output only
    optional UserCap plan_cap = 20;

	optional uint64 sequence = 100;
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

message PartnerIntegration {
    // SAML IdP configuration
    optional SamlIdentityProviderConfig idp_conf = 10;

	optional uint64 sequence = 100;
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

// partner's registered OAuth Apps
message PartnerWebApp {
	optional WebApp webapp = 10 [lazy = true];

	optional uint64 sequence = 100;
    // client_uuid format: WebApp.client_id
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

message PartnerContact {
	optional User user = 10 [lazy = true];
}

message PartnerGroup {
    optional Group group = 10 [lazy = true];

    optional uint64 sequence = 100;
    // must use group id as PartnerGroup.client_uuid so we can use index to query group from partner
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message PartnerTelephonyDomain {
    optional TelephonyDomain telephony_domain = 10;

    optional bool is_default = 50;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

// =============================================
// SAML Configuration
// =============================================

// One and only SystemSamlService, for SSO service provider, with key P_SERVICE_PROVIDER_LIST
message SystemSamlService {
	//Description The SystemSamlService's ID
	//Returns A JSON string
	optional string id = 10;

    // Partner's SSO integration with 3rd party system
    repeated SamlServiceProvider service_providers = 20;

	optional uint64 revision = 110;
    // for partial subscription data, the base revision which the partial data can be applied on
    // this is a output only field
	//optional uint64 previous_revision = 111;
    optional bool is_deleted = 120;
	optional uint64 local_revision = 130;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

message SamlServiceProvider {
    optional SamlServiceProviderConfig sp_conf = 10;

	optional uint64 sequence = 100;
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

message SamlIdentityProviderConfig {
    optional string name = 10;
    // IDP type template for prefill
    optional string type = 20;

    optional string idpid = 110;
    optional string spid = 120;
    optional string authncontextclassref = 130;
    optional string targetparameter = 140;
    optional string idploginurl = 150;
    optional string assertionconsumerserviceurl = 155;
    optional string cert = 160;
    optional string nameidformat = 170;
    // if true: nameid has unique id filled instead of email address
    optional bool nameid_has_unique_id = 172;
    optional bool idpinitiated = 180;
    optional bool postprofile = 190;
    optional bool authnrequestsigned = 200;
}

message SamlServiceProviderConfig {
    optional string idpid = 10;
    optional string spid = 20;
    optional string authncontextclassref = 30;
    optional string targetparameter = 40;
    optional string sploginurl = 50;
    optional string nameidformat = 60;
    optional bool encryption = 70;
    optional string algorithm = 80;
    optional string type = 90;

    optional string cert = 100;
    optional string key = 110;
}

// =============================================
// WebApp/OAuth App
// convert webapp to oauth app on May 22, 2014. Bingo
// =============================================

// user's registered OAuth Apps
message UserWebApp {
	optional WebApp webapp = 10 [lazy = true];

	optional uint64 sequence = 100;
    // client_uuid format: WebApp.client_id
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

message WebAppUser {
	optional User user = 10 [lazy = true];
    optional Partner partner = 20 [lazy = true];
}

enum WebAppType {
    WEBAPP_TYPE_SDK = 0;
    WEBAPP_TYPE_SDK_PLUS_API = 10;
    WEBAPP_TYPE_BOT = 20;
    WEBAPP_TYPE_SUBSCRIPTION = 30;
    WEBAPP_TYPE_EMBEDDED = 40;
    WEBAPP_TYPE_CONNECTOR = 50;
    WEBAPP_TYPE_INBOX_BOT = 60;
    WEBAPP_TYPE_AI = 80;
}

message WebApp {
    //Description The WebApp's ID
    optional string id = 10;

    //Description The WebApp's Type
    optional WebAppType type = 15;

    //Description The WebApp's name
    optional string app_name = 20;

    //Description The WebApp's description
    optional string description = 30;
    optional string description_long = 32;

    //Description The WebApp's redirect url
    optional string redirect_url = 40;

    //Description The WebApp's client id
    optional string client_id = 50;

    //Description The WebApp's client secret
    optional string client_secret = 60;

    //Description The WebApp's grant types
    repeated string grant_types = 70;

    //Description The WebApp's grant scopes
    repeated string scopes = 80;

    //Description The WebApp's picture
    optional uint64 picture = 90;

    //Description The WebApp's category
    optional string category = 92;

    optional string verification_token = 94;
    optional string verification_url = 95;
    optional string callback_url = 96;

    optional string template = 100;
    optional string template_richtext = 102;
    optional string instructions = 104;
    optional string note = 106;

    // urls for embedded web app
    optional string desktop_home_url = 140;
    optional string mobile_home_url = 141;

    // for connector
    // status to indicate if connector are trained
    optional string status = 150;
    // indicate if nature language processing is internal or external
    optional string nlp_type = 151;

    // app apns information so we can send push notification to them
    optional string apns_cert = 200;
    optional string apns_private_key = 210;
    optional string apns_password = 220;
    optional bool is_apns_cert_expired = 211;
    // if true, use apple development ios push service
    optional bool apns_development = 230; // shared by apns and voip notification
    optional string voip_cert = 240;
    optional string voip_private_key = 250;
    optional string voip_password = 260;
    optional bool is_voip_cert_expired = 261;
    // app gcm information so we can send push notification to them
    optional string gcm_api_key = 300;
    // send gcm notification to the url instead of to google gcm service
    optional string optional_gcm_url = 310;
    // if true, carry badge in the notification payload
    optional bool has_badge = 350;

    // ios app identifier, used in apple-app-site-association
    //team id + bundle id
    optional string ios_app_id = 800;
    optional string auth_key_id = 801;
    optional string auth_key = 802;
    optional string team_id = 803;
    optional string bundle_id = 804;
    // used for Apple Sign-in
    optional string apple_oauth_bundle_id = 805;

    // android app identifier, used in assetlinks.json
    optional string android_app_namespace = 810;
    optional string android_app_pkg_name = 811;
    repeated string android_app_fingerprints = 812;
    // used for Google Sign-in
    optional string google_oauth_client_id = 813;

    // the owner of the webapp
    optional WebAppUser owner = 400;

    // push notification sound customization
    optional string sound_default = 500;
    optional string sound_meeting_call = 510;

    // tags attached to this WebApp, and branding configuration are store in tags
    repeated UserTag tags = 600;

    //Description Converted branding css
    repeated UserResource resources = 700;

    repeated NotificationVendor vendors = 900;

    // indicate m0 app when it is true
    optional bool is_universal = 2000;

    // WebApp object is a main object and it doesn't have a sequence
    optional uint64 revision = 110;
    // for partial subscription data, the base revision which the partial data can be applied on
    // this is a output only field
    //optional uint64 previous_revision = 111;
    optional bool is_deleted = 120;
    optional uint64 local_revision = 130;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message NotificationVendor {
    optional string name = 10;
    optional string app_id = 20;
    optional string app_secret = 30;
    optional string authenticate_url = 40;
    optional string push_url = 50;

    optional uint64 sequence = 100;
    // client_uuid format: WebApp.client_id
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

// =============================================
// Board, Page and Element
// =============================================

enum BoardResourceType {
    BOARD_RESOURCE_NONE = 0;
    BOARD_RESOURCE_THUMBNAIL = 10;
    BOARD_RESOURCE_BACKGROUND = 20;
    BOARD_RESOURCE_VECTOR = 30;
    BOARD_RESOURCE_EMBEDDED = 40;
    BOARD_RESOURCE_VECTOR_THUMBNAIL = 50;
    BOARD_RESOURCE_BOARD_AS_PDF= 100;
    BOARD_RESOURCE_BOARD_AS_PPT= 110;
    BOARD_RESOURCE_RECORDING = 120;
    BOARD_RESOURCE_SESSION_AS_VIDEO = 130;
    BOARD_RESOURCE_COVER = 140;
    BOARD_RESOURCE_BANNER = 141;
    BOARD_RESOURCE_AUDIO_RECORDING = 150;
    BOARD_RESOURCE_AVATAR = 160;
    BOARD_RESOURCE_SIGNATURE_AS_PDF = 170;
    BOARD_RESOURCE_SESSION_AUDIO_SPEAKER = 180;
    BOARD_RESOURCE_SESSION_MEET_CHAT = 190;
    BOARD_RESOURCE_TRANSACTION_AS_PDF = 200;
    BOARD_RESOURCE_TRANSACTION_AS_CSV = 201;
    BOARD_RESOURCE_SESSION_TRANSCRIPTION = 210;
    BOARD_RESOURCE_SESSION_TRANSCRIPTION_VTT = 211;
    BOARD_RESOURCE_SESSION_SUMMARY = 212;
}

enum BoardResourceStatus {
    BOARD_RESOURCE_STATUS_NONE = 0;
    BOARD_RESOURCE_STATUS_QUEUED = 10;
    BOARD_RESOURCE_STATUS_CONVERTING = 20;
    BOARD_RESOURCE_STATUS_CONVERTED = 30;
    BOARD_RESOURCE_STATUS_CONVERT_FAILED = 40;
    BOARD_RESOURCE_STATUS_KEEP_UNCONVERTED = 50;
    BOARD_RESOURCE_STATUS_TOO_MANY_PAGES = 60;
    BOARD_RESOURCE_STATUS_TOO_LARGE = 70;
    BOARD_RESOURCE_STATUS_INVALID_PASSWORD = 80;
}

// external resource/file for the board
message BoardResource {
    // server internal use, part name in multipart uploads
    optional string part = 1;

    // for cross dc, after copy board, resource still in original dc
    optional string origin = 5;
    // use origin for grouping
    optional bool use_origin_grouping = 6;

    // resource name
    optional string name = 10;

    // client use only for resource path
    optional string path = 11;

    // resource type for client use only
    optional BoardResourceType type = 20;

    // Resource mime-type
    optional string content_type = 21;

    // Resource content-lenght
    optional uint64 content_length = 22;

    // Resource media length in milliseconds if it is a media file
    optional uint64 media_length = 23;

    //Is password protected resource
    optional bool is_password_protected = 24;

    // password for converter to decrypt
    optional string password = 25;

    // base16 sha256 hash of the file
    optional string sha256_hash = 29;
    // base16 md5 hash of the file, which is used to identify the file in storage
    optional string hash = 30;

    // thumbnail only: rotate in degrees (0, 90, 180, 270)
    optional uint64 rotate = 31;

    optional uint64 width = 32;
    optional uint64 height = 33;

    // the user sequence in user table who created the element
    optional uint64 OBSOLETE_creator_sequence = 41;
    // user who updated the resource
    optional User OBSOLETE_creator = 40 [lazy = true];
    // use BoardUser instead of User,
    // BoardUser is not a container,
    // User inside BoardActor will be updated as a bundle
    optional BoardActor creator = 42;

    // resource converting status
    optional string upload_sequence = 50;
    optional BoardResourceStatus status = 51;
    optional uint64 converted_pages = 52;
    optional uint64 total_pages = 53;
    optional uint64 max_pages = 54;

    // pdf/ppt converted from board, sequence and revision of the pages
    repeated BoardPage pages = 60;
    // video converted from board session, session_key and revision of the session
    optional ActionObject session = 65;

    // the email info if the resource is an incoming email to board id
    optional string email_subject = 70;
    optional string from_email = 71;
    optional string from_name = 72;
    optional bool is_email_empty = 73;

    // sequences on file path, for example: folder_sequence/subfolder_sequence/file_sequence
    optional string file = 80;

    // upload the converted file to destination board folder; now only support the same board
    optional Board destination_board = 85;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    // to instruct assign a local uint64 field from/to a uint64 variable
    // such as: $D1DD8A3F-19CC-412E-8ACF-48CD279819B0=sequence, where $D1DD8A3F-19CC-412E-8ACF-48CD279819B0 is a variable
    optional string assignments = 131;

    // to support to create feed for upload resource
    // this resource is generated from the original resource, such as an email
    optional uint64 original_resource_sequence = 155;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

message BoardTagName {
	optional string meet_agenda = 10 [default = "MEET_AGENDA"];
	optional string meet_document_board_id = 20 [default = "MEET_DOCUMENT_BOARD_ID"];
}

message BoardTag {
	optional string name = 10;
	optional string string_value = 20;
	optional uint64 uint64_value = 30;

	optional uint64 sequence = 100;
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

enum BoardUserStatus {
    // normal board member
	BOARD_MEMBER = 0;
    // user are invited to join the board and wait for accept
	BOARD_INVITED = 10;
    // user requested to join the board and wait for approval
	BOARD_MEMBER_REQUESTED = 20;
    // board user accessed before, via board key, or was member before
	BOARD_VISITED = 30;
}

enum BoardAccessType {
	BOARD_NO_ACCESS = 0;
	BOARD_UPLOAD_ONLY = 90;
	BOARD_READ = 100;
	BOARD_READ_WRITE = 200;
	BOARD_OWNER = 300;
}

enum BoardRoutingStatus {
    ROUTING_STATUS_INVALID = 0;
    ROUTING_STATUS_OPEN = 10;
    ROUTING_STATUS_OPEN_NO_TIMEOUT = 12;
    ROUTING_STATUS_IN_PROGRESS = 20;
    ROUTING_STATUS_AGENT_COMPLETE = 30;
    ROUTING_STATUS_CLOSE = 40;
    ROUTING_STATUS_CLOSE_TIMEOUT = 50;
    ROUTING_STATUS_OFFICE_CLOSE = 60;
    ROUTING_STATUS_BOT_IN_PROGRESS = 70;
}

message BoardUserAOSM {
    optional uint64 timestamp = 100;
    optional RSVPStatus reply = 101;
}

enum RequestingUserStatus {
    REQUESTING_USER_STATUS_INVALID = 0;
    REQUESTING_USER_STATUS_PENDING = 10;
    REQUESTING_USER_STATUS_APPROVED = 20;
    REQUESTING_USER_STATUS_DENIED = 30;
    REQUESTING_USER_STATUS_JOINED = 40;
}

message BoardUser {
	optional User user = 10 [lazy = true];
    // group can also be a board member
    optional Group group = 15 [lazy = true];
    optional BoardUserStatus status = 20;
    optional BoardAccessType type = 30;
    // flag to turn off push notification
    // deprecated, use push_notification_level instead
    optional bool is_notification_off = 40;
    optional NotificationLevel push_notification_level = 41;

    // last type indication timestamp a user set
    optional uint64 type_indication_timestamp = 50;

    optional bool is_alternative_host = 60;
    optional bool is_owner_delegate = 61;

    // last accessed time
    optional uint64 accessed_time = 70;
    // user marked unread feed's created time
    optional uint64 first_unread_feed_timestamp = 71;

    // last invited time
    optional uint64 invited_time = 80;
    optional bool is_invited_in_session = 81;
    // action over started meeting, added by meet team
    optional BoardUserAOSM action = 82;

    // indicate a user joined from team
    optional bool is_from_team = 90;
    // board team sequences that indicate the teams current user in
    repeated uint64 teams = 91;
    // participant number for invited member in session
    optional uint64 participant_number = 92;

    optional RequestingUserStatus requesting_user_status = 210;
    // actor who approve/deny the request
    optional BoardActor responder = 220;

    optional string invite_msg = 230;

	optional uint64 sequence = 100;
    // client_uuid format: User.id or Group.id
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;
    // mark field to keep content on deletion, do not fill
    optional bool keep_deleted = 121;

    optional string assignments = 131;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

message BoardSession {
    // keep track of board sessions
    // update and output as a bundle
    optional ActionObject session = 10 [lazy = true];
    optional ActionObject previous_session = 20 [lazy = true];

    // flow client_uuid this base object linked to
    //optional string flow = 321;

    // used to suppress recurring meet job
    optional bool is_not_recurring = 200;

	optional uint64 sequence = 100;
	optional string client_uuid = 101;
	optional uint64 revision = 110;
	optional bool is_deleted = 120;
    optional string assignments = 131;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

enum RichTextFormat {
    TEXT_FORMAT_BBCODE = 10;
    TEXT_FORMAT_CARD = 20;
}

message BoardComment {
    // the user sequence in user table who created the element
    optional uint64 OBSOLETE_creator_sequence = 11;
    // user who add the comment
    optional User user = 10 [lazy = true];
    optional BoardActor creator = 12;
    // in meet, use roster to tell who add the comment
    optional ActionUserRoster roster = 60;
    // the plain text
    optional string text = 20;
    // the rich text
    optional string rich_text = 21;
    // the rich text format
    optional RichTextFormat rich_text_format = 22;
    optional int64 x = 30;
    optional int64 y = 40;
    // page position comment index
    optional uint64 position_comment_index = 45;
    // audio/video resource used by comment
    // point the resource table of the container of this comment
    optional uint64 resource = 50;
    // output only field for media public view token
    optional string resource_view_token = 51;
    // client use only for resource path
    optional string resource_path = 52;
    // resource media length in milliseconds
    optional uint64 resource_length = 53;
    optional string url_preview = 65;

    // the original timestamp of the comment
    // it is set if the comment is copied from other place
    optional uint64 timestamp = 70;
    // the comment content is modified after creation
    optional bool is_modified = 71;

    // this is is original position comment or reply message to it
    optional bool is_position_comment = 80;

    //to support to create feed for upload resource
    optional uint64 original_resource_sequence = 155;

    // flow client_uuid this base object linked to
    //optional string flow = 161;

    // point to the original comment which this comment is replying
    optional uint64 original_comment = 165;
    // point to the original page group which this comment is replying
    optional string original_page_group = 166;
    // point to the original session which this comment is replying
    optional uint64 original_session = 167;
    // point to the original signature which this comment is replying
    optional uint64 original_signature = 168;
    // point to the original transaction which this comment is replying
    optional uint64 original_transaction = 169;
    // board reference link sequence point to the base object this comment is replying
    optional uint64 original_reference_link = 170;

    optional string custom_info = 200;
    // for social connector saving data from social clients
    optional string social_custom_info = 201;
    // A json string, used to store extra info
    optional string custom_data = 205;

    optional uint64 pin = 220;

    //optional uint64 main_feed = 600;
    //optional uint64 thread = 610;

    optional uint64 sequence = 100;
    optional string client_uuid = 5;
    optional uint64 revision = 110;
    // output only field for audit object to output past comment editions
    repeated BoardComment revisions = 112;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum BoardFolderType {
    FOLDER_TYPE_NONE = 0;
    FOLDER_TYPE_EMAIL = 10;
    FOLDER_TYPE_TRANSACTION = 20;
}

message BoardFolder {
    optional string name = 10;

    optional bool is_recycled = 30;
    optional BoardFolderType folder_type = 40;
    // sub folders in this folder
    repeated BoardFolder folders = 200;

    // files in this folder
    repeated BoardPageGroup files = 300;

    optional uint64 sequence = 100;
    optional string client_uuid = 20;
    optional uint64 revision = 110;
    //optional uint64 previous_revision = 111;
    optional uint64 local_revision = 112;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message BoardPageGroup {
    optional string name = 10;

    // file description
    optional string description = 11;

    // string format of float number to indicate file display order in board (with other pages and page groups)
    optional string order_number = 12;

    // the original board resource sequence of the file
    optional uint64 original = 15;
    // the original board signature sequence of the file
    optional uint64 original_signature = 16;

    optional bool is_recycled = 30;

    // the board reference link sequence which point to this file
    optional uint64 reference_link = 40;

    optional uint64 thumbnail = 200;
    optional uint64 first_page = 201;

    repeated BoardResource resources = 300;

    // flow client_uuid this base object linked to
    //optional string flow = 321;

    // increase by one when file related page is changed
    optional uint64 total_changes = 330;

    optional string custom_info = 340;
    // for social connector saving data from social clients
    optional string social_custom_info = 341;
    // for counting total used times of a file template in content library
    optional uint64 total_used_count = 350;
    // last used timestamp of a file template in content library
    optional uint64 last_used_timestamp = 351;

    optional uint64 pin = 360;

    // file display name
    optional string display_name = 1300;

    optional uint64 sequence = 100;
    optional string client_uuid = 20;
    optional uint64 revision = 110;
    //optional uint64 previous_revision = 111;
    optional uint64 local_revision = 112;
    optional bool is_deleted = 120;

    // to instruct assign a local uint64 field from/to a uint64 variable
    // such as: original=$D1DD8A3F-19CC-412E-8ACF-48CD279819B0, where $D1DD8A3F-19CC-412E-8ACF-48CD279819B0 is a variable
    optional string assignments = 131;

    // represents original created time before moved
    optional uint64 original_created_time = 140;
    // represents meaningful last modified time
    optional uint64 last_modified_time = 150;
    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message BoardViewToken {
    // base64 encoded token
    optional string token = 10;
    optional uint64 expire_timestamp = 20;
    optional BoardAccessType type = 30 [default = BOARD_READ];

    // BoardUser.sequence indicate who create the token
    optional BoardUser actor = 35;
    // ObjectFeed.actor will be set as actor_file_as for feeds authenticated with this token
    optional User actor_file_as = 36 [lazy = true];
    optional BoardActor creator = 37;

    // if exists: listed pages/page_groups/resources are granted in this token
    // if not: whole binder are granted
    repeated BoardPage pages = 40;
    repeated BoardResource resources = 50;
    repeated BoardPageGroup page_groups = 60;
    repeated BoardFolder folders = 70;
    repeated BoardSignature signatures = 80;

    // user who join board with this token do not need approval
    optional bool is_invitation_token= 200;

    // instruction note for webhook usage, store/retrieval only in biz server
    optional string note = 300;
    // indicate that this view token is an outgoing view token
    optional bool is_outgoing = 310;
    // whether the view token is diabled
    optional bool disabled = 320;

    repeated BoardUser users = 410;
    repeated WorkflowVar variables = 420;

    optional bool is_share_token = 500; // indicate this token is used for share link
    optional string code = 510;
    optional bool auto_approve = 520;
    optional bool member_only = 530;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message BoardPageElement {
	// svg tag or tags of the annotation
	// for example:
	// <rect fill="none" stroke="blue" stroke-width="1" x="1" y="1" width="498" height="398" />
	// <polyline fill="none" stroke="#888888" stroke-width="1" points="400,300 400,200" />
	// <path fill="none" stroke="red" stroke-width="5" d="M100,200 C100,100 250,100 250,200
	//                                   S400,300 400,200" />
	// <text x="250" y="150"
	//       font-family="Verdana" font-size="55" fill="blue">
	//   Hello, out there
	// </text>
	optional string svg_tag = 100;

    // used in feed to highlight user's annotation
	optional bool highlight = 110;
    // the annotation is readonly, used in e-sign case
    optional bool readonly = 111;

    // audio/video/picture resource used by element
    // point the resource table of the container of this element
	optional uint64 resource = 120;
    // client use only for resource path
    optional string resource_path = 121;
    // client use only for resource name
    optional string resource_name = 122;

    // the user sequence in user table who created the element
    optional uint64 creator_sequence = 131;
    // board user who add the element,
    // use creator_sequence instead,
    // use this field if creator_sequence is 0.
    optional User OBSOLETE_user = 130 [lazy = true];
    optional BoardActor creator = 132;

    // tags attached to this element
    repeated BoardTag tags = 200;

    optional string assignments = 300;

	optional uint64 sequence = 10;
	// id generated from client id to identify the annotation when it is received from server
	optional string client_uuid = 20;
	optional uint64 revision = 30;
	optional uint64 local_revision = 35;
    optional bool is_deleted = 40;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

// background layer of the page
// indicate the application type to render the background
// svg layer is always on the top of the background layer
enum BoardPageType {
    // empty background
	PAGE_TYPE_WHITEBOARD = 0;
    // Not supported resource file
    PAGE_TYPE_NOT_SUPPORTED = 1;
    // single background picture from resource table
	PAGE_TYPE_IMAGE = 10;
    // web based webclip
	PAGE_TYPE_WEB = 20;
    // Video clip and an optional album art picture
	PAGE_TYPE_VIDEO = 30;
    // Audio clip and an optional album art picture
	PAGE_TYPE_AUDIO = 40;
    // PDF background
	PAGE_TYPE_PDF = 50;
    // a url Link to a web page
	PAGE_TYPE_URL = 60;
    // moxtra note page
	PAGE_TYPE_NOTE = 70;
    // desktop share page
    PAGE_TYPE_DESKTOPSHARE = 80;
    // single geo picture
    PAGE_TYPE_GEO = 90;
    // Wildcard page type for internal usage
    PAGE_TYPE_ANY = 999;
}

enum FormFieldType {
    FORM_FIELD_TYPE_INVLIAD = 0;
    FORM_FIELD_TYPE_PUSH_BUTTON = 10;
    FORM_FIELD_TYPE_RADIO_BUTTON = 20;
    FORM_FIELD_TYPE_CHECKBOX = 30;
    FORM_FIELD_TYPE_TEXT = 40;
    FORM_FIELD_TYPE_CHOICE = 50;
    FORM_FIELD_TYPE_SIGNATURE = 60;
}

message BoardPageFormField {
    // basic
    optional FormFieldType type = 100;
    optional string name = 110;
    optional uint64 x = 120;
    optional uint64 y = 130;
    optional uint64 width = 140;
    optional uint64 height = 150;
    optional uint64 flags = 160;

    // for text field
    optional bool is_multiple_line = 200;
    optional bool is_password = 210;
    optional string default_value = 220;

    // for choice field
    optional bool is_multiple_select = 300;
    repeated string choices = 310;


    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional bool is_deleted = 40;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message BoardPage {
    // the user sequence in user table who created the page
    optional uint64 creator_sequence = 80;
    // the creator user
    optional User OBSOLETE_user = 81 [lazy = true];
    optional BoardActor creator = 82;

	optional string name = 90;

	// begining tag of the svg
	// for example:
	// <?xml version="1.0"?>
	// <svg width="5cm" height="4cm" viewBox="0 0 500 400"
	//      xmlns="http://www.w3.org/2000/svg" version="1.2" baseProfile="tiny">
	optional string svg_begining_tag = 100;

	// board contents, including annotations, images, audio clips and video
	repeated BoardPageElement contents = 110;

	// ending tag of the svg
	// for example:
	// </svg>
	optional string svg_ending_tag = 120;

    // original resource name which generated this page
    // do not set for whiteboards
    optional string original_resource_name = 150;
    // the page number in original resource file which generate this page
    // this is for multi-page document only
    optional uint64 original_page_number = 151;
    // the resource upload sequence in board resource table which generate this page
    optional string original_resource_upload_sequence = 153;
    // to support to create feed for upload resource
    optional uint64 original_resource_sequence = 155;
    // save the original resource sequence of 'original resource'
    // if the original resource is also generated from board resource
    optional uint64 inherited_original_resource_sequence = 156;
    // set to true when orginal resource is copied from page background/vector by server
    // so client can distinguish and hide "view original" option
    optional bool is_original_resource_from_page = 157;

    // to instruct update if revision match
    optional uint64 update_if_revision_match = 161;

    // external resource attached to this page
    repeated BoardResource resources = 200;

    optional string longitude = 215;
    optional string latitude = 216;

    // tags attached to this board page
    repeated BoardTag tags = 220;

    // page number of the board page
    optional string page_number = 230;

    // page group client_uuid this page belongs to
    optional string page_group = 235;
    // sequences on file path, for example: folder_sequence/subfolder_sequence/file_sequence
    optional string file = 236;

    // page type of the board page, client use only
    optional BoardPageType page_type = 240;

    // image only, save original for download purpose
    // this original is the sequence number of the resource in the page resource table
    // no longer used
    optional uint64 original = 241;
    // sequence number of the resource in page resource table
    optional uint64 vector = 244;
    optional uint64 background = 245;
    optional uint64 thumbnail = 246;
    // extracted text for search indexing purpose
    optional uint64 text = 290;

    // output only field for page thumbnail public view token
    optional string thumbnail_view_token = 243;

    // input only field for video length in milliseconds when upload page
    optional uint64 media_length = 242;

    // client use only for temporaly store local path
    optional string original_path = 201;
    optional string vector_path = 247;
    optional string background_path = 248;
    optional string thumbnail_path = 249;

    // page width in pixels
    optional uint64 width = 250;

    // page height in pixels
    optional uint64 height = 260;

    // for page type web: original link
    // for page type url: the url
    optional string url = 270;

    // page rotate in degrees (0, 90, 180, 270)
    optional uint64 rotate = 280;

    // comments on the page
    repeated BoardComment comments = 300;
    //repeated BoardComment position_comments = 303;
    // Statistics numbers, aggregated in biz and for output only
    optional uint64 total_comments = 301;
    // used to calculate position comment index; this will be saved in BoardPage
    optional uint64 total_position_comments = 302;

    // original session key which generated this page
    // only set for pages generted by session recording convert
    optional string original_session_key = 320;

    // board user sequence who is editing the page right now
    optional uint64 editor = 400;
    optional BoardActor editor_actor = 401;
    // the timestamp a user requested to be an editor
    // user has to repeat the request periodically to maintain the editor status
    // otherwise the editor should be considered invalid
    optional uint64 editor_time = 410;

    // indicator what user type can edit the page
    optional BoardEditorType editor_type = 420 [default = EDITOR_TYPE_ALL];

    // flow client_uuid this base object linked to
    //optional string flow = 421;

    // page description
    optional string description = 430;

    // page's vector thumbnail
    optional uint64 vector_thumbnail = 431;
    optional string vector_thumbnail_path = 432;

    // pdf form fields
    repeated BoardPageFormField form_fields = 500;
    optional string card = 510;

    repeated BoardDataReference ddrs = 600;

	optional uint64 sequence = 10;
	// id generated from client id to identify the page when it is received from server
	optional string client_uuid = 20;
	optional uint64 revision = 30;
	optional uint64 local_revision = 35;
    optional bool is_deleted = 40;
    optional string assignments = 41;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

enum BoardEditorType {
    EDITOR_TYPE_ALL = 0;
    EDITOR_TYPE_INTERNAL_ONLY = 10;
    EDITOR_TYPE_ASSIGNEE_ONLY = 20; //todo only
}

message BoardReferenceLink {
    // the referenced file in board
    optional Board board = 20 [lazy = true];

    optional uint64 sequence = 100;
    optional string client_uuid = 5;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum BoardReferenceType {
    REFERENCE_TYPE_ATTACHEMENT = 0;
    REFERENCE_TYPE_SUPPORT_FILE = 5;
    REFERENCE_TYPE_FILE_REPLY = 10;
    REFERENCE_TYPE_PDF_FORM_FILE = 20;
}

message BoardReference {
    // reference to page/page group/resource in board
    optional Board board = 40 [lazy = true];
    // the user sequence in user table who created the page
    optional uint64 creator_sequence = 80;

    // the creator user
    optional User OBSOLETE_user = 81 [lazy = true];
    optional BoardActor creator = 82;
    optional BoardReferenceType type = 90;

    optional uint64 sequence = 100;
    optional string client_uuid = 5;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    // A json string, used to store transaction step sequence
    optional string custom_data = 200;

    // mark field to keep content on deletion, do not fill
    optional bool keep_deleted = 121;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message BoardReminder {
    // the user sequence in user table who created the reminder
    optional uint64 creator_sequence = 80;
    // the creator user
    optional User OBSOLETE_user = 81 [lazy = true];
    optional BoardActor creator = 82;
    optional uint64 reminder_time = 90;
    // milliseconds to remind before an event, -1 means do not remind
    optional int64 reminder_interval = 95;

    optional uint64 sequence = 100;
    optional string client_uuid = 5;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum DueTimeFrameType {
    DUE_TIME_FRAME_DAYS = 0;
    DUE_TIME_FRAME_WEEKS = 10;
}

message BoardTodo {
    // the user sequence in user table who created the page
    optional uint64 creator_sequence = 80;
    // who create the todo
    // only be used if creator_sequence is not available
    optional User OBSOLETE_user = 81 [lazy = true];
    optional BoardActor creator = 82;

    optional string name = 90;
    optional string note = 100;
    optional bool is_marked = 110;
    optional uint64 assignee_sequence = 120;
    optional BoardActor assignee = 121;
    optional uint64 due_date = 130;
    optional DueTimeFrameType due_in_timeframe = 131;
    optional bool exclude_weekends = 132;

    optional bool is_completed = 150;
    optional DetailStatusCode detail_status = 151;

    optional bool is_template = 160; // indicate a template todo
    optional string template_name = 161;
    optional string template_description = 162;
    optional string original_client_uuid = 163; // point to source todo's client_uuid if it's a copied todo

    // for counting total used times of a todo template in content library
    optional uint64 total_used_count = 164;
    // last used timestamp of a todo template in content library
    optional uint64 last_used_timestamp = 165;
    // represent meaningful last modified time
    optional uint64 last_modified_time = 166;

    // string format of float number
    optional string order_number = 170;

    // comments on the todo itme
    repeated BoardComment comments = 300;
    // external resource attached to this page
    repeated BoardResource resources = 350;

    // reference page/resource in current board, which are displayed as attachements in the todo item
    repeated BoardReference references = 400;

    // reminders on the todo item
    repeated BoardReminder reminders = 500;

    // indicator what user type can edit the todo
    optional BoardEditorType editable_editor_type = 510 [default = EDITOR_TYPE_ALL];
    // indicator what user type can complete the todo
    optional BoardEditorType completable_editor_type = 520 [default = EDITOR_TYPE_ALL];


    // flow client_uuid this base object linked to
    //optional string flow = 521;

    optional uint64 pin = 540;

    optional uint64 update_if_revision_match = 800;

    optional uint64 workflow = 1200;
    optional uint64 step = 1210;

    optional uint64 sequence = 10;
    // id generated from client id to identify the page when it is received from server
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional uint64 local_revision = 35;
    optional bool is_deleted = 40;
    optional string assignments = 45;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}


message BoardActor {
    optional User user = 10 [lazy = true];
    // group can also be a board member
    optional Group group = 15 [lazy = true];
    // in meet, use roster to tell who add the element
    optional ActionUserRoster roster = 20;
    // is null actor
    optional bool is_null = 30;
}

// deprecated
enum BoardCallStatus {
    BOARD_CALL_STATUS_INVALID = 0;
    BOARD_CALL_STATUS_ENDED = 10;
    BOARD_CALL_STATUS_CANCELLED = 20;
}

message BoardCallLog {
    optional BoardActor from = 10;
    optional BoardActor to = 20;
    optional uint64 start_time = 30;
    optional uint64 end_time = 40;
    // deprecated, use call_status instead
    optional BoardCallStatus status = 50;
    optional CallStatus call_status = 60;

    // the creator user who create this call log
    optional BoardActor creator = 90;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum BoardSignatureStatus {
    SIGNATURE_STATUS_INVALID = 0;
    SIGNATURE_STATUS_PREPARING = 10;
    SIGNATURE_STATUS_EDITING = 15;
    SIGNATURE_STATUS_IN_PROGRESS = 20;
    SIGNATURE_STATUS_COMPLETED = 30;
    SIGNATURE_STATUS_DECLINED = 40;
}

enum DetailStatusCode {
    DETAIL_STATUS_NONE = 0;
    DETAIL_STATUS_MARK_AS_COMPLETED = 50;
}

enum BoardSigneeStatus {
    SIGNEE_STATUS_NONE = 0;
    SIGNEE_STATUS_SKIPPED = 50;
}

message BoardSignee {
    // the sequence list of BoardPageElement to sign
    repeated uint64 elements = 100;
    repeated uint64 submitted_elements = 110;
    optional BoardActor actor = 200;
    optional bool is_submitted = 210;
    optional BoardSigneeStatus status = 211;
    // signee can add a message when submit
    optional string msg = 220;

    optional string order_number = 300;

    optional uint64 requested_time = 400;
    optional uint64 viewed_time = 410;
    optional uint64 submitted_time = 420;
    optional string submitted_ip = 430;
    // indicate source platform
    optional ObjectFeedViaSource submitted_via = 440;

    optional uint64 signature = 500; // point to the sequence in signature resource table
    optional string initials_text = 502;
    optional SignatureStyle signature_style = 504;

    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional bool is_deleted = 40;
    optional string assignments = 50;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message BoardSignature {
    optional BoardActor creator = 100;
    optional BoardSignatureStatus status = 110;
    optional DetailStatusCode detail_status = 111;
    repeated BoardSignee signees = 120;

    optional bool enable_preparation = 131;
    optional BoardActor editor = 132;

    optional string doc_id = 190;
    optional string name = 200;
    optional string original_name = 201;
    optional bool is_from_pdf_form = 202;
    repeated BoardPage pages = 210;
    optional uint64 original = 220; // point to the converted pdf in Board resource table
    optional uint64 coc = 222; // Certificate of Completion
    optional uint64 original_with_coc = 224; // point to the converted pdf with CoC
    // converted signed file
    // sequences on file path, for example: folder_sequence/subfolder_sequence/file_sequence
    optional string file = 225;

    optional string description = 230;
    optional string order_number = 300;

    optional bool is_template = 350; // indicate a template signature
    optional string template_name = 351;
    optional string template_description = 352;

    optional string original_client_uuid = 360; // point to source signature's client_uuid if it's a copied signature

    // flow client_uuid this base object linked to
    //optional string flow = 321;

    // signees must submit in order if sign_by_order is true
    optional bool sign_by_order = 400 [default = true];
    optional uint64 started_time = 410;
    optional string started_by_ip = 411;
    optional ObjectFeedViaSource started_via = 412;
    optional uint64 ended_time = 420;
    optional uint64 due_date = 430;
    optional DueTimeFrameType due_in_timeframe = 431;
    optional bool exclude_weekends = 432;
    repeated BoardReference references = 440;

    repeated BoardResource resources = 500;
    optional uint64 original_resource_sequence = 510;

    optional uint64 pin = 520;

    // for counting total used times of a signature template in content library
    optional uint64 total_used_count = 530;
    // last used timestamp of a signature template in content library
    optional uint64 last_used_timestamp = 531;
    // represent meaningful last modified time
    optional uint64 last_modified_time = 600;

    optional uint64 workflow = 1200;
    optional uint64 step = 1210;
    optional bool is_workflow_source = 1215; // true for action with automation in content library and workspace
    repeated BoardDataReference ddrs = 1220;

    optional bool has_custom_folder = 1240;
    optional string custom_folder_name = 1241;

    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional uint64 local_revision = 35;
    optional bool is_deleted = 40;
    optional string assignments = 50;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message BoardUserActivity {
    optional BoardActor actor = 100;
    optional uint64 timestamp = 200;

    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional bool is_deleted = 40;
    optional string assignments = 41;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum TransactionActionStyle {
    ACTION_STYLE_BUTTON = 0;
    ACTION_STYLE_CHECKBOX = 10;
}

enum TransactionStepType {
    STEP_TYPE_GENERIC = 0;
    STEP_TYPE_DOCUSIGN_CC = 100;
    STEP_TYPE_REVIEWER = 200;
}

enum TransactionStepStatus {
    STEP_STATUS_INITIAL = 0;
    STEP_STATUS_PENDING = 10;
    STEP_STATUS_COMPLETED = 20;
    STEP_STATUS_CANCELED = 30;
    STEP_STATUS_SKIPPED = 40;
    STEP_STATUS_REOPENED = 50;
}

enum TransactionStatus {
    TRANSACTION_STATUS_EDITING = 5;
    TRANSACTION_STATUS_ACTIVE = 10;
    TRANSACTION_STATUS_INACTIVE = 20;
    TRANSACTION_STATUS_COMPLETED = 30;
    TRANSACTION_STATUS_CANCELED = 40;
    TRANSACTION_STATUS_EXPIRED = 50;
}

message TransactionActionLog {
    // Customized action log id
    optional string id = 100;
    optional string click_btn_id = 200;
    optional uint64 click_btn_timestamp = 300;
    optional string click_btn_from_ip = 400;
    optional string custom_action = 410;

    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional bool is_deleted = 40;
    optional string assignments = 50;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message TransactionElement {
    // JSON string
    optional string string_value = 100;

    optional BoardActor creator = 200;
    optional string order_number = 210;

    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional uint64 local_revision = 35;
    optional bool is_deleted = 40;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message TransactionStep {
    // Customized step id
    optional string id = 100;
    optional BoardActor assignee = 200;
    optional TransactionActionStyle action_style = 290;
    // A json string which has description of action buttons
    optional string actions = 300;
    repeated TransactionActionLog action_logs = 400;
    optional string order_number = 500;
    optional string step_group = 510; // TransactionStepGroup's client_uuid
    optional TransactionStepType type = 590;
    optional TransactionStepStatus status = 600 [default = STEP_STATUS_INITIAL];
    optional uint64 viewed_time = 700;
    // A json string, used to store step data for auto-save and etc
    optional string custom_data = 710;
    // use it to save assignee's data
    repeated TransactionElement contents = 720;

    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional uint64 local_revision = 35;
    optional bool is_deleted = 40;
    optional string assignments = 50;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message TransactionStepGroup {
	optional string name = 100;
	optional StepGroupCompletionType completion_type = 200;

	optional uint64 sequence = 10;
	optional string client_uuid = 20;
	optional uint64 revision = 30;
	optional bool is_deleted = 40;
	optional string assignments = 50;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

enum StepGroupCompletionType {
	STEP_GROUP_COMPLETION_TYPE_ALL = 0;
	STEP_GROUP_COMPLETION_TYPE_ONE = 10;
	STEP_GROUP_COMPLETION_TYPE_MAJORITY = 20;
}

enum TransactionType {
    TRANSACTION_TYPE_GENERIC = 0;
    TRANSACTION_TYPE_APPROVAL = 10;
    TRANSACTION_TYPE_ACKNOWLEDGE = 20;
    TRANSACTION_TYPE_FILE_REQUEST = 30;
    TRANSACTION_TYPE_MEET_REQUEST = 40;
    TRANSACTION_TYPE_FORM_REQUEST = 50;
    TRANSACTION_TYPE_TIME_BOOKING = 60;
    TRANSACTION_TYPE_PDF_FORM = 70;
    TRANSACTION_TYPE_DOCUSIGN = 75;
    TRANSACTION_TYPE_WEBHOOK = 76;
    TRANSACTION_TYPE_LAUNCH_WEB_APP = 77;
    TRANSACTION_TYPE_INTEGRATION = 78;
    TRANSACTION_TYPE_TODO = 79;
    TRANSACTION_TYPE_DECISION = 80;
    TRANSACTION_TYPE_AWAIT = 90;
}

message BoardTransaction {
    optional string id = 90;
    optional string title = 100;
    optional string sub_title = 101;
    // A bbcode string
    optional string content = 110;
    optional RichTextFormat content_format = 111;
    // A json string with fields 'text', 'style' and more
    optional string display_status = 120;
    // The external system will leverage is_active to suspend or resume the transaction
    optional bool is_active = 130;
    optional uint64 expiration_date = 140;
    optional bool is_expired = 141;
    optional DueTimeFrameType due_in_timeframe = 142;
    optional bool exclude_weekends = 143;
    optional TransactionStatus status = 150 [default = TRANSACTION_STATUS_ACTIVE];
    optional DetailStatusCode detail_status = 151;
    optional string callback_url = 160;

    optional bool is_template = 170; // indicate a template transaction
    optional string template_name = 171;
    optional string template_description = 172;
    optional string original_client_uuid = 180; // point to source transaction's client_uuid

    optional TransactionType type = 190;

    optional BoardActor creator = 200;
    optional bool enable_preparation = 211;
    optional BoardActor editor = 212;
    optional bool enable_decline = 220;

    repeated TransactionStep steps = 500;
    repeated TransactionStepGroup step_groups = 510;
    optional uint64 step_timeout = 501;
    repeated BoardReference references = 600;
    repeated BoardViewToken view_tokens = 700;

    optional string card = 800;
    optional string card_description = 801;
    optional string custom_data = 810;
    optional string sub_type = 820;
    repeated BoardResource resources = 830;
    repeated BoardReminder reminders = 840;
    optional string custom_result = 850;
    // use it instead of card to save Form data
    repeated TransactionElement segments = 860;

    optional uint64 pin = 900;

    // for counting total used times of a template in content library
    optional uint64 total_used_count = 920;
    // last used timestamp of a template in content library
    optional uint64 last_used_timestamp = 921;
    // represent meaningful last modified time
    optional uint64 last_modified_time = 923;

    optional uint64 workflow = 1200;
    optional uint64 step = 1210;
    optional bool is_workflow_source = 1220; // true for action with automation in content library and workspace

    optional uint64 original = 1310; // point to the converted pdf in board resource table
    optional uint64 original_masked = 1311; // point to the converted pdf with masked feilds in board resource table
    optional uint64 original_csv = 1312;
    optional uint64 original_csv_masked = 1313;

    optional bool has_custom_folder = 1330;
    optional string custom_folder_name = 1331;

    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional uint64 local_revision = 35;
    optional bool is_deleted = 40;
    optional string assignments = 50;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

//message ThreadBoard
//{
//    optional Board board = 10 [lazy = true];
//
//    optional uint64 sequence = 100;
//    optional string client_uuid = 101;
//    optional uint64 revision = 110;
//    optional bool is_deleted = 120;
//
//    optional uint64 created_time = 1000;
//    optional uint64 updated_time = 1010;
//}
//
//message ThreadFeed
//{
//    optional ObjectFeed feed = 10;
//
//    optional uint64 sequence = 100;
//    optional string client_uuid = 101;
//    optional uint64 revision = 110;
//    optional bool is_deleted = 120;
//    optional string assignments = 130;
//
//    optional uint64 created_time = 1000;
//    optional uint64 updated_time = 1010;
//}
//
//message BoardThread
//{
//    optional ThreadBoard base_object = 100;
//    repeated ThreadFeed activities = 200;
//
//    optional uint64 sequence = 10;
//    optional string client_uuid = 20;
//    optional uint64 revision = 30;
//    optional uint64 local_revision = 35;
//    optional bool is_deleted = 40;
//    optional string assignments = 45;
//
//    optional uint64 created_time = 1000;
//    optional uint64 updated_time = 1010;
//}

enum SocialType {
    SOCIAL_TYPE_INVALID = 0;
    SOCIAL_TYPE_WECHAT = 10;
    SOCIAL_TYPE_LINE = 20;
    SOCIAL_TYPE_WHATSAPP = 30;
}

enum RSVPStatus {
    RSVP_NEEDS_ACTION = 0;
    RSVP_ACCEPTED = 10;
    RSVP_DECLINED = 20;
    RSVP_TENTATIVE = 30;
}

message RSVPReply {
    optional string from_email = 100;
    // the original board resource sequence of ics
    optional uint64 original = 110;

    optional string timezone = 600 [default = "America/Los_Angeles"];
    optional uint64 dtstart = 610;
    optional uint64 dtend = 611;
    optional string rrule = 620;
    optional RSVPStatus partstat = 700;

    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional uint64 local_revision = 35;
    optional bool is_deleted = 40;
    optional string assignments = 41;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message BoardUserRSVP {
    optional BoardActor actor = 100;
    repeated RSVPReply replies = 110;
    repeated BoardResource resources = 200;

    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional uint64 local_revision = 35;
    optional bool is_deleted = 40;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message BoardPin {
    optional BoardActor actor = 100;
    optional Board board = 110 [lazy = true];

    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional bool is_deleted = 40;
    optional string assignments = 50;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message BoardDataReference {
    optional string id = 100;
    optional string value = 120;
    optional string label = 130;

    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional bool is_deleted = 40;
    optional string assignments = 50;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum WorkflowType {
    WORKFLOW_TYPE_DEFAULT = 0;
    WORKFLOW_TYPE_FLEXIBLE = 10; // PRC-451: the flexible paralled flow
}

enum WorkflowStatus {
    WORKFLOW_STATUS_INITIAL = 0;
    WORKFLOW_STATUS_RUNNING = 10;
    WORKFLOW_STATUS_COMPLETED = 20;
    WORKFLOW_STATUS_CANCELED = 30;
    WORKFLOW_STATUS_FAULTED = 40;
}

enum WorkflowVarParameter {
    // if the var place holder ${var} is in BoardActor.user.id,
    // server can update the var string_value to BoardActor.group.id
    VAR_PARAM_USER_ID_TO_GROUP_ID = 10;
    // query repeated fields, and convert result to a JSON array.
    VAR_PARAM_QUERY_REPEATED = 20;
    VAR_PARAM_EXPEND_ARRAY = 30;
    // define the string format of var value
    VAR_PARAM_FORMAT = 40;
}

message WorkflowVarParam {
    optional WorkflowVarParameter name = 10;
    optional string string_value = 20;
    optional uint64 uint64_value = 30;
}

enum WorkflowVarType {
    VAR_TYPE_DEFAULT = 0;
    VAR_TYPE_ALL_FILES = 100;
    VAR_TYPE_FORM_PDF = 101;
    VAR_TYPE_FORM_CSV = 102;
    VAR_TYPE_WORKSPACE_VARIABLE = 200;
    VAR_TYPE_WORKSPACE_EMAIL = 210;
    VAR_TYPE_WORKSPACE_URL = 220;
}

message WorkflowVar {
    optional string name = 10;
    optional string string_value = 20;
    optional string default_value = 21;
    optional BoardActor default_actor = 22;

    // placeholder of variable
    optional string label = 30;
    optional string refer_to = 40;
    optional bool resolved = 41;

    // type is a string, app can define different types for Form/Integration/etc
    // if type is not set or empty, it means the var is used for Role, to keep compatiable with old app
    optional string type = 50;
    optional string custom_data = 60;

    repeated WorkflowVarParam params = 70;

    optional WorkflowVarType var_type = 80;
    optional string step_uuid = 81;
    optional uint64 timeout = 82;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum WorkflowStepType {
    WORKFLOW_STEP_TYPE_INVALID = 0;
    WORKFLOW_STEP_TYPE_MILESTONE = 1;
    WORKFLOW_STEP_TYPE_AB = 2; // represent an AB step
    WORKFLOW_STEP_TYPE_AUTOMATION = 3;
    WORKFLOW_STEP_TYPE_CB = 4;
    WORKFLOW_STEP_TYPE_SHADOW_FLOW = 5;
    WORKFLOW_STEP_TYPE_SEND_FILE = 10;
    WORKFLOW_STEP_TYPE_TRANSACTION = 19;
    WORKFLOW_STEP_TYPE_FORM_REQUEST = 20;
    WORKFLOW_STEP_TYPE_FILE_REQUEST = 30;
    WORKFLOW_STEP_TYPE_MEET_REQUEST = 31;
    WORKFLOW_STEP_TYPE_APPROVAL = 40;
    WORKFLOW_STEP_TYPE_ACKNOWLEDGE = 41;
    WORKFLOW_STEP_TYPE_SIGNATURE = 50;
    WORKFLOW_STEP_TYPE_TODO = 60;
    WORKFLOW_STEP_TYPE_TIME_BOOKING = 65;
    WORKFLOW_STEP_TYPE_DOCUSIGN = 70;
    WORKFLOW_STEP_TYPE_WEBHOOK = 71;
    WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP = 72;
    WORKFLOW_STEP_TYPE_INTEGRATION = 73;
    WORKFLOW_STEP_TYPE_TODO_TRANSACTION = 74;
    WORKFLOW_STEP_TYPE_DECISION = 75;
    WORKFLOW_STEP_TYPE_AWAIT = 76;
    WORKFLOW_STEP_TYPE_PDF_FORM = 77;
    WORKFLOW_STEP_TYPE_SHADOW_ACTION = 78;
}

enum WorkflowStepStatus {
    WORKFLOW_STEP_STATUS_INITIAL = 0;
    WORKFLOW_STEP_STATUS_PREPARING = 5;
    WORKFLOW_STEP_STATUS_READY = 6;
    WORKFLOW_STEP_STATUS_STARTED = 10;
    WORKFLOW_STEP_STATUS_COMPLETED = 20;
    WORKFLOW_STEP_STATUS_CANCELED = 30;
    WORKFLOW_STEP_STATUS_FAULTED = 40;
    WORKFLOW_STEP_STATUS_SKIPPED = 50;
    WORKFLOW_STEP_STATUS_PRECONDITION_NOT_MET = 60;
}

message WorkflowOutgoing {
    optional string name = 10;
    optional ObjectFeed feed = 20;
    optional string payload = 30;
    optional string webhooks = 40;
    optional string triggers = 50;
    optional string integrations = 60;
    // configuration for non-automation event sending to integration server
    optional string integrations_ext = 61;
    optional WorkflowOutgoingQueueType queue_type = 70;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum WorkflowOutgoingQueueType {
    QUEUE_TYPE_GROUP = 0;
    QUEUE_TYPE_BOARD = 10;
}

enum WorkflowConditionCategory {
    CONDITION_DEFAULT = 0;
    CONDITION_EXECUTION_ORDER = 10;
    CONDITION_EXECUTION_BRANCH = 20;
    CONDITION_EXECUTION_MIXED = 30;
}

message WorkflowCondition {
    optional string name = 10;

    optional string expression = 20;
    optional string result = 30;
    // result before resolved. server use only
    optional string original_result = 31;
    optional bool resolved = 40;

    optional WorkflowConditionCategory category = 50;

    // step precondition is either expression or following fields specified.
    optional string conditional_step = 200; // client_uuid of DB/CB step
    repeated string waiting_steps = 210; // client_uuid of dependent steps
    repeated string waiting_milestones = 220; // client_uuid of dependent milestones
    repeated string waiting_conditional_steps = 230; // client_uuid of DB/CB steps, represents dependent all branch steps of the DB/CB steps.

    // key-value variables for prepared_expression
    repeated WorkflowVar variables = 310;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional uint64 local_revision = 130;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum WorkflowActionType {
    WORKFLOW_ACTION_TYPE_INVALID = 0;
    WORKFLOW_ACTION_TYPE_SMS = 10;
    WORKFLOW_ACTION_TYPE_EMAIL = 20;
    WORKFLOW_ACTION_TYPE_COMMENT = 30;
    WORKFLOW_ACTION_TYPE_APPROVAL = 40;
    WORKFLOW_ACTION_TYPE_ACKNOWLEDGE = 41;
    WORKFLOW_ACTION_TYPE_SIGNATURE = 50;
    WORKFLOW_ACTION_TYPE_TODO = 60;
}

enum WorkflowActionStatus {
    WORKFLOW_ACTION_STATUS_INITIAL = 0;
    WORKFLOW_ACTION_STATUS_RUNNING = 10;
    WORKFLOW_ACTION_STATUS_COMPLETED = 20;
    WORKFLOW_ACTION_STATUS_FAULTED = 30;
}

message WorkflowAction {
    optional string name = 10;
    optional WorkflowActionStatus status = 20;

    optional WorkflowActionType type = 40; // define what to do 
    optional string board_id = 60; // related base object and resource is stored in a board
    optional string board_view_token = 70; // view token used to read the board
    optional BoardReference input = 80; // the action input, include the email/phone_number list
    optional string destination_board_id = 90;
    optional BoardReference output = 200; // the action result, the targe binder id

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message WorkflowCheckpoint {
    optional string name = 10;

    optional ObjectFeed feed = 20;
    optional WorkflowAction action = 30;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message WorkflowStep {
    optional string name = 10;
    optional string description = 11;
    optional string order_number = 20;
    optional bool is_parallel_with_prev_step = 25;
    optional WorkflowStepStatus status = 30; 
    optional bool enable_preparation = 31;
    optional BoardActor editor = 32;
    optional bool hide_before_started = 33;

    optional WorkflowStepType type = 40; // define what to do, such as uploading file
    optional string board_id = 60; // related base object and resource is stored in a board
    optional string board_view_token = 70; // view token used to read the board
    optional BoardReference input = 80; // only used in workflow template, user inputs based on the base object

    optional BoardReference output = 200; // only used in workflow instance, the step's result 

    repeated WorkflowOutgoing outgoings = 300; // automations and integrations

    optional string outbound_data = 310; // JSON string for the data used by external system
    optional string inbound_data = 320; // JSON response received from external system
    
    optional string parent_step = 400; // the parent step's client_uuid
    optional bool is_optional = 500; // if true, user can skip this step
    repeated WorkflowCondition preconditions = 600; // if precondition not met, won't do this step
    optional WorkflowCondition condition = 650; // condition for AB step
    repeated WorkflowCheckpoint checkpoints = 700;
    
    optional bool is_holding = 800; // holding to start before referred ddr resolved
    optional string custom_data = 900; // for storing steps of main and side branches controlled by a AB step

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional uint64 local_revision = 130;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message WorkflowObject {
    optional Board board = 10 [lazy = true];
    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 131;
    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message WorkflowMilestone {
    optional string name = 10;
    optional string description = 11;
    optional string order_number = 20;

    optional uint64 processed_steps = 30;
    optional uint64 total_steps = 40;

    // JSON string, used to store milestone settings: 
    // follow workflow process order or not, dependecy, etc
    optional string custom_data = 50;

    repeated WorkflowCondition preconditions = 60; // if precondition not met, won't process the steps in this milestone 

    optional bool reverse_workflow_order=70;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional uint64 local_revision = 130;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum WorkflowTriggerType {
    WORKFLOW_TRIGGER_TYPE_NONE = 0;
    WORKFLOW_TRIGGER_TYPE_WEBHOOK = 10;
    WORKFLOW_TRIGGER_TYPE_APPS = 20;
}

enum WorkflowTriggerError {
    WORKFLOW_TRIGGER_ERROR_NONE = 0;
    WORKFLOW_TRIGGER_ERROR_INVALID_CREDENTIAL = 10;
    WORKFLOW_TRIGGER_ERROR_SYSTEM = 20;
}

message BoardWorkflow {
    optional BoardActor creator = 100;
    // who uses this workflow
    optional BoardActor user = 101;
    optional string template_board_id = 110;
    // PRC-480: scheduled flow can auto update template from the original template
    optional string original_template_board_id = 111;
    optional bool update_from_original = 112;
    // output template name for user activity
    optional string template_name = 120; 
    optional string name = 200;
    optional string description = 210;
    // the welcome message in workflow template
    optional string welcome_msg = 211;
    // point to the created comment
    optional uint64 welcome_msg_comment_sequence = 212;
    // the failed reason of workflow instance
    optional string error_msg = 213;
    optional ClientResponseCode error_code = 214;

    optional WorkflowStatus status = 220;

    // obsoleted
    optional string definition = 230;
    optional string nodes = 240;
    repeated WorkflowObject objects = 250;
    
    // progress percentage = 100*processed_steps/total_steps
    optional uint64 processed_steps = 300;
    optional uint64 total_steps = 310;
    optional uint64 current_step = 320;

    optional bool is_template = 400;
    optional bool is_active = 401;
    optional bool process_in_parallel = 402;
    optional WorkflowType type = 403;
    repeated WorkflowVar variables = 410;
    repeated WorkflowStep steps = 420;
    repeated WorkflowMilestone milestones = 430;

    repeated WorkflowOutgoing outgoings = 500; // automations and integrations

    // for counting total used times of a template in library
    optional uint64 total_used_count = 600;
    // last used timestamp of a template in library
    optional uint64 last_used_timestamp = 610;
    // represent meaningful last modified time
    optional uint64 last_modified_time = 611;
    // represent the workflow completed time
    optional uint64 completed_time = 612;

    optional uint64 original_signature = 625; // indicate it is a flow of the e-sign 
    optional uint64 original_transaction = 626; // indicate it is a flow of the transaction

    // for rest api to save & get reference_id from board workflow
    optional string reference_id = 700;

    optional WorkflowTriggerType trigger_type = 800;
    optional uint64 trigger_activation_time = 810;
    optional WorkflowTriggerError trigger_error = 820;
    
    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional uint64 local_revision = 35;
    optional bool is_deleted = 40;
    optional string assignments = 50;

    optional Board internal_board = 2222; // iSDK internal use only
    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum BoardType {
    BOARD_TYPE_DEFAULT = 0;
    BOARD_TYPE_WORKFLOW = 100;
    BOARD_TYPE_WORKFLOW_TEMPLATE = 200;
    BOARD_TYPE_CONTENT_LIBRARY_ACTION = 300;
    BOARD_TYPE_CONTENT_LIBRARY_FILE = 400;
    BOARD_TYPE_CONTENT_LIBRARY_MILESTONE = 800;
    BOARD_TYPE_SCHEDULE = 500;
    BOARD_TYPE_SELF_SERVICE_TEMPLATE = 600;
    BOARD_TYPE_WORKFLOW_TEMPLATE_FOLDER = 700;
    BOARD_TYPE_BROADCAST = 900;
    BOARD_TYPE_AI = 1000;
}

message BoardProperty {
    optional uint64 group_sequence = 100;
    optional string name = 110;
    optional string value = 120;

    optional uint64 sequence = 10;
    optional string client_uuid = 20;
    optional uint64 revision = 30;
    optional bool is_deleted = 40;
    optional string assignments = 50;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

enum WaitingRoomAudience {
    WAITING_ROOM_AUDIENCE_GUEST = 0;
    WAITING_ROOM_AUDIENCE_ALL = 10;
}

// S3 Storage Object (as part of CacheObject) with Hash_BoardID as Key
message Board {
    // GUID generate from server side to represent the id of the board
    optional string id = 10;

    // id generated from client side to identify the board when it is received from server
    optional string client_uuid = 20;

    // is the board dedicated for a live session
    optional bool islive = 50;

    // is private conversation
    optional bool isconversation = 51;

    // is default
    optional bool isdefault = 52;

    // is web moxtra note
    optional bool isnote = 53;

    // is temp board, which will not be put in user board table
    optional bool istemp = 54;

    // is restricted to group members only
    optional bool is_restricted = 55;

    // is team binder
    optional bool is_team = 56;

    optional bool use_member_name_as_name = 57;
    optional bool use_member_avatar_as_cover = 58;

    // WebApp id if the board is App-Enabled, support for webapp removed on May 22, 2014. Bingo
    //optional WebApp webapp = 60;

    // is relationship binder
    optional bool is_relation = 61;

    // is transaction binder
    optional bool is_transaction = 62;

    optional bool is_inactive = 63;
    // the timestamp when board is set inactive
    optional uint64 inactive_time = 64;

    // is bot relationship binder
    optional bool is_bot_relation = 65;

    // used in feed only, to indicate it is a duplicate of another binder
    optional bool is_duplicate = 70;

    optional bool is_file = 71; 
    optional bool is_todo = 72;
    optional bool is_signature = 73;

    // if true, biz will disable certain permission check for presenter/host role in meet
    optional bool is_flexible = 80;

    // indicate if the board has external members
    optional bool is_external = 81;

    // used with islive together if the board is an audio call
    optional bool iscall = 82;

    // indicate if the board is client inbox board
    optional bool is_inbox = 83;

    // used to generate session flow into the original board
    //optional bool is_flow_enabled = 83;

    // indicate if the board is personal meeting room
    optional bool is_personal_room = 84;

    // max personal room waiting timeout in milliseconds
    optional uint64 milliseconds_personal_room_waiting_timeout = 85;

    // indicate if the board was create from group integration
    optional bool is_app_subscription = 90;
    optional bool is_shadow_flow = 91;

    // board level delegate option
    optional bool is_owner_delegate_enabled = 95;

    // represent social board's type
    optional SocialType social_type = 96;

    // allow Client user to edit flow and action object
    optional bool is_client_editing_enabled = 97;

    // the name of the board
    optional string name = 100;
    optional string description = 101;
    optional BoardType type = 102;
    optional string workspace_id = 103;
    optional string invite_code = 104;

    optional string thumbnail_need_migrate = 110;
    // sequence number of the resource in board resource table
    optional uint64 thumbnail = 111;
    // output only field to thumbnail view token
    optional string thumbnail_view_token = 112;
    optional uint64 banner = 113;
    optional uint64 banner_mobile = 114;

    // sequence number of the resource in board resource table
    optional uint64 board_pdf = 120;
    optional uint64 board_ppt = 121;
    optional uint64 board_recording = 122;

    // sequence number of the board page which board thumbnail comes from
    optional uint64 thumbnail_source_page = 130;
    // sequence of board resource which thumbnail comes from
    optional uint64 thumbnail_source_resource = 131;

    // bind board to an email adress
    optional string email_address = 140;
    // bind board to a phone number
    optional string phone_number = 150;

    optional bool is_acd = 161;
    optional bool is_service_request = 162;
    optional BoardRoutingStatus routing_status = 163;
    optional uint64 routing_channel = 164;

    optional bool is_channel_subscription = 170;

    optional bool is_content_library = 171;

    optional bool is_client_resources = 172;

    // enable session waiting room
    optional bool enable_waiting_room = 175;
    optional WaitingRoomAudience waiting_room_audience = 176; //available only if enable_waiting_room=true

    // invited teams in board
    repeated BoardUser teams = 180;

    // the files of the board
    repeated BoardPage pages = 200;

    // external resource attached to this board
    repeated BoardResource resources = 210;

    // tags attached to this board
    repeated BoardTag tags = 220;

    // page groups of this board
    repeated BoardPageGroup page_groups = 240;

    // public view tokens, only last one take effect
    repeated BoardViewToken view_tokens = 250;

    // folders of this board
    repeated BoardFolder folders = 260;

    // reference links of this board
    // each one is a link points to a file in this board
    repeated BoardReferenceLink reference_links = 270;

    // Users who has access to the board
    repeated BoardUser users = 300;
    // board owner sequence for fast owner lookup
    optional uint64 owner = 310;
    // board user's RSVP (reply) from app or calendar
    repeated BoardUserRSVP user_rsvps = 320;

    // Sessions history happened to the board
    // and scheduled sessions
    repeated BoardSession sessions = 400;
    repeated BoardCallLog calls = 410;

    // convert jobs for the board
    // repeated BoardJob jobs = 500;

    // comments in board
    repeated BoardComment comments = 700;

    // Board todos
    repeated BoardTodo todos = 710;

    repeated BoardSignature signatures = 720;

    repeated BoardTransaction transactions = 725;

    repeated BoardWorkflow workflows = 750;

    // Board feeds
    repeated ObjectFeed feeds = 800;

    // Board flows
    //repeated BoardFlow flows = 810;
    //repeated BoardFlow flow_templates = 815;

    // used together with is_personal_room to store waiting list rosters
    repeated ActionUserRoster waiting_users = 820;

    repeated BoardUserActivity user_activities = 830;
    optional uint64 user_activities_last = 840;

    //repeated BoardThread threads = 850;
    optional BoardEditorType pin_editor_type = 860; 
    repeated BoardPin pins = 870;
    repeated BoardReminder reminders = 880;

    // users who are requesting to join the board
    repeated BoardUser requesting_users = 890;

    // Statistics numbers, aggregated in biz and for output only
    optional uint64 total_pages = 900;
    optional uint64 total_members = 910;
    optional uint64 total_comments = 920;
    optional uint64 total_todos = 930;
    optional uint64 total_open_todos = 931;
    optional bool has_folder = 940;
    optional uint64 total_signatures = 950;
    optional uint64 total_emails = 951;
    optional uint64 total_hits = 952;
    optional uint64 total_creators = 953;
    optional uint64 total_transactions = 954;    
    optional uint64 total_pins = 955;
    //optional uint64 total_recordings = 952;
    optional uint64 total_open_signatures = 956;
    optional uint64 total_open_transactions = 957;

    // Board total size in bytes, by adding all files together
    optional uint64 total_size = 1200;

    optional string access_control_board_id = 1600;

    optional BoardMemberNotificationSetting board_member_notification_settings = 1701; 
    optional ActionNotificationSetting action_notification_settings = 1702;
    optional BoardNotificationSetting board_notification_settings = 1703;

    // archive userboard when workflow is completed and no feed for N days, in milliseconds
    optional uint64 archive_after = 1710;

    repeated BoardProperty properties = 1720;

    optional uint64 previous_due_date = 1739; // for FEED_BOARD_DUE_DATE_UPDATE only
    optional uint64 due_date = 1740;
    optional DueTimeFrameType due_in_timeframe = 1741;
    optional bool exclude_weekends = 1742;

    // the original board where this board is used
    // for shadow flow template/instance case, it points to the main flow template/instance board
    optional string original_board_id = 1730;
    
    repeated BoardBroadcast broadcasts = 1800;

    // User object is a main object and it doesn't have a sequence
    optional uint64 revision = 30;
    // for partial subscription data, the base revision which the partial data can be applied on
    // this is a output only field
    //optional uint64 previous_revision = 31;
    optional bool is_deleted = 40;
    optional string assignments = 41;
    optional uint64 local_revision = 35;
    // to instruct the index job to re-index board local fields
    optional uint64 index_local_field_after_revision = 36;

    optional uint64 index_revision = 32;
    optional uint64 index_version = 33;

    optional uint64 revision_in_previous_update_member_job = 1300;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

// ===================================================
// Combined Push Notification
// includes Apple and Android
// ===================================================
message CombinedPushNotificationPayload {
    // for iOS
    optional ApplePushNotificationPayload payload = 10;

    // for Android
    optional GCMPushNotificationData data = 100;
}

// ===================================================
// Apple Push Notification
// push notification json result map to protocol buffers
// ===================================================

message ApplePushNotification {
    optional ApplePushNotificationPayload payload = 10;
    optional string apple_device_token = 20;
    repeated string apple_device_tokens = 21;
    optional uint64 message_sequence = 30;
    optional string user_id = 40;
    // client_id will only appears when send notification through proxy_url
    optional string client_id = 50;
    // use Apple voip service certificate to send notification
    optional bool is_voip = 60;

    // carry gcm payload in notification through proxy
    repeated string registration_ids = 100;
    optional GCMPushNotificationData data = 200; 
}

message ApplePushNotificationPayload {
    optional ApplePushNotificationAps aps = 10;
    // DEPRECATED for new notifications since v2.5, use id instead
    optional ClientRequest request = 20;
    // "id": "userboard_sequence"_"feed_sequence"
    // "id": "usercalllog_sequence"
    optional string id = 30;
    optional string moxtra = 40;

    // payload for notification through proxy
    optional string sender_org_id = 100;
    optional string sender_unique_id = 101;
    optional string sender_name = 102;
    optional string receiver_org_id = 110;
    optional string receiver_unique_id = 111;
    optional string receiver_language = 112;
    optional string receiver_email = 113;
    optional string receiver_phone_number = 114;
    optional string category = 120;
    optional string board_id = 130;
    optional bool is_privacy = 140;
    optional string original_loc_key = 150;
    optional string original_action_loc_key = 160;
    // client prefers to use the same loc_key for transaction/approval/ack/etc notifications 
    // to keep compatible on old app, but need use different strings for these notifications
    optional string extended_loc_key = 170;
    optional string workspace_type = 180;
}

message ApplePushNotificationAps {
    // If this property is included, the system displays a standard alert. You may specify a string as the value of alert or a dictionary as its value. If you specify a string, it becomes the message text of an alert with two buttons: Close and View. If the user taps View, the app is launched.
    optional ApplePushNotificationAlert alert = 10;
    // The number to display as the badge of the app icon. If this property is absent, the badge is not changed. To remove the badge, set the value of this property to 0.
    optional uint64 badge = 20;
    optional string sound = 30;
    // Provide this key with a value of 1 to indicate that new content is available. Including this key and value means that when your app is launched in the background or resumed, application:didReceiveRemoteNotification:fetchCompletionHandler: is called.
    optional uint64 content_available = 40;
    optional string category = 50;
}

message ApplePushNotificationAlert {
    optional string title = 5;
    optional string body = 10;
    optional string action_loc_key = 20;
    optional string loc_key = 30;
    repeated string loc_args = 40;
    optional string launch_image = 50;
}

message ApplePushNotificationResponse {
    optional string reason = 10;
    optional uint64 timestamp = 20;
}

// ===================================================
// GCM Push Notification for Android device
// push notification json result map to protocol buffers
// ===================================================

message GCMPushNotification {
    repeated string registration_ids = 10;
    optional GCMPushNotificationData data = 20;
    // client_id will only appears when send notification through proxy_url
    optional string client_id = 30;
    // set notification priority, hign or normal
    optional string priority = 40;

    optional string restricted_package_name = 50;

    optional GCMPushNotificationMessage notification = 60;

}

message GCMPushNotificationMessage {
    // https://firebase.google.cn/docs/cloud-messaging/http-server-ref?authuser=0#send-downstream
    optional string title = 10;
    optional string body = 20;
    optional string click_action = 30;
    optional string android_channel_id = 40;
    optional string tag = 50;

}

message GCMPushNotificationData {
    optional string body = 10;
    optional string action_loc_key = 20;
    optional string loc_key = 30;

    optional string arg1 = 100;
    optional string arg2 = 110;
    optional string arg3 = 120;
    optional string arg4 = 130;

    optional uint64 badge = 200;
    optional string sound = 210;

    // DEPRECATED for new notifications since v2.5, use id instead
    optional string session_key = 300;
    optional string board_id = 310;
    optional uint64 page_sequence = 340;
    optional uint64 feed_sequence = 350;
    // used to check if this is the target user on client
    optional string user_id = 360;
    // "id": "userboard_sequence"_"feed_sequence"
    // "id": "usercalllog_sequence"
    optional string id = 370;
    optional string moxtra = 380;
    optional string request = 390; // the json string of client request
    optional string title = 400;
    // DEPRECATED, it is not used since android app 3.0
    // used to identify notification message
    // client will use it to filter duplicate messages from gcm and websocket
    //optional string sequence = 400;

    optional string board_name = 500;
    optional uint64 board_feed_unread_count = 510;

    // payload for notification through proxy
    optional string sender_org_id = 600;
    optional string sender_unique_id = 601;
    optional string sender_name = 602;
    optional string receiver_org_id = 610;
    optional string receiver_unique_id = 611;
    optional string receiver_language = 612;
    optional string receiver_email = 613;
    optional string receiver_phone_number = 614;
    optional string category = 620;
    optional bool is_privacy = 630;
    optional string original_loc_key = 640;
    optional string original_action_loc_key = 650;
    // client prefers to use the same loc_key for transaction/approval/ack/etc notifications 
    // to keep compatible on old app, but need use different strings for these notifications
    optional string extended_loc_key = 660;
    optional string workspace_type = 670;
}

// ===================================================
// GCM integration part
// GCM response json result map to protocol buffers
// ===================================================

message GCMPushNotificationResponse {
    optional string multicast_id = 10;
    optional uint64 success = 20;
    optional uint64 failure = 30;
    optional uint64 canonical_ids = 40;
    repeated GCMResult results = 50;
}

message GCMResult {
    optional string message_id = 10;
    optional string registration_id = 20;
    optional string error = 30;
}

message PushNotificationProxyResponse {
    optional string message = 10;
    optional uint64 timestamp = 20;
    optional string user_id = 30;
    repeated string bad_apple_device_tokens = 100;
    repeated string bad_registration_ids = 200;
}

// =============================================
// Recording and Feed part
// =============================================

enum ObjectFeedType {
    FEED_INVALID = 0;

    FEED_BOARD_CREATE = 100;
    FEED_BOARD_NAME_CHANGE = 101;
    FEED_BOARD_COMMENT = 102;
    // digest email output only, not in real data
    FEED_BOARD_VOICE_COMMENT = 103;
    FEED_BOARD_COMMENT_DELETE = 104;

    FEED_BOARD_DUE_DATE_UPDATE = 107;
    FEED_BOARD_DUE_DATE_ARRIVE = 108;

    // pages create, group by same kind of page type
    // multi-page document type group by itself
    FEED_PAGES_CREATE = 200;
    // DEPRECATED
    FEED_PAGES_CREATE_WITH_ANNOTATION = 201;

    // annotation on previous uploaded document, or document uploaded by other people
    FEED_PAGES_ANNOTATION = 230;
    // page updated, webdoc changed
    FEED_PAGES_UPDATE = 240;
    FEED_PAGES_DELETE = 250;
    FEED_PAGES_COMMENT = 260;
    FEED_PAGES_POSITION_COMMENT = 261;
    FEED_PAGES_COMMENT_DELETE = 262;

    // feeds for file support
    FEED_PAGES_RENAME = 270;
    FEED_PAGES_RECYCLE = 271;
    FEED_PAGES_MOVE = 272;

    // receive email to board id
    // the feed can includes board resource, comment, and pages
    FEED_EMAIL_RECEIVE = 300;

    // relationship feed
    // user accept the invitation
    FEED_RELATIONSHIP_JOIN = 500;
    // member leave the member by himself
    FEED_RELATIONSHIP_LEAVE = 501;
    // invitation
    FEED_RELATIONSHIP_INVITE = 502;
    // user decline the invitation
    FEED_RELATIONSHIP_DECLINE = 503;
    // invitation was cancelled by a board member
    FEED_RELATIONSHIP_CANCEL = 504;
    // a member was removed by a board member
    FEED_RELATIONSHIP_REMOVE = 505;
    // change to member/invitee's access type
    FEED_RELATIONSHIP_CHANGE_ROLE = 506;

    // todo list feed
    FEED_TODO_CREATE = 600;
    FEED_TODO_CREATE_WITH_RESOURCE = 601;
    FEED_TODO_UPDATE = 602;
    FEED_TODO_DELETE = 603;
    FEED_TODO_ASSIGN = 604;
    FEED_TODO_COMMENT = 605;
    FEED_TODO_ATTACHMENT = 606;
    FEED_TODO_DUE_DATE = 607;
    FEED_TODO_COMPLETE = 608;
    // reopen a completed todo
    FEED_TODO_REOPEN = 609;
    FEED_TODO_DUE_DATE_ARRIVE = 610;
    FEED_TODO_COMMENT_DELETE = 611;
    FEED_TODO_MARK_AS_COMPLETED = 612;

    // added in Flow design
    //FEED_FLOW_PART_1_MIN = 620;

    //FEED_FLOW_DUE_DATE = 622;
    //FEED_FLOW_DUE_DATE_ARRIVE = 623;
    //FEED_FLOW_ASSIGN = 624;
    //FEED_FLOW_COMPLETE = 625;
    //FEED_FLOW_REOPEN = 626;
    //FEED_FLOW_TODO_ON = 627;
    //FEED_FLOW_TODO_OFF = 628;

    //FEED_COMMENT_FLOW_COMMENT = 630;
    //FEED_PAGE_FLOW_COMMENT = 631;
    //FEED_FILE_FLOW_COMMENT = 632;
    //FEED_PAGE_POSITION_COMMENT_FLOW_COMMENT = 633;
    //FEED_TODO_FLOW_COMMENT = 634;
    //FEED_SESSION_FLOW_COMMENT = 635;
    //FEED_SIGN_FLOW_COMMENT = 636;
    //FEED_PURE_FLOW_COMMENT = 637;

    // generic feed for flow comment deletion
    //FEED_FLOW_COMMENT_DELETE = 638;

    //FEED_COMMENT_FLOW_ATTACHMENT = 640;
    //FEED_PAGE_FLOW_ATTACHMENT = 641;
    //FEED_FILE_FLOW_ATTACHMENT = 642;
    //FEED_PAGE_POSITION_COMMENT_FLOW_ATTACHMENT = 643;
    //FEED_TODO_FLOW_ATTACHMENT = 644;
    //FEED_SESSION_FLOW_ATTACHMENT = 645;
    //FEED_SIGN_FLOW_ATTACHMENT = 646;
    //FEED_PURE_FLOW_ATTACHMENT = 647;

    //FEED_COMMENT_FLOW_CHECKLIST_CREATE = 650;
    //FEED_PAGE_FLOW_CHECKLIST_CREATE = 651;
    //FEED_FILE_FLOW_CHECKLIST_CREATE = 652;
    //FEED_PAGE_POSITION_COMMENT_FLOW_CHECKLIST_CREATE = 653;
    //FEED_TODO_FLOW_CHECKLIST_CREATE = 654;
    //FEED_SESSION_FLOW_CHECKLIST_CREATE = 655;
    //FEED_SIGN_FLOW_CHECKLIST_CREATE = 656;
    //FEED_PURE_FLOW_CHECKLIST_CREATE = 657;

    //FEED_COMMENT_FLOW_CHECKLIST_UPDATE = 660;
    //FEED_PAGE_FLOW_CHECKLIST_UPDATE = 661;
    //FEED_FILE_FLOW_CHECKLIST_UPDATE = 662;
    //FEED_PAGE_POSITION_COMMENT_FLOW_CHECKLIST_UPDATE = 663;
    //FEED_TODO_FLOW_CHECKLIST_UPDATE = 664;
    //FEED_SESSION_FLOW_CHECKLIST_UPDATE = 665;
    //FEED_SIGN_FLOW_CHECKLIST_UPDATE = 666;
    //FEED_PURE_FLOW_CHECKLIST_UPDATE = 667;

    //FEED_COMMENT_FLOW_CHECKLIST_DELETE = 670;
    //FEED_PAGE_FLOW_CHECKLIST_DELETE = 671;
    //FEED_FILE_FLOW_CHECKLIST_DELETE = 672;
    //FEED_PAGE_POSITION_COMMENT_FLOW_CHECKLIST_DELETE = 673;
    //FEED_TODO_FLOW_CHECKLIST_DELETE = 674;
    //FEED_SESSION_FLOW_CHECKLIST_DELETE = 675;
    //FEED_SIGN_FLOW_CHECKLIST_DELETE = 676;
    //FEED_PURE_FLOW_CHECKLIST_DELETE = 677;

    //FEED_COMMENT_FLOW_SUBTASK_CREATE = 680;
    //FEED_PAGE_FLOW_SUBTASK_CREATE = 681;
    //FEED_FILE_FLOW_SUBTASK_CREATE = 682;
    //FEED_PAGE_POSITION_COMMENT_FLOW_SUBTASK_CREATE = 683;
    //FEED_TODO_FLOW_SUBTASK_CREATE = 684;
    //FEED_SESSION_FLOW_SUBTASK_CREATE = 685;
    //FEED_SIGN_FLOW_SUBTASK_CREATE = 686;
    //FEED_PURE_FLOW_SUBTASK_CREATE = 687;

    //FEED_COMMENT_FLOW_SUBTASK_UPDATE = 690;
    //FEED_PAGE_FLOW_SUBTASK_UPDATE = 691;
    //FEED_FILE_FLOW_SUBTASK_UPDATE = 692;
    //FEED_PAGE_POSITION_COMMENT_FLOW_SUBTASK_UPDATE = 693;
    //FEED_TODO_FLOW_SUBTASK_UPDATE = 694;
    //FEED_SESSION_FLOW_SUBTASK_UPDATE = 695;
    //FEED_SIGN_FLOW_SUBTASK_UPDATE = 696;
    //FEED_PURE_FLOW_SUBTASK_UPDATE = 697;

    //FEED_FLOW_PART_1_MAX = 699;

    //FEED_FLOW_PART_2_MIN = 709;

    //FEED_COMMENT_FLOW_SUBTASK_DELETE = 710;
    //FEED_PAGE_FLOW_SUBTASK_DELETE = 711;
    //FEED_FILE_FLOW_SUBTASK_DELETE = 712;
    //FEED_PAGE_POSITION_COMMENT_FLOW_SUBTASK_DELETE = 713;
    //FEED_TODO_FLOW_SUBTASK_DELETE = 714;
    //FEED_SESSION_FLOW_SUBTASK_DELETE = 715;
    //FEED_SIGN_FLOW_SUBTASK_DELETE = 716;
    //FEED_PURE_FLOW_SUBTASK_DELETE = 717;

    //FEED_COMMENT_FLOW_ATTACHMENT_CLIP = 720;
    //FEED_PAGE_FLOW_ATTACHMENT_CLIP = 721;
    //FEED_FILE_FLOW_ATTACHMENT_CLIP = 722;
    //FEED_PAGE_POSITION_COMMENT_FLOW_ATTACHMENT_CLIP = 723;
    //FEED_TODO_FLOW_ATTACHMENT_CLIP = 724;
    //FEED_SESSION_FLOW_ATTACHMENT_CLIP = 725;
    //FEED_SIGN_FLOW_ATTACHMENT_CLIP = 726;
    //FEED_PURE_FLOW_ATTACHMENT_CLIP = 727;

    //FEED_PURE_FLOW_CREATE = 740;
    //FEED_PURE_FLOW_UPDATE = 741;
    //FEED_PURE_FLOW_DELETE = 742;

    //FEED_FLOW_RENAME = 780;
    //FEED_FLOW_UPDATE_NOTE = 781;

    //FEED_FLOW_PART_2_MAX = 799;

    // folder feed
    FEED_FOLDER_CREATE = 700;
    FEED_FOLDER_RENAME = 701;
    FEED_FOLDER_RECYCLE = 702;
    FEED_FOLDER_DELETE = 703;

    // session feed
    FEED_SESSION_SCHEDULE = 800;
    FEED_SESSION_RESCHEDULE = 801;
    FEED_SESSION_START = 802;
    FEED_SESSION_END = 803;
    FEED_SESSION_RECORDING_READY = 804;
    FEED_SESSION_CANCEL = 805;
    FEED_SESSION_RENAME = 806;

    // web note feed
    FEED_NOTE_CREATE = 810;

    // pin feed
    FEED_PIN = 900;

    // call log feed
    FEED_CALL_LOG = 1000;
    // audio call log feed with call_status intead of status
    FEED_AUDIO_CALL_LOG = 1010;

    FEED_RELATIONSHIP_INVITE_PENDING = 1100;
    FEED_RELATIONSHIP_REMOVE_PENDING = 1110;
    FEED_RELATIONSHIP_JOIN_PENDING = 1120;
    FEED_REQUESTING_USER_CREATE = 1121;
    FEED_REQUESTING_USER_UPDATE = 1122;

    FEED_SIGNATURE_STATUS_UPDATE = 1200;
    FEED_SIGNATURE_DELETE = 1201;
    FEED_SIGNATURE_RENAME = 1202;
    FEED_SIGNATURE_CONVERTED = 1203;
    FEED_SIGNATURE_DUE_DATE_UPDATE = 1204;
    FEED_SIGNATURE_DUE_DATE_ARRIVE = 1205;
    FEED_SIGNATURE_FILE_REPLY = 1206;
    FEED_SIGNATURE_UPDATE = 1207;
    FEED_SIGNATURE_REOPEN = 1208;
    FEED_SIGNATURE_UPDATE_REOPEN = 1209;

    FEED_VIEWTOKEN_CREATE = 1210;
    FEED_VIEWTOKEN_DELETE = 1211;
    FEED_VIEWTOKEN_UPDATE = 1212;
    FEED_SIGNATURE_UPDATE_EDITING = 1213;
    FEED_SIGNATURE_UPDATE_READY = 1214;
    FEED_SIGNATURE_MARK_AS_COMPLETED = 1215;

    FEED_TRANSACTION_CREATE = 1220;
    FEED_TRANSACTION_DELETE = 1221;
    FEED_TRANSACTION_UPDATE = 1222;
    FEED_TRANSACTION_UPDATE_REOPEN = 1223;
    FEED_TRANSACTION_STEP_SUBMIT = 1225;  
    FEED_TRANSACTION_ATTACHMENT = 1226;  
    FEED_TRANSACTION_EXPIRATION_DATE_ARRIVE = 1227;  
    FEED_TRANSACTION_STEP_SUBMIT_BATCH = 1228;  
    FEED_TRANSACTION_STEP_REOPEN = 1229;  
    FEED_TRANSACTION_EXPIRATION_UPDATE = 1230;
    FEED_TRANSACTION_FILE_REPLY = 1231;
    FEED_TRANSACTION_REOPEN = 1232;
    FEED_TRANSACTION_FORM_CONVERTED = 1233;
    FEED_TRANSACTION_UPDATE_CUSTOM_RESULT = 1234;
    FEED_TRANSACTION_UPDATE_EDITING = 1235;
    FEED_TRANSACTION_UPDATE_READY = 1236;
    FEED_TRANSACTION_MARK_AS_COMPLETED = 1237;

    FEED_WORKFLOW_STEP_PREPARING = 1300;
    FEED_WORKFLOW_STEP_READY = 1301;
    FEED_WORKFLOW_STEP_SKIPPED = 1302;
    FEED_WORKFLOW_STEP_REOPEN = 1303;
    FEED_WORKFLOW_STARTED = 1310;
    FEED_WORKFLOW_RESTARTED = 1311;
    FEED_WORKFLOW_COMPLETED = 1320;
    FEED_WORKFLOW_CANCELED = 1321;
    FEED_WORKFLOW_CONTINUED = 1322;

    FEED_WORKFLOW_CREATE = 1330;
    FEED_WORKFLOW_UPDATE = 1331;
    FEED_WORKFLOW_DELETE = 1332;

    // general reassign todo/esign/transaction feed
    FEED_REASSIGN = 1400;


    // Webhook - Service Request
    FEED_SERVICE_REQUEST_CREATE = 1500;
    FEED_SERVICE_REQUEST_UPDATE = 1501;
    FEED_SERVICE_REQUEST_COMPLETE = 1502;
    FEED_SERVICE_REQUEST_REOPEN = 1503;

    FEED_ACD_REQUEST_END = 1550;
    FEED_ACD_REQUEST_TIMEOUT = 1551;
}

enum ObjectFeedStatus {
    FEED_STATUS_INVALID = 0;
    FEED_STATUS_PENDING = 10;
    FEED_STATUS_APPROVED = 20;
    FEED_STATUS_DENIED = 30;
}

enum ObjectFeedViaSource {
    FEED_VIA_UNKNOWN = 0;
    FEED_VIA_IOS = 10;
    FEED_VIA_ANDROID = 20;
    FEED_VIA_WEB = 30;
    FEED_VIA_DESKTOP = 40;
    FEED_VIA_WORKFLOW = 50;
}

message FeedReaction {
    optional BoardActor creator = 10;
    optional string text = 20;
    optional uint64 timestamp = 30;
}

message ObjectFeed {
    // who make the change/action
    optional User actor = 10 [lazy = true];
    optional BoardViewToken view_token = 11;
    optional ActionUserRoster roster = 12;

    // action
    optional ObjectFeedType type = 20;

    // board pages thumbnail and annotations to show in feed
    optional Board board = 40 [lazy = true];

    // the timestamp of the feed
    optional uint64 timestamp = 50;

    optional bool is_pinned = 60;

    optional User delegate = 200 [lazy = true];
    optional ObjectFeedStatus status = 210;
    // indicate source platform feed via
    optional ObjectFeedViaSource via = 220;
    repeated FeedReaction reactions = 300;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message ObjectActivity {
    // activity id comes from request sequence
	optional string id = 10;
    // request biz server name
	optional string server = 15;

    // who make the change/action
	optional User actor = 20;

    // change request to the object
    optional CacheObject request_object = 90;

    // changes to the object
    optional CacheObject object = 100;

    // the end revision of this activity
    optional uint64 revision = 110;

    // the timestamp of the activity
	optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

// S3 Storage Object (as part of CacheObject) with Hash_ObjectID_Rxxx as Key
message ObjectRecording {
    optional string id = 10;

    // for session recording only
    //optional string session_key = 15;
    //optional string session_topic = 16;

    // input only field, not stored
    //optional uint64 start_time = 20;
    //optional uint64 end_time = 30;

    // the snapshot of the object when the recording begins
    //optional CacheObject snapshot_begin = 100;

    // incremental object changes that can be merged into snapshot one by one
    repeated ObjectActivity activities = 110;

    // the snapshot of the object when the recording ends
    //optional CacheObject snapshot_end = 120;
    //optional ActionObject session_end = 130;

    // the snapshot of the object when the recording begins
    // for client use only
    //optional ActionObject session_begin = 140;

    // in CacheObject to store total recordings which was merged into it
    // one last recording need to be merged wil be _R{recording_count} as recording starts at R0
    optional uint64 recording_count = 150;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

//enum RecordingIndexType {
//    INDEX_SESSION_START = 10;
//    INDEX_SESSION_END = 11;

//    INDEX_USER_JOIN = 20;
//    INDEX_USER_LEAVE = 21;

//    INDEX_PAGE_SWITCH = 30;
//}

//message RecordingIndex {
//    optional RecordingIndexType type = 10;

//    optional User user = 20;
//    optional BoardPage page = 30;

    // timestamp of the first activity
//    optional uint64 start_time = 100;
    // timestamp of the next activity
//    optional uint64 end_time = 110;
//}

message AudioRecording {
    // board id
    optional string id = 10;
    // session key
    optional string session_key = 20;
    // audio file start time
    optional uint64 start_time = 30;
    // audio file end time
    optional uint64 end_time = 40;
    // audio file short name (without prefix)
    optional string name = 50;
    // audio file hash
    optional string hash = 60;
    // audio server address, server internal use only
    optional string server_addr = 70;
}

message VideoRecording {
    // board id
    optional string id = 10;
    // session key
    optional string session_key = 20;
    // audio file start time
    optional uint64 start_time = 30;
    // audio file end time
    optional uint64 end_time = 40;
    // audio file short name (without prefix)
    optional string name = 50;
    // audio file hash
    optional string hash = 60;
    // audio server address, server internal use only
    optional string server_addr = 70;
}

message DsRecording {
    // board id
    optional string id = 10;
    // session key
    optional string session_key = 20;
    // ds file start time
    optional uint64 start_time = 30;
    // ds file end time
    optional uint64 end_time = 40;
    // ds file short name (without prefix)
    optional string name = 50;
    // ds server address, server internal use only
    optional string server_addr = 60;
}

enum PublicViewTokenType {
    EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL = 1;
    EMAIL_TOKEN_PICTURE_PAGE_THUMBNAIL = 2;
    EMAIL_TOKEN_PICTURE_USER_PICTURE = 3;
    EMAIL_TOKEN_VIDEO_COMMENT_HASH = 4;
    EMAIL_TOKEN_VIDEO_COMMENT = 5;
    EMAIL_TOKEN_VERIFY_USER_EMAIL = 10;
    // to track email display
    EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL_INVIATION = 11;
    EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL_INVIATION_NON_REGISTERED = 12;
    // to track email display
    EMAIL_TOKEN_PICTURE_USER_PICTURE_INVIATION = 13;
    EMAIL_TOKEN_PICTURE_USER_PICTURE_INVIATION_NON_REGISTERED = 14;
    PUBLIC_VIEW_TOKEN_BOARD = 20;
    TRANSACTION_VIEW_TOKEN = 30;
    // to generate board resource url for office online
    BOARD_RESOURCE_VIEW_TOKEN = 40;

    // token for invited board user to access board as regular member
    BOARD_PUBLIC_ACCESS_TOKEN = 50;

    GROUP_INVITATION_TOKEN = 200;
    PARTNER_INVITATION_TOKEN = 260;
    BOARD_INVITATION_TOKEN = 210;
    CONTACT_INVITATION_TOKEN = 230;
    //PARTNER_SSO_VERIFICATION_TOKEN = 240;

    SESSION_ROSTER_TOKEN = 220;

    GROUP_USER_VIEW_TOKEN = 250;
    GROUP_USER_INVITATION_TOKEN = 270;
}

// for resource(picture, etc), type/resource_hash/token are filled
// for board/page public view, type/board_id[/page_seq] are filled, and board_token from board are used for verification
message PublicViewToken {
    optional uint64 version = 1;

    // checksum sha1 hashed token
    optional string token = 5;   // required for verification

    optional string user_id = 10;  // required for verify user email

    optional string board_id = 15;  // required for public board view
    optional uint64 board_token = 16;  // required for public board view
    optional string session_key = 17;   // required for session roster token
    optional uint64 roster_index = 18;  // required for session roster token
    optional uint64 roster_channel = 19;  // required for session roster token

    optional PublicViewTokenType type = 50; // required

    optional string resource_hash = 60; // required for resource view
    optional uint64 resource_seq = 61; // required for board resource token
    optional string resource_origin = 62;

    optional string actor_id = 20;  // not used
    optional uint64 feed_seq = 30;  // not used
    optional uint64 contact_seq = 40;  // used in contact invitation token, which has user_id and contact_seq filled

    optional uint64 transaction_seq = 70;  // required for transaction view token

    optional uint64 boarduser_seq = 80; // required for board access token 

    optional uint64 created_time = 100; // to make token different every time we create it

    // for group invitation token
    optional string user_email = 200;
    optional string user_phone_number = 201;
    optional string group_id = 210;
    optional uint64 groupuser_seq = 211; 
    optional uint64 invitation_token_seq = 212; 
    optional uint64 invitation_token_created_timestamp = 213; 
    optional string partner_id = 220;

    optional uint64 token_created_timestamp = 230;
    optional uint64 token_expire_timestamp = 240;
}

// =============================================
// Group statistics
// =============================================

enum GroupUsageItemType {
    GROUP_LICENSE_USAGE_TYPE = 10;
    GROUP_LICENSE_USAGE_TYPE_LOCAL = 20;
}

message GroupUsageItem {
    // group with plan_code and status filled
    optional Group group = 10;

    optional GroupUsageItemType item_type = 20;
	optional uint64 timestamp = 30;
	optional uint64 quantity_used = 40;
    // active user count since last calendar month
    optional uint64 quantity_active = 42;
    // client count without internal user assigned
    // or internal user without client assigned
    optional uint64 quantity_unassigned = 44;
	optional uint64 quantity_committed = 50;
    // max of used and committed
    optional uint64 quantity = 60;

	optional uint64 sequence = 100;
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

// =============================================
// Partner/Group statistics for superadmin/partner admin
// =============================================

// output only
message GroupUsageCount {
    optional uint64 partners = 10;
    optional uint64 groups = 20;
	optional uint64 users = 30;
}

// =============================================
// meet statistics, logged when meet ended
// =============================================

message SessionUsageItem {
    optional Board board = 10; // board tags
    optional ActionObject session = 20; // meet basic information and roster list
	optional uint64 total_minutes = 30; // total meeting minutes of the meet
    optional uint64 total_telephony_minutes = 40; // total telephony minutes of the meet

	optional uint64 sequence = 100;
	optional string client_uuid = 101;
	optional uint64 revision = 110;
    optional bool is_deleted = 120;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

// =============================================
// board statistics, logged when board update member happened
// =============================================

message BoardUsageItem {
    optional Board board = 10; // board id, name and owner

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message UsageStatistics {
    optional string id = 1;

    // daily org license usage, collected in daily job
    repeated GroupUsageItem group_usage_items = 30;

    // meet usage, collected when meet ended
    repeated SessionUsageItem session_items = 40;
    // output only
	optional uint64 total_minutes = 41; // total meeting minutes
    optional uint64 total_telephony_minutes = 42; // total telephony minutes

    // board usage
    repeated BoardUsageItem board_items = 50;

    //output only
    optional GroupUsageCount group_counts = 200;

	optional uint64 revision = 110;
	//optional uint64 previous_revision = 111;
    optional bool is_deleted = 120;
	optional uint64 local_revision = 130;

	optional uint64 created_time = 1000;
	optional uint64 updated_time = 1010;
}

// =============================================
// Activity log and statistics
// =============================================

message ActivityLog {
    optional AccessToken token = 10;
    optional ClientRequest request = 100;
    optional ClientResponse response = 200;
    // optional ApplePushNotification apn = 300;
    // updated user id for digest email
    optional User user = 400;

    // pre-calculated statistics from one biz of 5 minutes interval
    optional ActivityStatistics stats = 900;

    repeated TransactionLog logs = 1000;
    optional uint64 created_time = 2000;
}

// daily/weekly/month statistics saved in S3
message ActivityStatistics {
    // interval start
    optional uint64 start_time = 10;
    // interval end
    optional uint64 end_time = 20;

    // optional UserStatistics user_stats = 100;
    // optional AgentStatistics agent_stats = 200;
    // optional BoardStatistics board_stats = 300;
    // optional SessionStatistics session_stats = 400;
    // optional NoteStatistics note_stats = 500;
    // optional ServiceStatistics service_stats = 600;
    // optional TeamStatistics team_stats = 700;
    optional AppStatistics app_stats = 800;

    // users who sent requests
    // repeated ActiveStatistics active_stats = 1000;
    // users got updated passively, like user board updated, disabled, etc
    // repeated PassiveStatistics passive_stats = 1010;
    //repeated WebAppStatistics webapp_stats = 1100;
    // repeated GroupStatistics group_stats = 1200;

    // 5 min statistics collected and reported by every single server
    // repeated ServerStatistics server_stats = 1300;

    optional uint64 revision = 103;
    optional bool is_deleted = 104;
    optional string assignments = 105;
    optional uint64 local_revision = 106;

    optional uint64 created_time = 108;
    optional uint64 updated_time = 109;
}

message AppStatCategoryLeftSidePanel {
    optional uint64 click_clients = 1110;
    optional uint64 click_internal = 1111;
    optional uint64 click_files = 1112;
    optional uint64 click_files_plus = 1113;
    optional uint64 click_meetings = 1114;
    optional uint64 click_meetings_plus = 1115;
    optional uint64 click_esign = 1116;
    optional uint64 click_esign_plus = 1117;
    optional uint64 click_todos = 1118;
    optional uint64 click_todos_plus = 1119;
    optional uint64 click_contacts = 1120;
    optional uint64 click_broadcast = 1121;
    optional uint64 click_updates = 1122;
}

message AppStatCategoryMainNewPlusPanel {
    optional uint64 click_file = 1210;
    optional uint64 click_esign = 1211;
    optional uint64 click_todos = 1212;
    optional uint64 click_start_meeting = 1213;
    optional uint64 click_schedule_meeting = 1214;
}

message AppStatCategoryTopNavBar {
    optional uint64 click_manage = 1310;
    optional uint64 click_global_search = 1311;
    optional uint64 click_action_items = 1312;
    optional uint64 click_mentions = 1313;
}

message AppStatCategoryMentionList {
    optional uint64 click_dismiss_all = 1410;
    optional uint64 click_dismiss_item = 1411;
    optional uint64 click_mention_item = 1412;
}

message AppStatCategoryActionItems {
    optional uint64 click_action_item = 1510;
}

message AppStatCategoryTimeline {
    optional uint64 click_action_items = 1610;
    optional uint64 click_mentions = 1611;
    optional uint64 click_global_search = 1612;
    optional uint64 click_filter_conversation = 1613;
}

message AppStatCategoryBinderView {
    optional uint64 click_meeting = 1710;
    optional uint64 click_search = 1711;
    optional uint64 click_files_tab = 1712;
    optional uint64 click_todo_tab = 1713;
}

message AppStatCategoryOverview {
    optional uint64 click_overview_tab = 1810;
    optional uint64 click_files_view_all = 1811;
    optional uint64 click_file_item = 1812;
    optional uint64 click_esign_view_all = 1813;
    optional uint64 click_sign_now = 1814;
    optional uint64 click_todos_view_all = 1815;
    optional uint64 click_todo_item = 1816;
}

message AppStatCategoryNewFlowWorkspace {
    optional uint64 total_launched_by_webhook = 3310;
    optional uint64 total_launched_by_webhook_with_newly_invited_client = 3311;
    optional uint64 total_launched_from_template = 3312;
    optional uint64 total_launched_from_template_with_newly_invited_client = 3313;
    optional uint64 total_launched_from_plus_new = 3314;
    optional uint64 total_launched_from_plus_new_with_newly_invited_client = 3315;
    optional uint64 total_launched_from_scheduled_flow = 3316;
    optional uint64 total_launched_from_scheduled_flow_with_newly_invited_client = 3317;
    optional uint64 total_launched_by_zapier = 3318;
    optional uint64 total_launched_by_zapier_with_newly_invited_client = 3319;
    optional uint64 total_launched_by_rest_api = 3320;
    optional uint64 total_launched_by_rest_api_with_newly_invited_client = 3321;
    optional uint64 total_launched_from_sr = 3322;
    optional uint64 total_launched_from_main_flow = 3323;
}

message AppStatistics {
    optional AppStatCategoryLeftSidePanel web_left_side_panel = 1103;
    optional AppStatCategoryMainNewPlusPanel web_main_new_plus_panel = 1203;
    optional AppStatCategoryTopNavBar web_top_nav_bar = 1303;

    optional AppStatCategoryMentionList ios_mention_list = 1401;
    optional AppStatCategoryMentionList android_mention_list = 1402;
    optional AppStatCategoryMentionList web_mention_list = 1403;
    optional AppStatCategoryActionItems ios_action_items = 1501;
    optional AppStatCategoryActionItems android_action_items = 1502;
    optional AppStatCategoryActionItems web_action_items = 1503;

    optional AppStatCategoryTimeline ios_timeline = 1601;
    optional AppStatCategoryTimeline android_timeline = 1602;
    optional AppStatCategoryBinderView ios_binder_view = 1701;
    optional AppStatCategoryBinderView android_binder_view = 1702;
    optional AppStatCategoryOverview ios_overview = 1801;
    optional AppStatCategoryOverview android_overview = 1802;

    optional AppStatCategoryNewFlowWorkspace ios_new_flow_workspace = 3301;
    optional AppStatCategoryNewFlowWorkspace android_new_flow_workspace = 3302;
    optional AppStatCategoryNewFlowWorkspace web_new_flow_workspace = 3303;
}

// =============================================
// Resource manager data structure, Client internal use only
// =============================================
message ResourceItem {
    optional string hash = 10;
    optional uint64 accessed_time = 20;
    optional uint64 created_time = 30;
    optional uint64 content_length = 40;
}

message Resources {
    repeated ResourceItem resources = 10;
}

// =============================================
// twilio request param
// =============================================

message TwilioRequestParam {
    optional string call_sid= 10;
    optional string account_sid= 20;
    optional string from = 30;
    optional string to = 40;
    optional string p_asserted_identity = 45;
    optional string call_status = 50;
    optional string api_version = 60;
}

// =============================================
// system configuration
// configurations for the server to function
// like email config, cache plans, admin users, etc
// =============================================

message SystemEmailConfig {
    // smtp user name
    optional string username = 10;
    // smtp password
    optional string password = 20;
    // smtp server address
    optional string server_address = 30;
    // smtp server port
    optional uint64 server_port = 40;
    // sender/from address in outgoing emails
    optional string from_address = 50;

    // default incoming email domain for binder email
    optional string default_incoming_domain = 100;
    // extra incoming email domain allowed
    repeated string incoming_domains = 110;

    // emails to receive system alerts
    repeated string alert_emails = 200;

    // emails to receive feedbacks from USER_REQUEST_FEEDBACK
    optional string feedback_email = 210;

    optional uint64 revision = 1000;
    optional bool is_deleted = 1010;
    optional uint64 created_time = 1020;
    optional uint64 updated_time = 1030;
}

message SystemAdminUser {
    // only fill user.email and user.role
    optional User user = 10;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message SystemUdpMapping {
    // mms server host name like mms001, mms002 etc
    optional string name = 10;
    // mms external/client facing domain or ip
    optional string domain = 20;
    // mms external/client facing udp port
    // port forwarding needs to be setup separately
    optional uint64 port = 30;
    // mms external/client facing tcp port for webrtc
    // port forwarding needs to be setup separately
    optional uint64 tcp_port = 40;
    // mms external facing websocket url
    // url translation needs to be setup separately
    optional string url = 50;
    // ds external facing websocket url
    // url translation needs to be setup separately
    optional string ds_url = 60;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

// User agent to webapp client_id mapping
// for apns cert/key and gcm key lookup
message SystemAppMapping {
    // app name like Moxtra, Moxtra Beta, etc...
    optional string name = 10;
    // client id for the app
    optional string client_id = 20;

    optional uint64 sequence = 100;
    optional string client_uuid = 101;
    optional uint64 revision = 110;
    optional bool is_deleted = 120;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}

message SystemDocumentConverter {
    // office online server
    optional string wopi_url = 10;
    optional string word_viewer_url = 20;
    optional string ppt_viewer_url = 30;
    optional string ppt_param_pid = 40;
    // mac farm
    optional string mac_viewer_url = 100;
    // resource domain to download resource for conversion
    optional string resource_domain = 200;
}

message SystemFeatures {
    // disable normal user from register and/or login
    // SSO and SystemAdminUser will not be impacted
    optional bool disable_register_login = 10;

    optional bool disable_meet_recording_feed = 100;
}

message SystemPasswordRule {
    optional uint64 character = 10;
    optional uint64 lowercase = 20;
    optional uint64 uppercase = 30;
    optional uint64 digit = 40;
    optional uint64 special = 50;

    optional string special_characters = 200;
}

//message SystemAppConfig {
//  optional uint64 ios_app_version_recommended = 100;
//  optional uint64 android_app_version_recommended = 200;
//}

message SystemConfig {
    // WRITE role need to update system config

    // the domain name of the system, like www.example.com
    // for deployment with port other than 443, use www.example.com:8443
    optional string domain = 10;

    // system timezone, for use as user's default timezone
    optional string timezone = 11 [default = "America/Los_Angeles"];

    // outgoing and incoming email configuration
    optional SystemEmailConfig email_config = 15;

    //optional SystemAppConfig app_config = 16;

    // default plan cap is used as the base plan code
    optional UserCap default_plan_cap = 20;
    // plan code definition
    // values not defined in CachePlan.plan_cap are inherited from default_plan_cap
    // and plan code not defined are using default_plan_cap
    repeated CachePlan plans = 21;

    // app name to client id mapping is for lookup push notification cert and gcm key
    repeated SystemAppMapping app_mappings = 24;

    // udp mapping is for mms server and can largely improve VoIP performance
    repeated SystemUdpMapping udp_mappings = 25;

    // document converter config
    optional SystemDocumentConverter document_converter = 30;

    // system level admin user, fill in users.user.email and user.user.role
    repeated SystemAdminUser users = 40;

    // system level features tuning
    optional SystemFeatures features = 50;

    // system password rule
    optional SystemPasswordRule password_rule = 60;

    // system validation code
    optional string validation_code = 200;
    optional bool is_validated = 210 [default = true];
    optional uint64 validation_expires_after = 211;

    optional uint64 revision = 110;
    optional bool is_deleted = 120;
    optional uint64 local_revision = 130;
    optional string assignments = 131;

    optional uint64 created_time = 1000;
    optional uint64 updated_time = 1010;
}