export var RequestingUserStatus;
(function (RequestingUserStatus) {
    RequestingUserStatus["REQUESTING_USER_STATUS_INVALID"] = "REQUESTING_USER_STATUS_INVALID";
    RequestingUserStatus["REQUESTING_USER_STATUS_PENDING"] = "REQUESTING_USER_STATUS_PENDING";
    RequestingUserStatus["REQUESTING_USER_STATUS_APPROVED"] = "REQUESTING_USER_STATUS_APPROVED";
    RequestingUserStatus["REQUESTING_USER_STATUS_DENIED"] = "REQUESTING_USER_STATUS_DENIED";
    RequestingUserStatus["REQUESTING_USER_STATUS_JOINED"] = "REQUESTING_USER_STATUS_JOINED";
})(RequestingUserStatus || (RequestingUserStatus = {}));
