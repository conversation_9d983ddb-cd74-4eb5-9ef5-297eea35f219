export var BoardMemberPrivileges;
(function (BoardMemberPrivileges) {
    BoardMemberPrivileges["BOARD_DELETE_OTHERS_MSG"] = "BOARD_DELETE_OTHERS_MSG";
    BoardMemberPrivileges["BOARD_DELETE_OTHERS_COMMENT"] = "BOARD_DELETE_OTHERS_COMMENT";
    BoardMemberPrivileges["BOARD_DELETE_OTHERS_FILE"] = "BOARD_DELETE_OTHERS_FILE";
    BoardMemberPrivileges["BOARD_DELETE_OTHERS_ANNOTATION"] = "BOARD_DELETE_OTHERS_ANNOTATION";
    BoardMemberPrivileges["BOARD_HISTORY_FROM_JOIN"] = "BOARD_HISTORY_FROM_JOIN";
    BoardMemberPrivileges["BOARD_INVITE_BOARD_MEMBER"] = "BOARD_INVITE_BOARD_MEMBER";
    BoardMemberPrivileges["BOARD_SHARE_CONTENT"] = "BOARD_SHARE_CONTENT";
    BoardMemberPrivileges["BOARD_SEND_MSG"] = "BOARD_SEND_MSG";
    BoardMemberPrivileges["BOARD_ADD_COMMENT"] = "BOARD_ADD_COMMENT";
    BoardMemberPrivileges["BOARD_UPLOAD_FILE"] = "BOARD_UPLOAD_FILE";
    BoardMemberPrivileges["BOARD_SIGN_FILE"] = "BOARD_SIGN_FILE";
    BoardMemberPrivileges["BOARD_ADD_ANNOTATION"] = "BOARD_ADD_ANNOTATION";
    BoardMemberPrivileges["BOARD_COPY_TO_OTHER_BINDER"] = "BOARD_COPY_TO_OTHER_BINDER";
    BoardMemberPrivileges["BOARD_SAVE_TO_ALBUM"] = "BOARD_SAVE_TO_ALBUM";
})(BoardMemberPrivileges || (BoardMemberPrivileges = {}));
