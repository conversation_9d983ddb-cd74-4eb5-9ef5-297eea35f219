export var SessionRecordingStatus;
(function (SessionRecordingStatus) {
    SessionRecordingStatus["RECORDING_STOPPED"] = "RECORDING_STOPPED";
    SessionRecordingStatus["RECORDING_STARTED"] = "RECORDING_STARTED";
    SessionRecordingStatus["RECORDING_PAUSED"] = "RECORDING_PAUSED";
    SessionRecordingStatus["RECORDING_RESUMED"] = "RECORDING_RESUMED";
    SessionRecordingStatus["RECORDING_SAVED"] = "RECORDING_SAVED";
    SessionRecordingStatus["RECORDING_CANCELLED"] = "RECORDING_CANCELLED";
})(SessionRecordingStatus || (SessionRecordingStatus = {}));
