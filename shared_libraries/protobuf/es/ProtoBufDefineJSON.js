export const ProtoBufDefineJSON = { "nested": { "AccessToken": { "edition": "proto2", "fields": { "board_id": { "type": "string", "id": 2 }, "device_id": { "type": "string", "id": 3 }, "client_id": { "type": "string", "id": 4 }, "agent_id": { "type": "string", "id": 5 }, "access_token": { "type": "string", "id": 6 }, "access_token_expire_timestamp": { "type": "uint64", "id": 7 }, "access_token_created_timestamp": { "type": "uint64", "id": 8 }, "uid": { "type": "string", "id": 10 }, "token": { "type": "string", "id": 11 }, "token_from_cookie": { "type": "bool", "id": 12 }, "session_id": { "type": "string", "id": 15 }, "session_id_from_cookie": { "type": "bool", "id": 16 }, "connection_id": { "type": "string", "id": 17 }, "email": { "type": "string", "id": 18 }, "client_ip": { "type": "string", "id": 20 }, "server_ip": { "type": "string", "id": 21 }, "awselb": { "type": "string", "id": 22 }, "request_url": { "type": "string", "id": 23 }, "host": { "type": "string", "id": 24 }, "referer": { "type": "string", "id": 25 }, "client_private_ip": { "type": "string", "id": 26 }, "origin": { "type": "string", "id": 27 }, "connection_number": { "type": "string", "id": 29 }, "client_ua": { "type": "string", "id": 30 }, "client_version": { "type": "uint64", "id": 31 }, "client_accept_language": { "type": "string", "id": 32 }, "gid": { "type": "string", "id": 40 }, "name": { "type": "string", "id": 50 }, "x_forwarded_uri": { "type": "string", "id": 60 }, "token_verified": { "type": "bool", "id": 100 }, "agent_token_verified": { "type": "bool", "id": 105 }, "server_token_verified": { "type": "bool", "id": 110 }, "websocket": { "type": "bool", "id": 120 }, "board_access_token_verified": { "type": "bool", "id": 130 }, "scope": { "type": "uint64", "id": 400 }, "created_time": { "type": "uint64", "id": 1000 } } }, "TransactionLog": { "edition": "proto2", "fields": { "timestamp": { "type": "uint64", "id": 10 }, "sequence": { "type": "string", "id": 20 }, "message": { "type": "string", "id": 30 } } }, "ClientRequestType": { "edition": "proto2", "options": { "allow_alias": true }, "values": { "INVALID_REQUEST": 0, "CLIENT_REQUEST_CONNECT": 5, "JOB_REQUEST_CONNECT": 6, "CLIENT_REQUEST_PING": 7, "USER_REQUEST_SSO": 8, "USER_REQUEST_READ_SSO_OPTIONS": 9, "USER_REQUEST_REGISTER": 10, "USER_REQUEST_REGISTER_AGENT": 11, "USER_REQUEST_UNREGISTER_AGENT": 12, "USER_REQUEST_RESEND_VERIFICATION_EMAIL": 15, "USER_REQUEST_VERIFY_EMAIL_TOKEN": 16, "USER_REQUEST_ENTER_FOREGROUND": 17, "USER_REQUEST_RESEND_VERIFICATION_CODE": 18, "USER_REQUEST_RESEND_VERIFICATION_CODE_EMAIL": 18, "USER_REQUEST_VERIFY_CODE": 19, "USER_REQUEST_VERIFY_EMAIL_CODE": 19, "USER_REQUEST_READ": 20, "USER_REQUEST_READ_CAP": 21, "USER_REQUEST_UNREAD_FEEDS_COUNT": 22, "USER_REQUEST_SUBSCRIBE": 25, "USER_REQUEST_READ_FEEDS": 26, "USER_REQUEST_READ_NOTES": 27, "USER_REQUEST_UPDATE": 30, "USER_REQUEST_UPDATE_PICTURES": 31, "USER_REQUEST_UPDATE_NAME": 32, "USER_REQUEST_UPDATE_PHONE_NUMBER": 33, "USER_REQUEST_UPDATE_EMAIL": 34, "USER_REQUEST_UPDATE_USER_BOARD": 35, "USER_REQUEST_UPDATE_USER_BOARD_ENTER": 36, "USER_REQUEST_UPDATE_USER_GROUP": 37, "USER_REQUEST_UPDATE_AGENT": 39, "USER_REQUEST_LOGIN": 40, "USER_REQUEST_RESOURCE_TOKEN": 43, "USER_REQUEST_ACCESS_TOKEN": 44, "USER_REQUEST_VERIFY_TOKEN": 45, "USER_REQUEST_DUPLICATE_TOKEN": 46, "USER_REQUEST_VERIFY_PASSWORD": 47, "USER_REQUEST_REFRESH_TOKEN": 48, "USER_REQUEST_LOGOUT": 50, "USER_REQUEST_LOGOUT_ALL_DEVICES": 51, "USER_REQUEST_UPLOAD_RESOURCE": 60, "USER_REQUEST_UPLOAD_PROFILE_PICTURES": 61, "USER_REQUEST_DOWNLOAD_RESOURCE": 70, "USER_REQUEST_RESET_PASSWORD": 80, "USER_REQUEST_CHANGE_PASSWORD": 81, "USER_REQUEST_CATEGORY_CREATE": 90, "USER_REQUEST_CATEGORY_RENAME": 91, "USER_REQUEST_CATEGORY_DELETE": 92, "USER_REQUEST_CATEGORY_ASSIGN": 93, "USER_REQUEST_READ_SESSIONS": 100, "USER_REQUEST_READ_BOARDS": 101, "USER_REQUEST_READ_RELATIONS": 102, "USER_REQUEST_READ_AUTO_ARCHIVED_BOARDS": 103, "USER_REQUEST_READ_USER_BOARDS": 104, "USER_REQUEST_EMAIL_LOOKUP": 110, "USER_REQUEST_PHONE_NUMBER_LOOKUP": 111, "USER_REQUEST_BOARD_LOOKUP": 120, "USER_REQUEST_RELATION_LOOKUP": 121, "USER_REQUEST_REMOVE_FAVORITE": 130, "USER_REQUEST_REMOVE_MENTIONME": 135, "USER_REQUEST_REMOVE_MENTIONME_BEFORE": 136, "USER_REQUEST_REMOVE_NOTIFICATION": 137, "USER_REQUEST_REMOVE_NOTIFICATION_BEFORE": 138, "USER_REQUEST_UPDATE_ORDER_NUMBER": 140, "USER_REQUEST_UPDATE_ACTION_ITEM": 141, "USER_REQUEST_REGISTER_LOCAL_USER": 150, "USER_REQUEST_LOGIN_LOCAL_USER": 151, "USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN": 152, "USER_REQUEST_VERIFY_LOCAL_EMAIL_CODE": 153, "USER_REQUEST_CREATE_RELATION_VIA_QR_TOKEN": 155, "USER_REQUEST_PREVIEW_EMAIL_TOKEN": 160, "USER_REQUEST_REGISTER_LOCAL_USER_BY_APPLE_ID": 170, "USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_APPLE_ID": 171, "USER_REQUEST_LOGIN_LOCAL_USER_BY_APPLE_ID": 172, "USER_REQUEST_REGISTER_LOCAL_USER_BY_GOOGLE_ID": 173, "USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_GOOGLE_ID": 174, "USER_REQUEST_LOGIN_LOCAL_USER_BY_GOOGLE_ID": 175, "USER_REQUEST_REGISTER_LOCAL_USER_BY_VERIFICATION_CODE": 176, "USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_VERIFICATION_CODE": 177, "USER_REQUEST_LOGIN_LOCAL_USER_BY_VERIFICATION_CODE": 178, "USER_REQUEST_LOGIN_LOCAL_USER_BY_GROUP_INVITATION_TOKEN": 179, "USER_REQUEST_LOGIN_LOCAL_USER_BY_SALESFORCE_ID": 180, "USER_REQUEST_UPDATE_DEVICE_TOKEN": 200, "USER_REQUEST_SEARCH": 210, "USER_REQUEST_SEARCH_CONTENT_LIBRARY_BOARD": 220, "USER_REQUEST_SEARCH_FLOW_TEMPLATE_LIBRARY_BOARD": 230, "USER_REQUEST_KEEP_ALIVE": 240, "USER_REQUEST_EMAIL_AUTH": 300, "USER_REQUEST_EMAIL_DELIVERY": 310, "USER_REQUEST_CONTACT_INVITE": 400, "USER_REQUEST_CONTACT_ACCEPT": 410, "USER_REQUEST_CONTACT_DENY": 420, "USER_REQUEST_CONTACT_CANCEL": 430, "USER_REQUEST_CONTACT_VIEW_INVITATION": 440, "USER_REQUEST_CREATE_CONTACT": 450, "USER_REQUEST_UPDATE_CONTACT": 451, "USER_REQUEST_DELETE_CONTACT": 452, "USER_REQUEST_DELETE_RESOURCE": 475, "USER_REQUEST_FEEDBACK": 500, "USER_REQUEST_FEEDBACK_AND_ATTACHMENT": 501, "USER_REQUEST_SYSTEM_FEEDBACK": 520, "USER_REQUEST_SSO_REGISTERED_USER": 600, "USER_REQUEST_SSO_GROUP_UNIQUE_ID": 610, "USER_REQUEST_SSO_EXTERNAL_MOXTRA": 620, "USER_REQUEST_SSO_REDIRECT": 630, "USER_REQUEST_UPDATE_SIP_REGISTRATION_STATUS": 659, "USER_REQUEST_CREATE_CALL_LOG": 660, "USER_REQUEST_UPDATE_CALL_LOG": 661, "USER_REQUEST_DELETE_CALL_LOG": 662, "USER_REQUEST_READ_PASSWORD_RULE": 710, "USER_REQUEST_RESEND_DELETION_EMAIL": 800, "USER_REQUEST_VERIFY_DELETION_TOKEN": 801, "USER_REQUEST_DELETE": 802, "USER_REQUEST_DELETE_LOCAL_USER": 803, "USER_REQUEST_FOLLOW_GROUP_BOARD": 810, "USER_REQUEST_UNFOLLOW_GROUP_BOARD": 811, "USER_REQUEST_UPDATE_GROUP_BOARD": 812, "USER_REQUEST_UPDATE_OUT_OF_OFFICE": 813, "USER_REQUEST_POST_ACTIVITY": 820, "USER_REQUEST_READ_ACTIVITY": 821, "USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_EMAIL": 850, "USER_REQUEST_QR_TOKEN": 851, "USER_REQUEST_VIEW_QR_TOKEN": 852, "USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_SMS": 860, "USER_REQUEST_VERIFY_LOCAL_SMS_CODE": 865, "USER_REQUEST_VERIFY_LOCAL_APPLE_JWT": 866, "USER_REQUEST_VERIFY_LOCAL_GOOGLE_JWT": 867, "USER_REQUEST_RESEND_APP_DOWNLOAD_LINK_SMS": 870, "USER_REQUEST_GLOBAL_LOOKUP_DOMAIN_BY_EMAIL": 880, "USER_REQUEST_GLOBAL_LOOKUP_DOMAIN_BY_PHONE_NUMBER": 881, "USER_REQUEST_GLOBAL_RESEND_SMS_CODE": 882, "USER_REQUEST_GLOBAL_VERIFY_SMS_CODE": 883, "USER_REQUEST_GLOBAL_RESEND_EMAIL_CODE": 884, "USER_REQUEST_GLOBAL_VERIFY_EMAIL_CODE": 885, "USER_REQUEST_PUSH_NOTIFICATION": 900, "USER_REQUEST_SMS": 905, "USER_REQUEST_CREATE_BROADCAST": 910, "USER_REQUEST_UPDATE_BROADCAST": 911, "USER_REQUEST_DELETE_BROADCAST": 912, "USER_REQUEST_GET_HTML": 920, "USER_REQUEST_SHORT_URL": 929, "USER_REQUEST_LOOKUP_SHORT_URL": 930, "USER_REQUEST_VERIFY_GOOGLE_PLAY_INTEGRITY_TOKEN": 931, "USER_REQUEST_USER_BOARD_ARCHIVE": 932, "USER_REQUEST_MAX": 999, "BOARD_REQUEST_CREATE": 2000, "BOARD_REQUEST_DUPLICATE": 2001, "BOARD_REQUEST_READ": 2010, "BOARD_REQUEST_VIEW": 2011, "BOARD_REQUEST_VIEW_AS_OGO": 2012, "BOARD_REQUEST_READ_FEEDS": 2013, "BOARD_REQUEST_SUBSCRIBE": 2015, "BOARD_REQUEST_UNSUBSCRIBE": 2016, "BOARD_REQUEST_SUBSCRIBE_MULTIPLE": 2017, "BOARD_REQUEST_UPDATE": 2020, "BOARD_REQUEST_COPY_PAGES": 2021, "BOARD_REQUEST_COPY_RESOURCES": 2022, "BOARD_REQUEST_COPY_TODOS": 2023, "BOARD_REQUEST_CREATE_COMMENT": 2225, "BOARD_REQUEST_UPLOAD_COMMENT": 2226, "BOARD_REQUEST_UPDATE_COMMENT": 2227, "BOARD_REQUEST_DELETE_COMMENT": 2228, "BOARD_REQUEST_TYPE_INDICATION": 2229, "BOARD_REQUEST_DELETE": 2030, "BOARD_REQUEST_UPDATE_COMMENT_URL_PREVIEW": 2031, "BOARD_REQUEST_DELETE_COMMENT_URL_PREVIEW": 2032, "BOARD_REQUEST_INCREASE_USED_COUNT": 2033, "BOARD_REQUEST_UPDATE_BOARD_USER": 2034, "BOARD_REQUEST_UPLOAD_RESOURCE": 2040, "BOARD_REQUEST_UPLOAD_AUDIO": 2041, "BOARD_REQUEST_DOWNLOAD_RESOURCE": 2050, "BOARD_REQUEST_DOWNLOAD_BOARD": 2052, "BOARD_REQUEST_UPLOAD_RESOURCE_URL": 2053, "BOARD_REQUEST_DOWNLOAD_FOLDER": 2054, "BOARD_REQUEST_DOWNLOAD_ZIP": 2055, "BOARD_REQUEST_INVITE": 2060, "BOARD_REQUEST_JOIN": 2061, "BOARD_REQUEST_LEAVE": 2062, "BOARD_REQUEST_APPROVE": 2063, "BOARD_REQUEST_DENY": 2064, "BOARD_REQUEST_EXPEL": 2065, "BOARD_REQUEST_SET_ACCESS_TYPE": 2066, "BOARD_REQUEST_VIEW_INVITATION": 2067, "BOARD_REQUEST_INVITE_OUT_OF_OFFICE_BACKUP_USER": 2068, "BOARD_REQUEST_ACTION_TRANSFER": 2069, "BOARD_REQUEST_GET_RECORDINGS": 2070, "BOARD_REQUEST_JOIN_BY_VIEW_TOKEN": 2074, "BOARD_REQUEST_UPDATE_REQUESTING_USER": 2075, "BOARD_REQUEST_JOIN_BY_VIEW_TOKEN_APPLE_ID": 2076, "BOARD_REQUEST_JOIN_BY_VIEW_TOKEN_GOOGLE_ID": 2077, "BOARD_REQUEST_CREATE_VIEW_TOKEN": 2080, "BOARD_REQUEST_UPDATE_VIEW_TOKEN": 2083, "BOARD_REQUEST_REMOVE_VIEW_TOKEN": 2081, "BOARD_REQUEST_EMAIL_VIEW_TOKEN": 2082, "BOARD_REQUEST_READ_VIEW_TOKEN": 2084, "BOARD_REQUEST_READ_VIEW_TOKENS": 2085, "BOARD_REQUEST_SEARCH_BOARD": 2090, "BOARD_REQUEST_SEARCH_GROUP_BOARD": 2091, "BOARD_REQUEST_SEARCH_GROUP": 2092, "SESSION_REQUEST_START": 2100, "SESSION_REQUEST_RESTART": 2101, "SESSION_REQUEST_END": 2110, "SESSION_REQUEST_JOIN": 2120, "SESSION_REQUEST_SUBSCRIBE": 2125, "SESSION_REQUEST_LEAVE": 2130, "SESSION_REQUEST_INVITE": 2140, "SESSION_REQUEST_KEEP_ALIVE": 2150, "SESSION_REQUEST_EVENT_LOG": 2152, "SESSION_REQUEST_READ_EVENT_LOG": 2153, "SESSION_REQUEST_WEBRTC_OFFER": 2160, "SESSION_REQUEST_CHANGE_PRESENTER": 2161, "SESSION_REQUEST_START_DS": 2162, "SESSION_REQUEST_STOP_DS": 2163, "SESSION_REQUEST_PUBLISH_DS_STATE": 2164, "SESSION_REQUEST_PUBLISH_AUDIO_STATE": 2165, "SESSION_REQUEST_PUBLISH_VIDEO_STATE": 2166, "SESSION_REQUEST_READ": 2168, "SESSION_REQUEST_READ_ROSTER": 2170, "SESSION_REQUEST_UPDATE_AUDIO_STATUS": 2172, "SESSION_REQUEST_UPDATE_VIDEO_STATUS": 2173, "SESSION_REQUEST_START_SESSION": 2174, "SESSION_REQUEST_RECLAIM_HOST": 2175, "SESSION_REQUEST_SET_PRESENTER": 2180, "SESSION_REQUEST_SET_HOST": 2181, "SESSION_REQUEST_MUTE": 2182, "SESSION_REQUEST_UNMUTE": 2183, "SESSION_REQUEST_MUTE_ALL": 2184, "SESSION_REQUEST_SWITCH_PAGE": 2185, "SESSION_REQUEST_START_DESKTOPSHARE": 2186, "SESSION_REQUEST_LEAVE_TELEPHONY": 2187, "SESSION_REQUEST_SCHEDULE": 2190, "SESSION_REQUEST_DOWNLOAD_CALENDAR": 2191, "SESSION_REQUEST_CREATE_PERSONAL_ROOM": 2192, "SESSION_REQUEST_REMOVE_ROSTER": 2193, "SESSION_REQUEST_LOCK": 2194, "SESSION_REQUEST_UNLOCK": 2195, "SESSION_REQUEST_SCHEDULE_TO_BOARD_USER": 2198, "BOARD_REQUEST_UPDATE_BOARD": 2200, "BOARD_REQUEST_UPLOAD_BOARD_RESOURCE": 2201, "BOARD_REQUEST_CREATE_PAGE": 2210, "BOARD_REQUEST_UPLOAD_PAGE": 2211, "BOARD_REQUEST_UPDATE_PAGE": 2212, "BOARD_REQUEST_DELETE_PAGE": 2213, "BOARD_REQUEST_DELETE_RESOURCE": 2214, "BOARD_REQUEST_SET_EDITOR": 2215, "BOARD_REQUEST_REMOVE_EDITOR": 2216, "BOARD_REQUEST_SET_EDITOR_TYPE": 2217, "BOARD_REQUEST_CREATE_PAGE_ELEMENT": 2220, "BOARD_REQUEST_UPLOAD_PAGE_ELEMENT": 2221, "BOARD_REQUEST_UPDATE_PAGE_ELEMENT": 2222, "BOARD_REQUEST_DELETE_PAGE_ELEMENT": 2223, "BOARD_REQUEST_CREATE_PAGE_COMMENT": 2230, "BOARD_REQUEST_UPLOAD_PAGE_COMMENT": 2231, "BOARD_REQUEST_UPDATE_PAGE_COMMENT": 2232, "BOARD_REQUEST_DELETE_PAGE_COMMENT": 2233, "BOARD_REQUEST_CREATE_PAGE_POSITION_COMMENT": 2234, "BOARD_REQUEST_UPLOAD_PAGE_POSITION_COMMENT": 2235, "BOARD_REQUEST_UPDATE_PAGE_POSITION_COMMENT": 2236, "BOARD_REQUEST_DELETE_PAGE_POSITION_COMMENT": 2237, "BOARD_REQUEST_CREATE_PAGE_GROUP": 2240, "BOARD_REQUEST_UPDATE_PAGE_GROUP": 2242, "BOARD_REQUEST_DELETE_PAGE_GROUP": 2243, "BOARD_REQUEST_COPY_PAGE_GROUP": 2244, "BOARD_REQUEST_MOVE_PAGE_GROUP": 2245, "BOARD_REQUEST_COPY_SIGNATURE": 2246, "BOARD_REQUEST_CREATE_PAGE_TAG": 2250, "BOARD_REQUEST_UPDATE_PAGE_TAG": 2252, "BOARD_REQUEST_DELETE_PAGE_TAG": 2253, "BOARD_REQUEST_DOWNLOAD_NOTE": 2260, "BOARD_REQUEST_CREATE_TODO": 2270, "BOARD_REQUEST_UPLOAD_TODO": 2271, "BOARD_REQUEST_UPDATE_TODO": 2272, "BOARD_REQUEST_DELETE_TODO": 2273, "BOARD_REQUEST_SET_TODO_ASSIGNEE": 2274, "BOARD_REQUEST_SET_TODO_DUE_DATE": 2275, "BOARD_REQUEST_SET_TODO_COMPLETED": 2276, "BOARD_REQUEST_UPDATE_TODO_ATTACHMENT": 2277, "BOARD_REQUEST_UPLOAD_TODO_COMMENT": 2279, "BOARD_REQUEST_CREATE_TODO_COMMENT": 2280, "BOARD_REQUEST_UPDATE_TODO_COMMENT": 2281, "BOARD_REQUEST_DELETE_TODO_COMMENT": 2282, "BOARD_REQUEST_CREATE_TODO_REMINDER": 2283, "BOARD_REQUEST_UPDATE_TODO_REMINDER": 2284, "BOARD_REQUEST_DELETE_TODO_REMINDER": 2285, "BOARD_REQUEST_DELETE_TODO_FILE": 2290, "BOARD_REQUEST_READ_FLAT_FEEDS": 2300, "BOARD_REQUEST_READ_THREAD": 2301, "BOARD_REQUEST_READ_ONGOING_SIGNATURES": 2302, "BOARD_REQUEST_READ_ONGOING_TRANSACTIONS": 2303, "BOARD_REQUEST_READ_ONGOING_DELEGATE_FEEDS": 2304, "BOARD_REQUEST_READ_COVER": 2305, "BOARD_REQUEST_LIST_FOLDER": 2306, "BOARD_REQUEST_READ_FILE": 2307, "BOARD_REQUEST_LIST_SIGNATURES": 2308, "BOARD_REQUEST_LIST_TODOS": 2309, "BOARD_REQUEST_READ_SIGNATURE": 2310, "BOARD_REQUEST_CREATE_REMINDER": 2320, "BOARD_REQUEST_UPDATE_REMINDER": 2321, "BOARD_REQUEST_DELETE_REMINDER": 2322, "BOARD_REQUEST_READ_AUDIT_OBJECT": 2400, "BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_RESOURCE": 2401, "BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_BOARD": 2402, "BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_ZIP": 2403, "BOARD_REQUEST_RESEND_INVITATION_EMAIL": 2410, "BOARD_REQUEST_RESEND_INVITATION_SMS": 2411, "BOARD_REQUEST_CREATE_INVITATION_VIEW_TOKEN": 2412, "BOARD_REQUEST_RESEND_VIEW_TOKEN": 2413, "BOARD_REQUEST_RENEW_WORKSPACE_ID": 2415, "BOARD_REQUEST_RESET_INVITE_CODE": 2416, "BOARD_PUBLISH_ACTION": 3000, "OBSOLETE_BOARD_REQUEST_SET_EMAIL_ADDRESS": 3010, "BOARD_REQUEST_SET_PHONE_NUMBER": 3020, "BOARD_REQUEST_CREATE_FOLDER": 3030, "BOARD_REQUEST_UPDATE_FOLDER": 3031, "BOARD_REQUEST_DELETE_FOLDER": 3032, "BOARD_REQUEST_COPY_FOLDER": 3033, "BOARD_REQUEST_MOVE_FOLDER": 3034, "BOARD_REQUEST_CREATE_FAVORITE": 3040, "BOARD_REQUEST_PIN": 3045, "BOARD_REQUEST_CHECK_ISRESTRICT": 3050, "BOARD_REQUEST_CALL_LOG": 3060, "BOARD_REQUEST_SET_OWNER_DELEGATE": 3065, "BOARD_REQUEST_SET_FEED_STATUS": 3066, "BOARD_REQUEST_SET_OWNER": 3067, "BOARD_REQUEST_CREATE_SIGNATURE": 3070, "BOARD_REQUEST_UPDATE_SIGNATURE": 3071, "BOARD_REQUEST_DELETE_SIGNATURE": 3072, "BOARD_REQUEST_ADD_SIGNATURE_SIGNEE": 3073, "BOARD_REQUEST_UPDATE_SIGNATURE_SIGNEE": 3074, "BOARD_REQUEST_REMOVE_SIGNATURE_SIGNEE": 3075, "BOARD_REQUEST_CREATE_SIGNATURE_PAGE_ELEMENT": 3076, "BOARD_REQUEST_UPLOAD_SIGNATURE_PAGE_ELEMENT": 3077, "BOARD_REQUEST_UPDATE_SIGNATURE_PAGE_ELEMENT": 3078, "BOARD_REQUEST_DELETE_SIGNATURE_PAGE_ELEMENT": 3079, "BOARD_REQUEST_START_SIGNATURE": 3080, "BOARD_REQUEST_UPLOAD_SIGNATURE_RESOURCE": 3081, "BOARD_REQUEST_SUBMIT_SIGNATURE": 3082, "BOARD_REQUEST_VIEW_SIGNATURE": 3083, "BOARD_REQUEST_SIGNEE_UPDATE": 3084, "BOARD_REQUEST_SIGNEE_UPLOAD_RESOURCE": 3085, "BOARD_REQUEST_DOWNLOAD_SIGNATURE_RESOURCE": 3086, "BOARD_REQUEST_SIGNATURE_UPDATE_ATTACHMENT": 3090, "BOARD_REQUEST_SIGNATURE_DELETE_ATTACHMENT": 3091, "BOARD_REQUEST_SIGNATURE_REOPEN": 3092, "BOARD_REQUEST_SIGNATURE_RESET_STATUS": 3093, "BOARD_REQUEST_SIGNATURE_REPLACE": 3094, "BOARD_REQUEST_SIGNATURE_REPLACE_PAGES_FROM_FILE": 3095, "BOARD_REQUEST_CREATE_WEBAPP_TOKEN": 3100, "BOARD_REQUEST_WEBAPP_CALLBACK": 3110, "FILE_FLOW_REQUEST_COMMENT_CREATE": 3260, "FILE_FLOW_REQUEST_COMMENT_UPDATE": 3261, "FILE_FLOW_REQUEST_COMMENT_UPLOAD": 3262, "FILE_FLOW_REQUEST_COMMENT_DELETE": 3263, "SESSION_FLOW_REQUEST_COMMENT_CREATE": 3320, "SESSION_FLOW_REQUEST_COMMENT_UPDATE": 3321, "SESSION_FLOW_REQUEST_COMMENT_UPLOAD": 3322, "SESSION_FLOW_REQUEST_COMMENT_DELETE": 3323, "SIGN_FLOW_REQUEST_COMMENT_CREATE": 3340, "SIGN_FLOW_REQUEST_COMMENT_UPDATE": 3341, "SIGN_FLOW_REQUEST_COMMENT_UPLOAD": 3342, "SIGN_FLOW_REQUEST_COMMENT_DELETE": 3343, "BOARD_REQUEST_CREATE_WAITING_USER": 3410, "BOARD_REQUEST_UPDATE_WAITING_USER": 3420, "BOARD_REQUEST_DELETE_WAITING_USER": 3430, "BOARD_REQUEST_UPDATE_RESOURCE": 3500, "BOARD_REQUEST_TRANSACTION_CREATE": 3510, "BOARD_REQUEST_TRANSACTION_DELETE": 3511, "BOARD_REQUEST_TRANSACTION_UPDATE": 3512, "BOARD_REQUEST_TRANSACTION_COPY": 3513, "BOARD_REQUEST_TRANSACTION_READ": 3514, "BOARD_REQUEST_TRANSACTION_STEP_SUBMIT": 3515, "BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT": 3516, "BOARD_REQUEST_TRANSACTION_UPDATE_ATTACHMENT": 3517, "BOARD_REQUEST_TRANSACTION_DELETE_ATTACHMENT": 3518, "BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_RESOURCE": 3519, "BOARD_REQUEST_TRANSACTION_COMMENT_CREATE": 3520, "BOARD_REQUEST_TRANSACTION_COMMENT_UPDATE": 3521, "BOARD_REQUEST_TRANSACTION_COMMENT_DELETE": 3522, "BOARD_REQUEST_TRANSACTION_COMMENT_UPLOAD": 3523, "BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_FILE": 3524, "BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_URL": 3525, "BOARD_REQUEST_TRANSACTION_STEP_SUBMIT_BATCH": 3530, "BOARD_REQUEST_TRANSACTION_VIEW": 3532, "BOARD_REQUEST_TRANSACTION_RESET_STATUS": 3538, "BOARD_REQUEST_TRANSACTION_REOPEN": 3539, "BOARD_REQUEST_TRANSACTION_STEP_REOPEN": 3540, "BOARD_REQUEST_TRANSACTION_DELETE_BATCH": 3541, "BOARD_REQUEST_TRANSACTION_UPLOAD_RESOURCE": 3542, "BOARD_REQUEST_TRANSACTION_UPLOAD_RESOURCE_URL": 3543, "BOARD_REQUEST_TRANSACTION_DOWNLOAD_RESOURCE": 3544, "BOARD_REQUEST_TRANSACTION_REMINDER_CREATE": 3545, "BOARD_REQUEST_TRANSACTION_REMINDER_UPDATE": 3546, "BOARD_REQUEST_TRANSACTION_REMINDER_DELETE": 3547, "BOARD_REQUEST_SET_FEED_UNREAD": 3600, "BOARD_REQUEST_SET_ISTEMP_OFF": 3531, "BOARD_REQUEST_UPDATE_RSVP": 3535, "BOARD_REQUEST_CREATE_PIN": 3550, "BOARD_REQUEST_DELETE_PIN": 3551, "BOARD_REQUEST_READ_FLAT_PINS": 3555, "BOARD_REQUEST_SET_BOARD_TYPE": 3556, "BOARD_REQUEST_SET_ACTIVE": 3557, "BOARD_REQUEST_WORKFLOW_CREATE": 3560, "BOARD_REQUEST_WORKFLOW_UPDATE": 3561, "BOARD_REQUEST_WORKFLOW_DELETE": 3562, "BOARD_REQUEST_READ_PREDECESSORS": 3565, "WORKFLOW_REQUEST_CREATE_TEMPLATE": 3570, "WORKFLOW_REQUEST_UPDATE_TEMPLATE": 3571, "WORKFLOW_REQUEST_DELETE_TEMPLATE": 3572, "WORKFLOW_REQUEST_LIST_TEMPLATE": 3574, "WORKFLOW_REQUEST_USE_TEMPLATE": 3575, "WORKFLOW_REQUEST_COPY_TEMPLATE": 3576, "WORKFLOW_REQUEST_LIST_PREBUILT_TEMPLATE": 3577, "WORKFLOW_REQUEST_UPDATE_BOARD_WORKFLOW": 3578, "WORKFLOW_REQUEST_UPDATE_BOARD_WORKFLOW_STEP": 3579, "WORKFLOW_REQUEST_SKIP_BOARD_WORKFLOW_STEP": 3580, "WORKFLOW_REQUEST_CREATE_WORKFLOW": 3581, "WORKFLOW_REQUEST_UPDATE_WORKFLOW": 3582, "WORKFLOW_REQUEST_DELETE_WORKFLOW": 3583, "WORKFLOW_REQUEST_UPDATE_STATUS": 3584, "WORKFLOW_REQUEST_RESTART": 3585, "WORKFLOW_REQUEST_COMPLETE": 3586, "WORKFLOW_REQUEST_CANCEL": 3587, "WORKFLOW_REQUEST_REOPEN_STEP": 3588, "WORKFLOW_REQUEST_SKIP_STEP": 3589, "WORKFLOW_REQUEST_INCREASE_USED_COUNT": 3590, "WORKFLOW_REQUEST_COPY_AS_TEMPLATE": 3591, "WORKFLOW_REQUEST_READ_TEMPLATE_LIBRARY_FOLDER_COUNT": 3592, "WORKFLOW_REQUEST_COPY_WORKFLOW": 3593, "WORKFLOW_REQUEST_READ_TEMPLATE_LIBRARY_TEMPLATE_COUNT": 3594, "WORKFLOW_REQUEST_MOVE_TEMPLATE": 3595, "BOARD_REQUEST_CREATE_PROPERTY": 3626, "BOARD_REQUEST_UPDATE_PROPERTY": 3627, "BOARD_REQUEST_DELETE_PROPERTY": 3628, "BOARD_REQUEST_UPDATE_FEED_REACTION": 3629, "BOARD_REQUEST_CREATE_BROADCAST": 3630, "BOARD_REQUEST_UPDATE_BROADCAST": 3631, "BOARD_REQUEST_DELETE_BROADCAST": 3632, "BOARD_REQUEST_UPDATE_MEETING_TRANSCRIPTION": 3640, "BOARD_REQUEST_DOWNLOAD_MEETING_TRANSCRIPTION": 3641, "BOARD_REQUEST_COPY_MEETING_TRANSCRIPTION": 3642, "BOARD_REQUEST_TRANSCRIPT_RESOURCE": 3643, "BOARD_REQUEST_UPDATE_MEETING_SUMMARY": 3650, "BOARD_REQUEST_UNITTEST": 3998, "BOARD_REQUEST_MAX": 3999, "SERVER_AUDIO_CAPACITY": 4000, "SERVER_PBX_REPORT": 4002, "SERVER_DESKTOP_SHARE_CAPACITY": 4010, "SERVER_PROBE": 4100, "SERVER_OBJECT_READ": 4110, "SERVER_OBJECT_SUBSCRIBE": 4111, "SERVER_OBJECT_UNSUBSCRIBE": 4112, "SERVER_OBJECT_ACTIVITY": 4115, "SERVER_OBJECT_AUDIT": 4116, "SERVER_OBJECT_WRITE": 4120, "SERVER_OBJECT_STATS": 4122, "SERVER_OBJECT_LIST_SERVERS": 4124, "SERVER_FILE_DOWNLOAD": 4130, "SERVER_FILE_UPLOAD": 4132, "SERVER_FILE_PREVIEW": 4134, "SERVER_USER_DISABLE": 4140, "SERVER_USER_ENABLE": 4141, "SERVER_USER_LEVEL_UPGRADE": 4142, "SERVER_USER_LEVEL_DOWNGRADE": 4143, "SERVER_USER_READ": 4144, "SERVER_USER_UP_SIZE": 4145, "SERVER_USER_DOWN_SIZE": 4146, "SERVER_REDO_JOB": 4150, "SERVER_FORWARD_REQUEST": 4151, "SERVER_UPLOAD_CRASH_REPORT": 4200, "SERVER_LIST_CRASH_REPORTS": 4210, "SERVER_DOWNLOAD_CRASH_REPORT": 4220, "SERVER_DELETE_CRASH_REPORT": 4230, "SERVER_READ_STATISTICS": 4300, "SERVER_UPDATE_STATISTICS": 4301, "SERVER_TOKEN_DECODE": 4310, "SERVER_SERVICE_PROVIDERS_READ": 4400, "SERVER_SERVICE_PROVIDERS_UPDATE": 4410, "SERVER_IDP_CONFIG_READ": 4420, "SERVER_WEBAPP_READ": 4430, "SERVER_SYSTEM_CONFIG_READ": 4500, "SERVER_SYSTEM_CONFIG_UPDATE": 4510, "SERVER_REQUEST_VALIDATION_CODE": 4520, "SERVER_GROUP_LIST_ADD": 4530, "SERVER_GROUP_LIST_REMOVE": 4535, "SERVER_CHART_READ": 4600, "SERVER_GROUP_USAGE_REPORT": 4700, "SERVER_PROBE_REPORT": 4710, "SERVER_STATISTICS_REPORT": 4720, "SERVER_REQUEST_MAX": 4999, "AGENT_REQUEST_LIST_FOLDER": 5000, "AGENT_REQUEST_DOWNLOAD_FILE": 5010, "AGENT_REQUEST_PREVIEW_FILE": 5011, "AGENT_REQUEST_UPLOAD_RESOURCE": 5020, "AGENT_REQUEST_UPLOAD_FILE": 5030, "AGENT_REQUEST_UPLOAD_FILE_RESOURCE": 5031, "AGENT_REQUEST_CREATE_FOLDER": 5040, "AGENT_REQUEST_MOVE_ENTRY": 5041, "AGENT_REQUEST_DELETE_ENTRY": 5050, "AGENT_REQUEST_QUERY_UPLOAD_PROGRESS": 5060, "AGENT_PUBLISH_RESPONSE": 5100, "AGENT_SERVE_FILE": 5110, "AGENT_REQUEST_ONLINE": 5120, "AGENT_REQUEST_OFFLINE": 5121, "AGENT_REQUEST_MAX": 5999, "WEBAPP_REQUEST_CREATE": 6000, "WEBAPP_REQUEST_READ": 6010, "WEBAPP_REQUEST_UPDATE": 6020, "WEBAPP_REQUEST_DELETE": 6030, "WEBAPP_REQUEST_LIST": 6050, "WEBAPP_REQUEST_LIST_BOT": 6052, "WEBAPP_REQUEST_LIST_EMBEDDED": 6053, "WEBAPP_REQUEST_LIST_SUBSCRIPTION": 6054, "WEBAPP_REQUEST_LIST_INBOX_BOT": 6055, "WEBAPP_REQUEST_DOWNLOAD_RESOURCE": 6100, "WEBAPP_REQUEST_UPLOAD_RESOURCE": 6110, "WEBAPP_REQUEST_UPLOAD_PICTURE": 6112, "WEBAPP_REQUEST_CREATE_TOKEN": 6200, "WEBAPP_REQUEST_MAX": 6999, "GROUP_REQUEST_CREATE": 7000, "GROUP_REQUEST_READ_CAPABILITY": 7001, "GROUP_REQUEST_REGISTER": 7002, "GROUP_REQUEST_LIST_AVAILABLE_BASE_DOMAINS": 7003, "GROUP_REQUEST_CHECK_DOMAIN_AVAILABILITY": 7004, "GROUP_REQUEST_UNREGISTER": 7009, "GROUP_REQUEST_READ": 7010, "GROUP_REQUEST_READ_MEMBERS": 7011, "GROUP_REQUEST_READ_SORT_MEMBERS": 7025, "GROUP_REQUEST_READ_MANAGEMENT_MEMBERS": 7024, "GROUP_REQUEST_EXPORT_MEMBERS": 7015, "GROUP_REQUEST_EXPORT_USER_ACTIVITIES": 7016, "GROUP_REQUEST_EXPORT_CLIENT_ENGAGEMENT": 7017, "GROUP_REQUEST_EXPORT_INTERNAL_USER_ENGAGEMENT": 7018, "GROUP_REQUEST_EXPORT_CLIENT_COVERAGE": 7019, "GROUP_REQUEST_EXPORT_SOCIAL_ENGAGEMENT": 7012, "GROUP_REQUEST_READ_MANAGEMENT_USER_ACTIVITIES": 7013, "GROUP_REQUEST_READ_MANAGEMENT_USER_BOARDS": 7014, "GROUP_REQUEST_EXPORT_SERVICE_REQUEST_SUMMARY": 7080, "GROUP_REQUEST_EXPORT_SERVICE_REQUEST_AGENT_SUMMARY": 7081, "GROUP_REQUEST_EXPORT_ACD_SUMMARY": 7082, "GROUP_REQUEST_EXPORT_ACD_AGENT_SUMMARY": 7083, "GROUP_REQUEST_EXPORT_CLIENT_USAGE": 7090, "GROUP_REQUEST_EXPORT_INTERNAL_USAGE": 7091, "GROUP_REQUEST_EXPORT_DAILY_ORG_ACTIVITY": 7092, "GROUP_REQUEST_EXPORT_DAILY_USER_ACTIVITY": 7093, "GROUP_REQUEST_UPDATE": 7020, "GROUP_REQUEST_UPDATE_ALIAS": 7021, "GROUP_REQUEST_UPDATE_MEMBER_ALIAS": 7022, "GROUP_REQUEST_READ_ALIAS": 7023, "GROUP_REQUEST_CANCEL": 7030, "GROUP_REQUEST_READ_USAGE": 7040, "GROUP_REQUEST_SUBSCRIBE": 7050, "GROUP_REQUEST_UNSUBSCRIBE": 7060, "GROUP_REQUEST_READ_APP_ASSOCIATION": 7070, "GROUP_REQUEST_READ_APP_ASSETLINKS": 7071, "GROUP_REQUEST_READ_TELEPHONY_DOMAIN": 7075, "GROUP_REQUEST_INVITE": 7100, "GROUP_REQUEST_INVITE_CSV_IMPORT": 7105, "GROUP_REQUEST_INVITE_CSV_IMPORT_BY_INTERNAL": 7108, "GROUP_REQUEST_JOIN": 7110, "GROUP_REQUEST_JOIN_VIA_INVITATION": 7115, "GROUP_REQUEST_LEAVE": 7120, "GROUP_REQUEST_RESEND_INVITATION_EMAIL": 7130, "GROUP_REQUEST_INVITATION_CONFIRM_EMAIL": 7135, "GROUP_REQUEST_RESEND_INVITATION_SMS": 7140, "GROUP_REQUEST_INVITATION_CONFIRM_SMS": 7145, "GROUP_REQUEST_EXPEL": 7150, "GROUP_REQUEST_REMOVE_MEMBER": 7151, "GROUP_REQUEST_SET_ACCESS_TYPE": 7160, "GROUP_REQUEST_VIEW_INVITATION": 7170, "GROUP_REQUEST_DOWNLOAD_RESOURCE": 7180, "GROUP_REQUEST_UPLOAD_RESOURCE": 7190, "GROUP_REQUEST_USER_READ": 7200, "GROUP_REQUEST_USER_UPDATE": 7210, "GROUP_REQUEST_USER_UPDATE_EMAIL": 7212, "GROUP_REQUEST_USER_UPDATE_EMAIL_PHONE_NUMBER": 7212, "GROUP_REQUEST_USER_DISABLE": 7230, "GROUP_REQUEST_USER_ENABLE": 7240, "GROUP_REQUEST_USER_TRANSFER": 7250, "GROUP_REQUEST_USER_ARCHIVE": 7255, "GROUP_REQUEST_USERS_READ": 7260, "GROUP_REQUEST_READ_USER_BUSY_TIME": 7261, "GROUP_REQUEST_IMPORT_REDEEM_URL": 7270, "GROUP_REQUEST_RESET_REDEEM_URL": 7271, "GROUP_REQUEST_GET_REDEEM_URL": 7272, "GROUP_REQUEST_CREATE_BOARD_PROPERTY": 7280, "GROUP_REQUEST_UPDATE_BOARD_PROPERTY": 7281, "GROUP_REQUEST_DELETE_BOARD_PROPERTY": 7282, "GROUP_REQUEST_BOARD_LEAVE": 7400, "GROUP_REQUEST_BOARD_CREATE": 7401, "GROUP_REQUEST_BOARD_DELETE": 7402, "GROUP_REQUEST_BOARD_ADD_MEMBER": 7403, "GROUP_REQUEST_BOARD_REMOVE_MEMBER": 7404, "GROUP_REQUEST_BOARD_UPDATE_MEMBER": 7406, "GROUP_REQUEST_BOARD_UPDATE": 7405, "GROUP_REQUEST_SESSION_SCHEDULE": 7407, "GROUP_REQUEST_CREATE_RELATION": 7416, "GROUP_REQUEST_INVITE_AND_CREATE_RELATION": 7417, "GROUP_REQUEST_CONFIRM_RELATION": 7418, "GROUP_REQUEST_TRANSFER_RELATION": 7419, "GROUP_REQUEST_DELETE_RELATION": 7420, "GROUP_REQUEST_UPDATE_RELATION": 7421, "GROUP_REQUEST_INVITE_BOARD_REQUESTING_USER": 7422, "GROUP_REQUEST_CREATE_BOT_RELATION": 7430, "GROUP_REQUEST_DELETE_BOT_RELATION": 7431, "GROUP_REQUEST_CREATE_INTEGRATION": 7500, "GROUP_REQUEST_UPDATE_INTEGRATION": 7510, "GROUP_REQUEST_DELETE_INTEGRATION": 7520, "GROUP_REQUEST_VERIFY_INTEGRATION": 7530, "GROUP_REQUEST_GET_INTEGRATION_USER_ACCESSTOKEN": 7540, "GROUP_REQUEST_READ_TASKS": 7550, "GROUP_REQUEST_STRIPE_WEBHOOK": 7600, "GROUP_REQUEST_STRIPE_CUSTOMER": 7610, "GROUP_REQUEST_STRIPE_SUBSCRIBE": 7620, "GROUP_REQUEST_STRIPE_PRICE": 7630, "GROUP_REQUEST_STRIPE_INVOICES": 7640, "GROUP_REQUEST_STRIPE_UPCOMING_INVOICE": 7642, "GROUP_REQUEST_STRIPE_COUPON": 7650, "GROUP_REQUEST_STRIPE_PUBLISHABLE_KEY": 7660, "GROUP_REQUEST_CREATE_TEAM": 7700, "GROUP_REQUEST_UPDATE_TEAM": 7705, "GROUP_REQUEST_DELETE_TEAM": 7710, "GROUP_REQUEST_CREATE_PUBLIC_TEAM": 7711, "GROUP_REQUEST_UPDATE_PUBLIC_TEAM": 7712, "GROUP_REQUEST_DELETE_PUBLIC_TEAM": 7713, "GROUP_REQUEST_ADD_TEAM_MEMBER": 7720, "GROUP_REQUEST_REMOVE_TEAM_MEMBER": 7730, "GROUP_REQUEST_LEAVE_TEAM": 7740, "GROUP_REQUEST_REASSIGN_TEAM_OWNER": 7750, "GROUP_REQUEST_SET_TEAM_MEMBER_ACCESS_TYPE": 7751, "GROUP_REQUEST_ADD_TEAM_MANAGER": 7752, "GROUP_REQUEST_REMOVE_TEAM_MANAGER": 7753, "GROUP_REQUEST_ASSIGN_TELEPHONY_DOMAIN": 7760, "GROUP_REQUEST_ADD_INTEGRATION_SUBSCRIBER": 7770, "GROUP_REQUEST_REMOVE_INTEGRATION_SUBSCRIBER": 7780, "GROUP_REQUEST_READ_INTEGRATION_SUBSCRIBERS": 7790, "GROUP_REQUEST_SET_BOARD_MEMBER_ACCESS_TYPE": 7800, "GROUP_REQUEST_USER_READ_ACTIVITIES": 7810, "GROUP_REQUEST_READ_GROUP_ACTIVITIES": 7811, "GROUP_REQUEST_USER_POST_ACTIVITIES": 7820, "GROUP_REQUEST_SUBSCRIBE_SERVICE_REQUESTS": 7850, "GROUP_REQUEST_CREATE_ROLE": 7900, "GROUP_REQUEST_UPDATE_ROLE": 7901, "GROUP_REQUEST_DELETE_ROLE": 7902, "GROUP_REQUEST_USER_RESET_PASSWORD": 7910, "GROUP_REQUEST_USER_UPDATE_PICTURES": 7911, "GROUP_REQUEST_USER_UPLOAD_PROFILE_PICTURES": 7912, "GROUP_REQUEST_USER_UPLOAD_RESOURCE": 7913, "GROUP_REQUEST_CREATE_SOCIAL_CONNECTION": 7914, "GROUP_REQUEST_UPDATE_SOCIAL_CONNECTION": 7915, "GROUP_REQUEST_DELETE_SOCIAL_CONNECTION": 7916, "GROUP_REQUEST_READ_SOCIAL_CONNECTION": 7917, "GROUP_REQUEST_JWT_TOKEN": 7920, "GROUP_REQUEST_USER_RESET_PASSWORD_SMS": 7925, "GROUP_REQUEST_CREATE_ROUTING_CHANNEL": 7930, "GROUP_REQUEST_UPDATE_ROUTING_CHANNEL": 7931, "GROUP_REQUEST_DELETE_ROUTING_CHANNEL": 7932, "GROUP_REQUEST_UPDATE_USER_INVITATION_TOKEN": 7940, "GROUP_REQUEST_READ_SUBSCRIPTION_BOARDS": 7941, "GROUP_REQUEST_READ_CONTENT_LIBRARY_BOARDS": 7942, "GROUP_REQUEST_READ_CONTENT_LIBRARY_BOARD_COUNT": 7943, "GROUP_REQUEST_UPDATE_CRM_REPORT": 7950, "GROUP_REQUEST_READ_CRM_REPORT": 7951, "GROUP_REQUEST_BOX_ACCESS_TOKEN": 7960, "GROUP_REQUEST_SEARCH_USER_BOARD": 7970, "GROUP_REQUEST_USER_BOARD_LOOKUP": 7971, "GROUP_REQUEST_USER_UPDATE_OUT_OF_OFFICE": 7972, "GROUP_REQUEST_INVITE_AND_USE_WORKFLOW_TEMPLATE": 7973, "GROUP_REQUEST_USER_UPDATE_DISTRIBUTION_LIST": 7974, "GROUP_REQUEST_USER_DELETE_FUTURE_SESSIONS": 7980, "GROUP_REQUEST_USER_DELETE_SCHEDULE_BOARDS": 7981, "GROUP_REQUEST_MAX": 7999, "PRESENCE_REQUEST_READ": 8000, "PRESENCE_USER_REQUEST_READ": 8001, "PRESENCE_REQUEST_MESSAGE": 8010, "PRESENCE_USER_REQUEST_UPDATE": 8021, "PRESENCE_REQUEST_MAX": 8999, "PARTNER_REQUEST_CREATE": 9000, "PARTNER_REQUEST_READ": 9010, "PARTNER_REQUEST_UPDATE": 9020, "PARTNER_REQUEST_LIST": 9040, "PARTNER_REQUEST_ADD_MEMBER": 9100, "PARTNER_REQUEST_DELETE_MEMBER": 9110, "PARTNER_REQUEST_VIEW_INVITATION": 9102, "PARTNER_REQUEST_ADD_PLAN_CODE": 9120, "PARTNER_REQUEST_DELETE_PLAN_CODE": 9130, "PARTNER_REQUEST_CREATE_INTEGRATION": 9200, "PARTNER_REQUEST_UPDATE_INTEGRATION": 9210, "PARTNER_REQUEST_DELETE_INTEGRATION": 9220, "PARTNER_REQUEST_READ_STATISTICS": 9300, "PARTNER_REQUEST_READ_PLAN_CODES": 9310, "PARTNER_REQUEST_READ_USAGE": 9320, "PARTNER_REQUEST_CREATE_GROUP": 9400, "PARTNER_REQUEST_LIST_GROUPS": 9410, "PARTNER_REQUEST_CREATE_USERS": 9500, "PARTNER_REQUEST_CREATE_TELEPHONY_DOMAIN": 9600, "PARTNER_REQUEST_UPDATE_TELEPHONY_DOMAIN": 9601, "PARTNER_REQUEST_DELETE_TELEPHONY_DOMAIN": 9602, "PARTNER_REQUEST_LIST_TELEPHONY_DOMAINS": 9610, "PARTNER_REQUEST_READ_TELEPHONY_DOMAIN": 9611, "PARTNER_REQUEST_SET_DEFAULT_TELEPHONY_DOMAIN": 9612, "PARTNER_REQUEST_CREATE_TELEPHONE_NUMBER": 9620, "PARTNER_REQUEST_UPDATE_TELEPHONE_NUMBER": 9621, "PARTNER_REQUEST_DELETE_TELEPHONE_NUMBER": 9622, "PARTNER_REQUEST_UPLOAD_TELEPHONE_NUMBER_RESOURCE": 9630, "PARTNER_REQUEST_DOWNLOAD_TELEPHONE_NUMBER_RESOURCE": 9631, "PARTNER_REQUEST_MAX": 9999, "TELEPHONY_REQUEST_ONCALLIN": 10000, "TELEPHONY_REQUEST_SUBMIT_SESSIONKEY": 10010, "TELEPHONY_REQUEST_SUBMIT_PARTICIPANTNUM": 10020, "TELEPHONY_REQUEST_ONLEAVE": 10030, "TELEPHONY_REQUEST_ON_SIPGATEWAY_CALL": 10040, "TELEPHONY_REQUEST_POST_SIPGATEWAY_CALL": 10050, "TELEPHONY_REQUEST_ON_TEXT_MESSAGE": 10060, "TELEPHONY_REQUEST_ON_VOICE_MESSAGE": 10070, "TELEPHONY_REQUEST_MAX": 10999, "ROUTING_ACD_REQUEST_CREATE": 11010, "ROUTING_ACD_REQUEST_UPDATE": 11020, "ROUTING_ACD_REQUEST_ACCEPT": 11030, "ROUTING_ACD_REQUEST_DECLINE": 11040, "ROUTING_ACD_REQUEST_READ_OFFICE_HOUR": 11050, "ROUTING_ACD_REQUEST_LEAVE_MESSAGE": 11060, "ROUTING_ACD_REQUEST_ADD_BOT": 11070, "ROUTING_ACD_REQUEST_REMOVE_BOT": 11072, "ROUTING_SERVICE_REQUEST_SUBSCRIBE": 11110, "ROUTING_SERVICE_REQUEST_LIST": 11120, "ROUTING_SERVICE_REQUEST_CREATE": 11130, "ROUTING_SERVICE_REQUEST_UPDATE": 11140, "ROUTING_REQUEST_MAX": 11999, "SEARCH_REQUEST_INDEX": 20000, "SEARCH_REQUEST_SEARCH": 20010, "SEARCH_REQUEST_MAX": 20999, "SSO_SP_REQUEST_GET": 40000, "SSO_SP_REQUEST_POST": 40010, "SSO_SP_REQUEST_MAX": 40499, "SSO_IDP_REQUEST_GET": 40500, "SSO_IDP_REQUEST_POST": 40510, "SSO_REQUEST_MAX": 40999, "ACTIVITY_REQUEST_QUERY": 41000, "ACTIVITY_REQUEST_MAX": 41100, "CLIENT_USERS_FLOW_ACTION_SUMMARY": 51000, "INT_USERS_FLOW_ACTION_SUMMARY": 51010, "FLOW_ACTION_LIST": 51020, "FLOW_BINDER_LIST": 51030, "EXTERNAL_USERS": 51040, "INTERNAL_USERS": 51050, "WORKSPACES": 51060, "WORKSPACE_ACTIONS": 51070, "GROUP_REQUEST_EXPORT_WORKSPACE_LIST": 51080, "GROUP_REQUEST_EXPORT_ACTION_LIST": 51090, "USERS_WITH_OPEN_ACTIONS_CNT": 51100, "GROUP_REQUEST_EXPORT_MEETING_USAGE": 51110, "GROUP_REQUEST_EXPORT_MEETING_LIST": 51120, "GROUP_REQUEST_EXPORT_SR_LIST": 51130 } }, "ClientResponseCode": { "edition": "proto2", "values": { "RESPONSE_CONNECT_SUCCESS": 10, "RESPONSE_ERROR_UPGRADE_REQUIRED": 20, "RESPONSE_SUCCESS": 200, "RESPONSE_ACCEPTED": 202, "RESPONSE_NO_CONTENT": 204, "RESPONSE_ERROR_STATUS_MOVED": 302, "RESPONSE_ERROR_X_ACCEL_REDIRECT": 306, "RESPONSE_ERROR_TEMPORARY_REDIRECTION": 307, "RESPONSE_ERROR_INVALID_REQUEST": 400, "RESPONSE_ERROR_INVALID_TOKEN": 401, "RESPONSE_ERROR_PAYMENT_REQUIRED": 402, "RESPONSE_ERROR_PERMISSION": 403, "RESPONSE_ERROR_NOT_FOUND": 404, "RESPONSE_ERROR_INVALID_OBJECT": 406, "RESPONSE_ERROR_TIMEOUT": 408, "RESPONSE_ERROR_CONFLICT": 409, "RESPONSE_ERROR_PRECONDITION_FAILED": 412, "RESPONSE_ERROR_EXCEED_LIMIT": 413, "RESPONSE_ERROR_TOO_MANY_REQUESTS": 429, "RESPONSE_ERROR_FAILED": 500, "RESPONSE_ERROR_BAD_GATEWAY": 502, "RESPONSE_ERROR_SERVICE_UNAVAILABLE": 503, "RESPONSE_SUBSCRIPTION_DATA": 1000, "RESPONSE_CONNECTION_TOKEN_VERIFIED": 1010, "RESPONSE_ERROR_DISCONNECTED": 3000 } }, "ClientResponseDetailCode": { "edition": "proto2", "values": { "DETAIL_CODE_NO_DETAILS": 0, "DETAIL_CODE_CLIENT_UPGRADE_RECOMMENDED": 10, "EXCEED_USER_BOARDS_MAX": 100, "EXCEED_BOARD_PAGES_MAX": 110, "EXCEED_BOARD_USERS_MAX": 120, "EXCEED_SESSION_USERS_MAX": 130, "EXCEED_GROUP_BOARDS_MAX": 140, "EXCEED_GROUP_USERS_MAX": 145, "EXCEED_UPLOAD_CLIENT_BODY_MAX": 160, "EXCEED_USER_CLOUD_MAX": 170, "EXCEED_NAME_LENGTH_MAX": 180, "ERROR_USER_DISABLED": 2000, "ERROR_GROUP_SUBSCRIPTION_EXPIRED": 2010, "ERROR_SSO_ENFORCED": 2020, "ERROR_INVALID_BOARD_ID": 2030, "ERROR_VIRUS_DETECTED": 2040, "ERROR_FILE_TYPE_NOT_SUPPORTED": 2050, "ERROR_PASSWORD_RULE_CONFLICT": 2060, "ERROR_VERIFICATION_CODE_EXPIRED": 2070, "ERROR_BOARD_VIEW_TOKEN_EXPIRED": 2071, "ERROR_LOGIN_LOCKED": 2080, "ERROR_USER_NOT_REGISTERED": 2081, "ERROR_USER_NOT_GROUP_MEMBER": 2082, "ERROR_USER_NOT_AUTHORIZED": 2083, "ERROR_NOT_EMPTY": 2084, "ERROR_USER_NOT_LOGIN": 2085, "ERROR_INVALID_USER_TYPE": 2086, "ERROR_2FA_REQUIRED": 2087, "EXCEED_FILE_SIZE_MAX": 3010, "ERROR_INVALID_FILE_ENCODING": 3020, "EXCEED_FILE_LINES_MAX": 3030, "ERROR_INVALID_FILE_FORMAT": 3035, "ERROR_INVALID_FILE_HEADER": 3040, "ERROR_INVALID_FIELD": 3050, "ERROR_DUPLICATE_FIELD": 3051, "ERROR_EXPECTED_FIELD": 3052, "AGENT_ERROR_INVALID_PASSCODE": 40301, "AGENT_ERROR_INVALID_TIMESTAMP": 40302, "AGENT_ERROR_INVALID_PATH": 40303, "ERROR_INTEGRATION_INVALID_GROUP": 50010, "ERROR_INTEGRATION_EXPIRED_GROUP_SUBSCRIPTION": 50020, "ERROR_INTEGRATION_INVALID_EXTERNAL_RESPONSE": 50030, "ERROR_INTEGRATION_NOT_GROUP_MEMBER": 50100, "ERROR_INTEGRATION_EXCEED_GROUP_MEMBER_QUANTITY": 50110, "ERROR_INTEGRATION_PENDING_GROUP_MEMBER": 50120, "ERROR_SESSION_NOT_STARTED": 60010, "ERROR_SESSION_ENDED": 60020, "ERROR_SESSION_PASSWORD_PROTECTED": 60030, "ERROR_SESSION_WAITING_LIST_ENABLED": 60040, "ERROR_SESSION_LOCKED": 60050, "ERROR_FLOW_STEP_INVALID_STATUS": 60100 } }, "UserActivityRoutingDetail": { "edition": "proto2", "fields": { "board_id": { "type": "string", "id": 10 }, "duration": { "type": "uint64", "id": 20 }, "count": { "type": "uint64", "id": 30 } } }, "UserActivityLogDetail": { "edition": "proto2", "fields": { "object": { "type": "CacheObject", "id": 10 }, "routing_detail": { "type": "UserActivityRoutingDetail", "id": 20 } } }, "UserActivityLogEntry": { "edition": "proto2", "fields": { "action_group_id": { "type": "string", "id": 10 }, "action_group": { "type": "string", "id": 20 }, "action_type_id": { "type": "string", "id": 30 }, "action_type": { "type": "string", "id": 40 }, "action_description": { "type": "string", "id": 50 }, "platform": { "type": "string", "id": 90 }, "browser": { "type": "string", "id": 100 }, "device_model": { "type": "string", "id": 110 }, "client_private_ip": { "type": "string", "id": 120 }, "client_public_ip": { "type": "string", "id": 130 }, "client_version": { "type": "string", "id": 140 }, "detail": { "type": "UserActivityLogDetail", "id": 150 }, "created_time": { "type": "uint64", "id": 160 } } }, "UserEngagementQuery": { "edition": "proto2", "fields": { "actor_id": { "type": "string", "id": 10 }, "peer_actor_id": { "rule": "repeated", "type": "string", "id": 20 } } }, "QueryFilter": { "edition": "proto2", "fields": { "key": { "type": "string", "id": 10 }, "value": { "type": "string", "id": 20 }, "op": { "type": "string", "id": 30 } } }, "PaginationFilter": { "edition": "proto2", "fields": { "conditions": { "rule": "repeated", "type": "string", "id": 10 } } }, "UserActivityLog": { "edition": "proto2", "fields": { "actor_org_id": { "type": "string", "id": 10 }, "actor_id": { "type": "string", "id": 20 }, "actor_email": { "type": "string", "id": 30 }, "actor_unique_id": { "type": "string", "id": 40 }, "actor_phone_number": { "type": "string", "id": 80 }, "actor_type": { "type": "UserType", "id": 50 }, "actor": { "type": "User", "id": 200 }, "suppress_stats": { "type": "bool", "id": 70 }, "activities": { "rule": "repeated", "type": "UserActivityLogEntry", "id": 60 }, "actor_ids": { "rule": "repeated", "type": "string", "id": 90 }, "from_date": { "type": "uint64", "id": 100 }, "to_date": { "type": "uint64", "id": 110 }, "action_group_id": { "rule": "repeated", "type": "string", "id": 120 }, "action_type_id": { "rule": "repeated", "type": "string", "id": 130 }, "user_engagement_query": { "rule": "repeated", "type": "UserEngagementQuery", "id": 140 }, "page_start": { "type": "uint64", "id": 201 }, "page_size": { "type": "uint64", "id": 202 }, "page_number": { "type": "uint64", "id": 203 }, "sort_field_name": { "type": "string", "id": 204 }, "sort_method": { "type": "string", "id": 205 }, "filters": { "rule": "repeated", "type": "QueryFilter", "id": 206 } } }, "Engagement": { "edition": "proto2", "fields": { "timestamp": { "type": "uint64", "id": 10 }, "active_client_count": { "type": "uint64", "id": 20 }, "binder_active_client_count": { "type": "uint64", "id": 21 }, "p2p_active_client_count": { "type": "uint64", "id": 22 }, "group_active_client_count": { "type": "uint64", "id": 23 }, "session_count": { "type": "uint64", "id": 30 }, "doc_shared_count": { "type": "uint64", "id": 40 }, "chat_count": { "type": "uint64", "id": 50 }, "meet_count": { "type": "uint64", "id": 60 }, "anotation_count": { "type": "uint64", "id": 70 } } }, "SocialEngagement": { "edition": "proto2", "fields": { "timestamp": { "type": "uint64", "id": 10 }, "social_binder_create_count": { "type": "uint64", "id": 20 }, "social_binder_active_count": { "type": "uint64", "id": 21 }, "client_chat_count": { "type": "uint64", "id": 30 }, "internal_chat_count": { "type": "uint64", "id": 31 }, "client_doc_shared_count": { "type": "uint64", "id": 40 }, "internal_doc_shared_count": { "type": "uint64", "id": 41 }, "unboard_client_count": { "type": "uint64", "id": 50 }, "unreply_message_count": { "type": "uint64", "id": 51 } } }, "SocialEngagementRecord": { "edition": "proto2", "fields": { "timestamp": { "type": "uint64", "id": 10 }, "wechat_binder_create_count": { "type": "uint64", "id": 20 }, "wechat_binder_active_count": { "type": "uint64", "id": 21 }, "wechat_client_chat_count": { "type": "uint64", "id": 30 }, "wechat_internal_chat_count": { "type": "uint64", "id": 31 }, "wechat_client_doc_shared_count": { "type": "uint64", "id": 40 }, "wechat_internal_doc_shared_count": { "type": "uint64", "id": 41 }, "whatsapp_binder_create_count": { "type": "uint64", "id": 120 }, "whatsapp_binder_active_count": { "type": "uint64", "id": 121 }, "whatsapp_client_chat_count": { "type": "uint64", "id": 130 }, "whatsapp_internal_chat_count": { "type": "uint64", "id": 131 }, "whatsapp_client_doc_shared_count": { "type": "uint64", "id": 140 }, "whatsapp_internal_doc_shared_count": { "type": "uint64", "id": 141 } } }, "ClientCoverage": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10 }, "client_count": { "type": "uint64", "id": 20 }, "engaged_client_count": { "type": "uint64", "id": 30 }, "total_engaged_client_count": { "type": "uint64", "id": 31 }, "doc_shared_count": { "type": "uint64", "id": 40 }, "chat_count": { "type": "uint64", "id": 50 }, "meet_count": { "type": "uint64", "id": 60 }, "left_client_count": { "type": "uint64", "id": 70 }, "assigned_client_count": { "type": "uint64", "id": 80 }, "client_coverage": { "type": "uint64", "id": 90 }, "client_daily_coverage": { "type": "uint64", "id": 100 } } }, "UserEngageMent": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10 }, "peer_user": { "type": "User", "id": 20 }, "msg_count": { "type": "uint64", "id": 50 }, "peer_msg_count": { "type": "uint64", "id": 51 }, "doc_shared_count": { "type": "uint64", "id": 60 }, "peer_doc_shared_count": { "type": "uint64", "id": 61 }, "meet_count": { "type": "uint64", "id": 70 }, "esign_count": { "type": "uint64", "id": 80 }, "peer_esign_count": { "type": "uint64", "id": 81 }, "active_relation_total": { "type": "uint64", "id": 82 }, "todo_count": { "type": "uint64", "id": 90 }, "peer_todo_count": { "type": "uint64", "id": 91 }, "timestamp": { "type": "uint64", "id": 100 } } }, "ACDSummary": { "edition": "proto2", "fields": { "timestamp": { "type": "uint64", "id": 1 }, "new_acd_number": { "type": "uint64", "id": 10 }, "accepted_acd_number": { "type": "uint64", "id": 20 }, "request_timeout_acd_number": { "type": "uint64", "id": 30 }, "leave_msg_acd_number": { "type": "uint64", "id": 40 }, "total_wait_time": { "type": "uint64", "id": 50 }, "has_guest_number": { "type": "uint64", "id": 60 }, "has_meet_number": { "type": "uint64", "id": 70 }, "has_file_number": { "type": "uint64", "id": 80 }, "total_msg_file_number": { "type": "uint64", "id": 90 }, "total_acd_duration": { "type": "uint64", "id": 100 }, "new_anonymous_acd_number": { "type": "uint64", "id": 110 }, "closed_acd_number": { "type": "uint64", "id": 120 } } }, "ACDAgentSummary": { "edition": "proto2", "fields": { "timestamp": { "type": "uint64", "id": 1 }, "user": { "type": "User", "id": 2 }, "received_acd_number": { "type": "uint64", "id": 10 }, "accepted_acd_number": { "type": "uint64", "id": 20 }, "rejected_acd_number": { "type": "uint64", "id": 30 }, "missed_acd_number": { "type": "uint64", "id": 40 }, "has_guest_number": { "type": "uint64", "id": 50 }, "has_meet_number": { "type": "uint64", "id": 60 }, "has_file_number": { "type": "uint64", "id": 70 }, "total_msg_file_number": { "type": "uint64", "id": 80 }, "total_acd_duration": { "type": "uint64", "id": 90 }, "closed_acd_number": { "type": "uint64", "id": 100 } } }, "SRSummary": { "edition": "proto2", "fields": { "timestamp": { "type": "uint64", "id": 1 }, "new_sr_number": { "type": "uint64", "id": 10 }, "new_unassigned_number": { "type": "uint64", "id": 20 }, "return_to_inbox_number": { "type": "uint64", "id": 30 }, "assigned_number": { "type": "uint64", "id": 40 }, "assigned_no_agent_response_number": { "type": "uint64", "id": 50 }, "total_assignment_time": { "type": "uint64", "id": 60 }, "total_resolution_time": { "type": "uint64", "id": 70 }, "active_sr_number": { "type": "uint64", "id": 80 }, "total_msg_file_number": { "type": "uint64", "id": 90 }, "agent_resolve_number": { "type": "uint64", "id": 100 }, "client_close_number": { "type": "uint64", "id": 110 }, "client_reopen_number": { "type": "uint64", "id": 120 }, "reassigned_number": { "type": "uint64", "id": 121 }, "unique_client_number": { "type": "uint64", "id": 130 } } }, "SRAgentSummary": { "edition": "proto2", "fields": { "timestamp": { "type": "uint64", "id": 1 }, "user": { "type": "User", "id": 2 }, "assigned_to_other_number": { "type": "uint64", "id": 10 }, "assigned_to_self_number": { "type": "uint64", "id": 20 }, "total_assignment_time": { "type": "uint64", "id": 30 }, "assigned_no_response_number": { "type": "uint64", "id": 40 }, "return_to_inbox_number": { "type": "uint64", "id": 50 }, "resolve_by_self_number": { "type": "uint64", "id": 60 }, "close_by_client_number": { "type": "uint64", "id": 70 }, "total_msg_file_number": { "type": "uint64", "id": 80 }, "total_resolution_time": { "type": "uint64", "id": 90 }, "active_sr_number": { "type": "uint64", "id": 100 }, "client_reopen_number": { "type": "uint64", "id": 110 }, "assigned_to_self_number_new": { "type": "uint64", "id": 120 }, "reassigned_number": { "type": "uint64", "id": 121 } } }, "GroupReport": { "edition": "proto2", "fields": { "members": { "rule": "repeated", "type": "GroupUser", "id": 10 }, "total_number": { "type": "uint64", "id": 20 } } }, "MoxoReport": { "edition": "proto2", "fields": { "client_engagement": { "rule": "repeated", "type": "Engagement", "id": 10 }, "internal_user_engagement": { "rule": "repeated", "type": "Engagement", "id": 20 }, "client_coverage": { "rule": "repeated", "type": "ClientCoverage", "id": 30 }, "social_engagement": { "rule": "repeated", "type": "SocialEngagement", "id": 40 }, "user_engagement": { "rule": "repeated", "type": "UserEngageMent", "id": 50 }, "user_activity_summary": { "rule": "repeated", "type": "UserEngageMent", "id": 60 }, "client_changes": { "rule": "repeated", "type": "ClientCoverage", "id": 70 }, "acd_summary": { "rule": "repeated", "type": "ACDSummary", "id": 100 }, "acd_agent_summary": { "rule": "repeated", "type": "ACDAgentSummary", "id": 110 }, "sr_summary": { "rule": "repeated", "type": "SRSummary", "id": 120 }, "sr_agent_summary": { "rule": "repeated", "type": "SRAgentSummary", "id": 130 }, "group_report": { "type": "GroupReport", "id": 140 } } }, "ClientRequests": { "edition": "proto2", "fields": { "requests": { "rule": "repeated", "type": "ClientRequest", "id": 10 } } }, "ClientRequest": { "edition": "proto2", "fields": { "type": { "type": "ClientRequestType", "id": 10 }, "original_type": { "type": "ClientRequestType", "id": 11 }, "sequence": { "type": "string", "id": 20 }, "params": { "rule": "repeated", "type": "ClientParam", "id": 30 }, "object": { "type": "CacheObject", "id": 100 }, "request_body": { "type": "string", "id": 110 }, "request_body_file_path": { "type": "string", "id": 120 }, "request_body_content_type": { "type": "string", "id": 130 }, "message": { "type": "CacheMessage", "id": 600 }, "user_activity": { "type": "UserActivityLog", "id": 800 }, "note": { "type": "string", "id": 1000 } } }, "ClientResponse": { "edition": "proto2", "fields": { "code": { "type": "ClientResponseCode", "id": 10 }, "detail_code": { "type": "ClientResponseDetailCode", "id": 12 }, "redirect_url": { "type": "string", "id": 13 }, "sequence": { "type": "string", "id": 20 }, "message": { "type": "string", "id": 30 }, "data": { "type": "string", "id": 31 }, "expires_in": { "type": "uint64", "id": 32 }, "timestamp": { "type": "uint64", "id": 33 }, "server": { "type": "string", "id": 34 }, "session_id": { "type": "string", "id": 35 }, "request_sequence": { "type": "string", "id": 36 }, "connection_id": { "type": "string", "id": 37 }, "zone": { "type": "string", "id": 38 }, "domain": { "type": "string", "id": 39 }, "is_truncated": { "type": "bool", "id": 40 }, "marker": { "type": "string", "id": 41 }, "next_marker": { "type": "string", "id": 42 }, "type": { "type": "ClientRequestType", "id": 50 }, "object": { "type": "CacheObject", "id": 100 }, "headers": { "rule": "repeated", "type": "HttpHeader", "id": 120 }, "params": { "rule": "repeated", "type": "ClientParam", "id": 121 }, "hits": { "type": "uint64", "id": 210 }, "start": { "type": "uint64", "id": 211 }, "size": { "type": "uint64", "id": 212 }, "recordings": { "rule": "repeated", "type": "ObjectRecording", "id": 800 }, "audios": { "rule": "repeated", "type": "AudioRecording", "id": 820 }, "videos": { "rule": "repeated", "type": "VideoRecording", "id": 840 }, "dss": { "rule": "repeated", "type": "DsRecording", "id": 830 }, "token": { "type": "PublicViewToken", "id": 900 }, "client_message": { "type": "CacheMessage", "id": 1000 }, "client_messages": { "rule": "repeated", "type": "CacheMessage", "id": 1001 }, "user_activities": { "rule": "repeated", "type": "UserActivityLog", "id": 1100 }, "report": { "type": "MoxoReport", "id": 1200 }, "group_capability": { "type": "GroupCapability", "id": 1300 } } }, "GroupCapability": { "edition": "proto2", "fields": { "has_sms_config": { "type": "bool", "id": 10 } } }, "ClientRequestParameter": { "edition": "proto2", "options": { "allow_alias": true }, "values": { "USER_REQUEST_REGISTER_NO_QS_BOARDS": 10, "USER_REQUEST_SALES_FORCE_CONNECT_URL": 13, "USER_REQUEST_READ_SET_COOKIE": 20, "USER_REQUEST_GET_ACCESS_TOKEN": 21, "USER_REQUEST_READ_TIMESTAMP": 26, "BOARD_REQUEST_READ_TIMESTAMP": 26, "SERVER_OBJECT_READ_TIMESTAMP": 26, "GROUP_REQUEST_READ_USAGE_TIMESTAMP": 26, "GROUP_REQUEST_READ_TASKS_TIMESTAMP": 26, "USER_REQUEST_READ_COUNT": 27, "BOARD_REQUEST_READ_COUNT": 27, "USER_REQUEST_READ_TIMESTAMP_FROM": 28, "GROUP_REQUEST_READ_TIMESTAMP_FROM": 28, "USER_REQUEST_READ_TIMESTAMP_TO": 29, "GROUP_REQUEST_READ_TIMESTAMP_TO": 29, "USER_REQUEST_READ_TIMESTAMP_OFFSET": 31, "GROUP_REQUEST_READ_TIMESTAMP_OFFSET": 31, "GROUP_REQUEST_EXPORT_REPORT_HEADERS": 32, "GROUP_REQUEST_EXPORT_DATE_LOCALE": 33, "USER_REQUEST_REPORT_RUN_BY": 35, "OUTPUT_FILTER_STRING": 30, "LOGIN_OUTPUT_USER_FILTER_STRING": 33, "USER_REQUEST_LOGIN_REMEMBER": 40, "USER_REQUEST_LOGIN_OUTPUT_BASIC": 41, "USER_REQUEST_LOGIN_LOCAL_USER_EXPECTED": 42, "GROUP_REQUEST_LOCAL_USER_EXPECTED": 42, "USER_REQUEST_LOGIN_PARTNER_ADMIN_EXPECTED": 43, "USER_REQUEST_LOGOUT_KEEP_DEVICE_TOKEN": 44, "USER_REQUEST_REMEMBER_DEVICE": 45, "USER_REQUEST_RESET_PASSWORD_PARTNER_ADMIN_EXPECTED": 46, "USER_REQUEST_PASSWORD_ENCODED": 49, "USER_REQUEST_KEEP_TOKEN": 50, "USER_REQUEST_ALL_BOARDS": 55, "USER_REQUEST_OLD_BOARDS": 56, "USER_REQUEST_FEEDBACK_MESSAGE": 500, "USER_REQUEST_FEEDBACK_SUBJECT": 501, "USER_SUBSCRIBE_FILTER_MEET": 510, "USER_REQUEST_FILTER_ACD": 511, "USER_REQUEST_FILTER_SERVICE_REQUEST": 512, "USER_REQUEST_SSO_REDIRECT_URL": 630, "USER_REQUEST_CODE_TO_REGISTER": 640, "USER_REQUEST_CODE_TO_RESET_PASSWORD": 641, "USER_REQUEST_HTML_URL": 650, "USER_REQUEST_EMAIL_CODE": 660, "USER_REQUEST_SMS_CODE": 661, "USER_REQUEST_APPLE_JWT": 662, "USER_REQUEST_GOOGLE_JWT": 663, "USER_REQUEST_DEVICE_TOKEN_VENDOR": 2012, "USER_REQUEST_DEVICE_TOKEN_VENDOR_EXT": 1990, "USER_REQUEST_CONTACT_INVITE_TOKEN": 2011, "GROUP_REQUEST_INVITE_MESSAGE": 310, "GROUP_REQUEST_INVITE_TOKEN": 2011, "GROUP_REQUEST_USER_TOKEN": 2011, "PARTNER_REQUEST_INVITE_TOKEN": 2011, "BOARD_REQUEST_EXTERNAL_ID": 1990, "BOARD_REQUEST_CREATE_AS_TEMP": 2000, "BOARD_REQUEST_CREATE_AS_DEFAULT": 2001, "BOARD_REQUEST_CLEAR_VIEW_TOKEN": 2002, "BOARD_REQUEST_NEW_BOARD": 2003, "BOARD_REQUEST_CREATE_AS_SUBSCRIPTION_CHANNEL": 2004, "BOARD_REQUEST_CREATE_SIGNATURE_AS_TEMPLATE": 2005, "BOARD_REQUEST_SUBMIT_SIGNATURE_KEEP_STATUS_UNCHANGED": 2006, "BOARD_REQUEST_READ_UPLOAD_SEQUENCE": 2010, "BOARD_REQUEST_VIEW_TOKEN": 2011, "SESSION_REQUEST_ROSTER_TOKEN": 2011, "BOARD_REQUEST_SEARCH_TEXT": 2012, "BOARD_REQUEST_SEARCH_START": 2013, "BOARD_REQUEST_SEARCH_SIZE": 2014, "BOARD_REQUEST_SEARCH_CREATOR": 2023, "BOARD_REQUEST_SEARCH_ID": 2024, "BOARD_REQUEST_SEARCH_BOARD_TYPE": 2027, "BOARD_REQUEST_KEEP_UNREAD_FEED_TIMESTAMP": 2025, "GROUP_REQUEST_SEARCH_TEXT": 2012, "GROUP_REQUEST_SEARCH_START": 2013, "GROUP_REQUEST_SEARCH_SIZE": 2014, "GROUP_REQUEST_SEARCH_PAGE_NUMBER": 2015, "GROUP_REQUEST_SEARCH_SORT_FIELD": 2016, "GROUP_REQUEST_SEARCH_SORT_METHOD": 2017, "PARTNER_REQUEST_SEARCH_TEXT": 2012, "PARTNER_REQUEST_SEARCH_START": 2013, "PARTNER_REQUEST_SEARCH_SIZE": 2014, "GROUP_REQUEST_ACTION_GROUP": 2031, "GROUP_REQUEST_ACTION_TYPE": 2032, "GROUP_REQUEST_SEARCH_MEMBER": 2033, "GROUP_REQUEST_SUPPRESS_STATISTICS": 2034, "USER_REQUEST_SEARCH_TEXT": 2012, "USER_REQUEST_SEARCH_START": 2013, "USER_REQUEST_SEARCH_SIZE": 2014, "USER_REQUEST_SEARCH_PAGE_NUMBER": 2015, "USER_REQUEST_SEARCH_CREATOR": 2023, "USER_REQUEST_SEARCH_ID": 2024, "USER_REQUEST_SEARCH_SORT_BY_TIME": 2025, "USER_REQUEST_SEARCH_DUE_FROM": 2101, "USER_REQUEST_SEARCH_DUE_TO": 2102, "USER_REQUEST_SEARCH_EXCLUDE_CREATOR": 2103, "USER_REQUEST_SEARCH_CREATED_OR_ASSIGNED": 2104, "USER_REQUEST_SEARCH_CREATED_OR_SUBMITTED": 2105, "USER_REQUEST_SEARCH_INCLUDE_CANCELED": 2106, "USER_REQUEST_SEARCH_TIMELINE": 2107, "USER_REQUEST_SEARCH_ARCHIVED": 2108, "USER_REQUEST_SEARCH_INCLUDE_EDITING": 2109, "BOARD_REQUEST_READ_FEEDS_INDEXED": 2015, "BOARD_REQUEST_READ_FEEDS_ORIGINAL": 2017, "BOARD_REQUEST_READ_WITH_DETAIL": 2016, "BOARD_REQUEST_READ_WITHOUT_MEMBERS": 2018, "GROUP_REQUEST_READ_GROUP_MEMBER": 2019, "GROUP_REQEUEST_READ_MEMBER_PRESENCE": 2020, "BOARD_REQUEST_COPY_PAGES_WITH_COMMENTS": 2021, "BOARD_REQUEST_COPY_PAGES_WITHOUT_ANNOTATIONS": 2022, "BOARD_REQUEST_COPY_TODOS_WITH_COMMENTS": 2026, "BOARD_REQUEST_COPY_SIGNATURE_KEEP_CREATOR": 2028, "BOARD_REQUEST_COPY_TRANSACTION_KEEP_CREATOR": 2028, "BOARD_REQUEST_COPY_WITH_TOTAL_USED_COUNT": 2035, "BOARD_REQUEST_COPY_WITH_LAST_MODIFIED_TIME": 2036, "BOARD_REQUEST_DUPLICATE_WITH_SIGNATURES": 2029, "BOARD_REQUEST_DUPLICATE_WITH_TRANSACTIONS": 2037, "RESOURCE_REQUEST_RESOURCE_TYPE": 2040, "RESOURCE_REQUEST_RESOURCE_ROTATION": 2045, "RESOURCE_REQUEST_RESOURCE_WIDTH": 2047, "RESOURCE_REQUEST_RESOURCE_HEIGHT": 2048, "BOARD_REQUEST_RELATIVE_ORDER_NUMBER": 2049, "RESOURCE_UPLOAD_RESOURCE_MEDIA_LENGTH": 2042, "RESOURCE_UPLOAD_RESOURCE_DESCRIPTION": 2043, "RESOURCE_DOWNLOAD_RESOURCE_CONTENT_DISPOSITION": 2050, "RESOURCE_UPLOAD_RESOURCE_URL": 2046, "RESOURCE_UPLOAD_RESOURCE_AUTHORIZATION": 2051, "RESOURCE_UPLOAD_RESOURCE_DESTINATION_FOLDER": 2052, "RESOURCE_UPLOAD_RESOURCE_CONTENT_TYPE": 2053, "BOARD_REQUEST_INVITEE_EMAIL": 2059, "BOARD_REQUEST_INVITE_MESSAGE": 2060, "BOARD_REQUEST_PUSH_NOTIFICATION_MESSAGE": 2061, "BOARD_REQUEST_PUSH_NOTIFICATION_OFF": 2062, "GROUP_REQUEST_PUSH_NOTIFICATION_OFF": 2062, "BOARD_REQUEST_EMAIL_OFF": 2063, "USER_REQUEST_EMAIL_OFF": 2063, "GROUP_REQUEST_EMAIL_OFF": 2063, "BOARD_REQUEST_SMS_OFF": 2064, "GROUP_REQUEST_SMS_OFF": 2064, "BOARD_REQUEST_INVITE_ADD_DIRECTLY": 2065, "BOARD_REQUEST_SIGNEE_MESSAGE": 2066, "GROUP_REQUEST_RESEND_EMAIL": 2067, "GROUP_REQUEST_RESEND_SMS": 2068, "BOARD_REQUEST_INVITE_WITH_INFO_FROM": 2069, "BOARD_REQUEST_DELETE_FOLDER_RECURSIVELY": 2070, "BOARD_REQUEST_UPDATE_FILE_COVER": 2075, "BOARD_REQUEST_CREATE_NEW_FILE": 2080, "BOARD_REQUEST_NEW_FILE_NAME_SEQUENCE": 2084, "BOARD_REQUEST_NEW_FILE_NAME": 2085, "BOARD_REQUEST_ACTOR_NAME": 2086, "BOARD_REQUEST_ACTOR_PICTURE_URL": 2087, "BOARD_REQUEST_CUSTOM_INFO": 2090, "BOARD_REQUEST_CUSTOM_INFO_SEQUENCE": 2091, "BOARD_REQUEST_ORDER_NUMBER_SEQUENCE": 2092, "BOARD_REQUEST_SOCIAL_CUSTOM_INFO": 2095, "BOARD_REQUEST_MEMBER_VIEW_TOKEN": 2093, "BOARD_REQUEST_RESET_MEMBER_STATUS": 2094, "BOARD_REQUEST_AUTO_COMPLETE_TRANSACTION": 2100, "BOARD_REQUEST_VIEW_TOKEN_TO_UPDATE_BOARD": 2111, "BOARD_REQUEST_DUPLICATE_WITH_WORKFLOW": 2112, "BOARD_REQUEST_DUPLICATE_AS_TEMPLATE": 2113, "BOARD_REQUEST_CREATE_FLOW_VARIABLE": 2114, "BOARD_REQUEST_NOT_FILTER_FLOW_VARIABLE": 2115, "BOARD_REQUEST_COPY_KEEP_FLOW_STEP_UUID": 2116, "BOARD_REQUEST_COPY_BOARD_THUMBNAIL": 2117, "BOARD_REQUEST_COPY_BOARD_BANNER": 2118, "BOARD_REQUEST_COPY_SKIP_ASSIGNEE_CHECKING": 2119, "BOARD_REQUEST_COPY_WITH_COMPLETED_TODOS": 2121, "BOARD_REQUEST_DUPLICATE_MILESTONE_FROM": 2122, "BOARD_REQUEST_DUPLICATE_MILESTONE_TO": 2123, "BOARD_REQUEST_COPY_BOARD_PROPERTY": 2124, "BOARD_REQUEST_UPDATE_WORKFLOW_WITHOUT_LAST_MODIFIED_TIME": 2125, "BOARD_REQUEST_REPLACE_TRANSACTION_WITH_STEPS": 2126, "BOARD_REQUEST_COPY_WITH_WORKFLOW": 2127, "SESSION_REQUEST_INVITE_MESSAGE": 2140, "SESSION_REQUEST_RECORDING": 2141, "SESSION_REQUEST_JOIN_INVISIBLE": 2142, "GRAB_PRESENTER_WHEN_NOT_SHARING": 2143, "SESSION_REQUEST_SESSION_KEY": 2144, "BOARD_REQUEST_SUPPRESS_FEED": 2150, "BOARD_REQUEST_SUPPRESS_JOB": 2151, "BOARD_REQUEST_SUPPRESS_USER_ACTIVITY": 2152, "SUBSCRIBE_REQUEST_NOHANG": 2160, "BOARD_REQUEST_DOWNLOAD_BOARD_NO_WAIT": 2160, "SUBSCRIBE_REQUEST_NO_GROUP_MEMBERS": 2161, "SUBSCRIBE_REQUEST_NO_USER_BOARDS": 2162, "SUBSCRIBE_REQUEST_RESPONSE_FILTER_OFF": 2163, "CLIENT_CONNECTION_PUSH_NOTIFICATION_OFF": 2170, "CLIENT_CONNECTION_SUBSCRIPTION_DATA_OFF": 2171, "BOARD_REQUEST_FILE_REPLY": 2190, "BOARD_REQUEST_SET_LAST_MODIFIED_TIME": 2191, "BOARD_REQUEST_REASSIGN": 2192, "BOARD_REQUEST_REASSIGN_FROM_GROUP_ID": 2193, "BOARD_REQUEST_REASSIGN_TO_GROUP_ID": 2194, "GROUP_REQUEST_READ_MEMBER_INTERNAL": 2200, "GROUP_REQUEST_READ_MEMBER_LOCAL": 2201, "GROUP_REQUEST_READ_GROUP_INVITED": 2210, "GROUP_REQUEST_READ_GROUP_ADMIN": 2211, "GROUP_REQUEST_READ_FILTER_ROLE": 2215, "GROUP_REQUEST_READ_INCLUDE_RELATION_USER": 2216, "GROUP_REQUEST_READ_INCLUDE_SUGGESTED_USER": 2222, "GROUP_REQUEST_READ_DEACTIVED_USER": 2217, "GROUP_REQUEST_READ_DELETED_USER": 2202, "GROUP_REQUEST_READ_MEMBER_OUTPUT_COUNT": 2203, "GROUP_REQUEST_READ_MEMBER_OUTPUT_CLIENT_TEAM": 2204, "GROUP_REQUEST_READ_MEMBER_OUTPUT_TEAM": 2205, "GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_INTERNAL": 2218, "GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_LOCAL": 2219, "GROUP_REQUEST_OUTPUT_USER_ACTIVITIES": 2220, "GROUP_REQUEST_READ_MANAGEMENT_TEAM_MEMBERS": 2221, "GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_REPORT": 2222, "GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_WITH_CLIENT_TEAMS": 2223, "GROUP_REQUEST_READ_MEMBER_FOR_AUDIT": 2230, "GROUP_REQUEST_EXPORT_REPORT_FORMATTED_RUN_ON": 2240, "GROUP_REQUEST_EXPORT_REPORT_FORMATTED_FROM": 2250, "GROUP_REQUEST_EXPORT_REPORT_FORMATTED_TO": 2260, "USER_REQUEST_SEARCH_FILTER_MIN": 2309, "USER_REQUEST_SEARCH_BOARD_NAME": 2310, "USER_REQUEST_SEARCH_BOARD_USER": 2311, "USER_REQUEST_SEARCH_BOARD_COMMENT": 2312, "USER_REQUEST_SEARCH_BOARD_FILE": 2313, "USER_REQUEST_SEARCH_BOARD_FOLDER": 2314, "USER_REQUEST_SEARCH_BOARD_PAGE": 2315, "USER_REQUEST_SEARCH_BOARD_PAGE_COMMENT": 2316, "USER_REQUEST_SEARCH_BOARD_TODO": 2317, "USER_REQUEST_SEARCH_BOARD_TODO_COMMENT": 2318, "USER_REQUEST_SEARCH_BOARD_SESSION": 2319, "USER_REQUEST_SEARCH_BOARD_SIGNATURE": 2320, "USER_REQUEST_SEARCH_BOARD_SIGNATURE_PAGE": 2321, "USER_REQUEST_SEARCH_BOARD_TRANSACTION": 2322, "USER_REQUEST_SEARCH_BOARD_MENTION_COMMENT": 2323, "USER_REQUEST_SEARCH_BOARD_APPROVAL": 2324, "USER_REQUEST_SEARCH_BOARD_ACKNOWLEDGE": 2325, "USER_REQUEST_SEARCH_BOARD_FILE_REQUEST": 2326, "USER_REQUEST_SEARCH_BOARD_MEET_REQUEST": 2327, "USER_REQUEST_SEARCH_BOARD_FORM_REQUEST": 2328, "USER_REQUEST_SEARCH_BOARD_DOCUSIGN": 2329, "USER_REQUEST_SEARCH_BOARD_WEBHOOK": 2330, "USER_REQUEST_SEARCH_BOARD_LAUNCH_WEB_APP": 2331, "USER_REQUEST_SEARCH_BOARD_INTEGRATION": 2332, "USER_REQUEST_SEARCH_BOARD_TODO_TRANSACTION": 2333, "USER_REQUEST_SEARCH_BOARD_WORKFLOW": 2334, "USER_REQUEST_SEARCH_BOARD_DECISION": 2335, "USER_REQUEST_SEARCH_BOARD_AWAIT": 2336, "USER_REQUEST_SEARCH_BOARD_PDF_FORM": 2337, "USER_REQUEST_SEARCH_FILTER_MAX": 2349, "SERVER_REQUEST_INDEX_USER_INDEX": 2357, "SERVER_REQUEST_INDEX_ORG_INDEX": 2358, "SERVER_REQUEST_INDEX_FILTER": 2359, "SERVER_REQUEST_INDEX_BOARD_NAME": 2360, "SERVER_REQUEST_INDEX_BOARD_USER": 2361, "SERVER_REQUEST_INDEX_BOARD_COMMENT": 2362, "SERVER_REQUEST_INDEX_BOARD_FILE": 2363, "SERVER_REQUEST_INDEX_BOARD_FOLDER": 2364, "SERVER_REQUEST_INDEX_BOARD_PAGE": 2365, "SERVER_REQUEST_INDEX_BOARD_PAGE_COMMENT": 2366, "SERVER_REQUEST_INDEX_BOARD_TODO": 2367, "SERVER_REQUEST_INDEX_BOARD_TODO_COMMENT": 2368, "SERVER_REQUEST_INDEX_BOARD_SESSION": 2369, "SERVER_REQUEST_INDEX_BOARD_SIGNATURE": 2370, "SERVER_REQUEST_INDEX_BOARD_SIGNATURE_PAGE": 2371, "SERVER_REQUEST_INDEX_BOARD_TRANSACTION": 2372, "SERVER_REQUEST_INDEX_BOARD_ACTIONITEM": 2373, "SERVER_REQUEST_INDEX_GLOBAL_USER_ORG_MAPPING": 2380, "USER_REQUEST_READ_START": 2400, "USER_REQUEST_READ_SIZE": 2401, "USER_REQUEST_READ_PAGE_NUMBER": 2402, "USER_REQUEST_READ_BEFORE": 2403, "USER_REQUEST_READ_AFTER": 2404, "USER_REQUEST_READ_TIMELINE": 2500, "USER_REQUEST_READ_SR_OPEN": 2501, "USER_REQUEST_READ_SR_COMPLETE": 2502, "USER_REQUEST_READ_ARCHIVED": 2503, "USER_REQUEST_READ_SUBSCRIPTION_CHANNEL": 2504, "USER_REQUEST_READ_INBOX": 2505, "USER_REQUEST_LOOKUP_P2P_BOARD": 2510, "USER_REQUEST_READ_UNREAD_BOARD": 2410, "USER_REQUEST_READ_CONVERSATION_BOARD": 2411, "USER_REQUEST_READ_PROJECT_BOARD": 2412, "USER_REQUEST_READ_SOCIAL_BOARD": 2413, "USER_REQUEST_READ_SCHEDULE_BOARD": 2414, "USER_REQUEST_USER_AGENT_EXT": 2421, "BOARD_REQUEST_READ_SIZE_BEFORE": 3001, "BOARD_REQUEST_READ_SIZE_AFTER": 3002, "BOARD_REQUEST_READ_FIRST_SUB_FOLDER_FILE": 3003, "BOARD_REQUEST_READ_POSITION_COMMENT_FEED": 3004, "BOARD_REQUEST_EDITOR_TYPE_INTERNAL_ONLY": 3010, "BOARD_REQUEST_REOPEN": 3020, "BOARD_REQUEST_UPDATE_FEED": 3021, "BOARD_REQUEST_KEEP_CURRENT_STATUS": 3022, "BOARD_REQUEST_REOPEN_FEED": 3023, "BOARD_REQUEST_REOPEN_EVENT": 3024, "BOARD_REQUEST_READY_FEED": 3025, "BOARD_REQUEST_RESET_REVIEWER_STATUS": 3026, "BOARD_REQUEST_CREATE_FEED": 3027, "BOARD_REQUEST_CREATE_NO_OWNER": 3036, "BOARD_REQUEST_START_FLOW_WITHOUT_OWNER": 3037, "BOARD_REQUEST_RESET_CODE": 3041, "BOARD_REQUEST_VIEW_TOKEN_CODE": 3042, "BOARD_REQUEST_MEET_REQUEST": 3043, "BOARD_REQUEST_SET_RSVP_ACCEPTED": 3044, "BOARD_REQUEST_CREATE_ATTACHMENT_FOLDER": 3045, "BOARD_REQUEST_SET_ASSIGNEE_TO_OWNER": 3049, "BOARD_REQUEST_COPY_WITH_COMPLETED_TRANSACTIONS": 3050, "BOARD_REQUEST_FILTER": 3051, "BOARD_REQUEST_FILTER_INCLUDE_TRANSACTION_TODO": 3052, "BOARD_REQUEST_FILTER_INCLUDE_CUSTOM_FOLDER": 3053, "BOARD_REQUEST_IS_INSTANT_MEET": 3066, "SERVER_PROBE_SERVER_NAME": 4100, "SERVER_OBJECT_READ_SERVER_NAME": 4100, "SERVER_OBJECT_READ_LOG_SERVER_NAME": 4105, "SERVER_OBJECT_FORWARD_REQUEST_TYPE": 4108, "SERVER_OBJECT_GROUP_ID": 4109, "SERVER_OBJECT_READ_ID": 4110, "SERVER_OBJECT_READ_TYPE": 4111, "SERVER_OBJECT_READ_QUERY_STRING": 4112, "SERVER_OBJECT_READ_SESSION_KEY": 4113, "SERVER_OBJECT_READ_USER_WITH_EMAIL": 4114, "SERVER_OBJECT_READ_DEVICE_TOKEN_MAPPING": 4115, "SERVER_OBJECT_READ_WITH_DETAIL": 2050, "SERVER_OBJECT_WRITE_ID": 4120, "SERVER_OBJECT_HASH_ID": 4125, "SERVER_RESOURCE_DOWNLOAD_KEY": 4130, "SERVER_RESOURCE_UPLOAD_KEY": 4130, "SERVER_RESOURCE_DOWNLOAD_BUCKET": 4131, "SERVER_RESOURCE_UPLOAD_BUCKET": 4131, "SERVER_RESOURCE_DOWNLOAD_OBJECT_ID": 4132, "SERVER_RESOURCE_UPLOAD_OBJECT_ID": 4132, "SERVER_PREVIEW_RESOURCE_URL": 4133, "SERVER_PREVIEW_RESOURCE_HASH": 4134, "SERVER_PREVIEW_RESOURCE_EXTENSION": 4135, "SERVER_REDO_JOB_ID": 4144, "SERVER_MIGRATE_ZONE": 4145, "SERVER_JOB_DOMAIN": 4146, "SERVER_JOB_USER_ID": 4147, "SERVER_JOB_FORWARDED_URI": 4148, "SERVER_OBJECT_READ_PARAM_BEFORE": 4150, "SERVER_OBJECT_READ_PARAM_AFTER": 4151, "SERVER_OBJECT_READ_BOARD_FEEDS": 4160, "SERVER_OBJECT_READ_BOARD_FEEDS_PAGE": 4161, "SERVER_OBJECT_READ_BOARD_THREAD": 4162, "SERVER_OBJECT_READ_BOARD_FOLDER": 4163, "SERVER_OBJECT_READ_BOARD_FILE": 4164, "SERVER_UPLOAD_CRASH_REPORT_NAME": 4200, "SERVER_DOWNLOAD_CRASH_REPORT_NAME": 4220, "SERVER_REQUEST_SERVER_TOKEN": 4230, "SERVER_REQUEST_EXPIRES_AFTER": 4231, "SERVER_REQUEST_SERVER_DOMAIN": 4232, "GROUP_REQUEST_CANCEL_MESSAGE": 7030, "GROUP_REQUEST_INVITE_PREVIEW": 7105, "GROUP_REQUEST_INVITE_UPDATE_EXISTING": 7106, "GROUP_REQUEST_STRIPE_SUBSCRIBE_TOKEN": 7620, "GROUP_REQUEST_STRIPE_COUPON_CODE": 7650, "GROUP_REQUEST_READ_MEET": 7660, "GROUP_REQUEST_DELETE_BOARD": 7670, "GROUP_REQUEST_READ_RELATED_USER_RELATION": 7680, "GROUP_REQUEST_READ_RELATED_USER_FLOW_TEMPLATE": 7685, "GROUP_REQUEST_READ_RELATED_USER_CONTENT_LIBRARY": 7686, "GROUP_REQUEST_READ_RELATED_USER_FLOW_TEMPLATE_LIBRARY": 7687, "GROUP_REQUEST_NO_RELATION_BOARD": 7681, "GROUP_REQUEST_CREATE_RELATION_BOARD": 7682, "GROUP_REQUEST_CREATE_PENDING_RELATION": 7683, "GROUP_REQUEST_WELCOME_MESSAGE": 7684, "GROUP_REQUEST_JWT_EXPIRES_AFTER": 4231, "GROUP_REQUEST_JWT_PAYLOAD": 2012, "GROUP_REQUEST_BOX_PAYLOAD": 7690, "GROUP_REQUEST_REFRESH_DEFAULT_MEET_PASSWORD": 7700, "GROUP_REQUEST_CREATE_CREATOR_AS_GROUP_OWNER": 7800, "GROUP_REQUEST_READ_CONTENT_LIBRARY_INCLUDE_DEFAULT": 7900, "CLIENT_PARAM_REVISION": 8001, "CLIENT_PARAM_JWT": 8100, "CLIENT_PARAM_FROM_BOARD_ID": 8111, "CLIENT_PARAM_SET_USER_ID_TO_VARIABLE": 8112, "CLIENT_PARAM_CHECK_BOARD_ACCESS": 8113, "CLIENT_PARAM_ORIGINAL_BOARD_ID": 8114, "CLIENT_PARAM_PARENT_BOARD_ID": 8115, "CLIENT_PARAM_KEEP_BOARD_OWNER": 8116, "CLIENT_PARAM_EMAIL_SMS_SKIP_USER_ID": 8117, "CLIENT_PARAM_CUSTOM_DATA": 8118, "CLIENT_PARAM_REFERENCE_TYPE": 8119, "CLIENT_PARAM_BROADCAST_TO_USER_ID": 8124, "PRESENCE_PARAM_USER_ONLINE": 9001, "PRESENCE_PARAM_USER_CLIENT": 9002, "OUTPUT_INCLUDE_STRING": 9030, "BOARD_REQUEST_ACD_SESSION_END_FEED": 9040, "BOARD_REQUEST_MEETING_TRANSCRIPTION": 9050, "BOARD_REQUEST_MEETING_SUMMARY": 9060, "CLIENT_PARAM_TIMELINE_BOTTOM_FEED_TIMESTAMP": 20001, "USER_REQUEST_READ_TIMEZONE": 30010, "GROUP_REQUEST_REPORT_NAME": 30020, "USER_REQUEST_INCL_WORKSPACE_TAGS": 30030, "USER_REQUEST_INCL_ADDITIONAL_COLUMNS": 30040 } }, "ClientParam": { "edition": "proto2", "fields": { "name": { "type": "ClientRequestParameter", "id": 10 }, "string_value": { "type": "string", "id": 20 }, "uint64_value": { "type": "uint64", "id": 30 } } }, "ActionPageSwitch": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "page_sequence": { "type": "uint64", "id": 30 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 }, "revision": { "type": "uint64", "id": 1020 }, "assignments": { "type": "string", "id": 1200 } } }, "ActionLaserPointer": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "is_deleted": { "type": "bool", "id": 20 }, "page_sequence": { "type": "uint64", "id": 30 }, "px": { "type": "uint64", "id": 40 }, "py": { "type": "uint64", "id": 50 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 }, "revision": { "type": "uint64", "id": 1020 } } }, "ActionUserPointer": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "is_deleted": { "type": "bool", "id": 20 }, "page_sequence": { "type": "uint64", "id": 30 }, "px": { "type": "uint64", "id": 40 }, "py": { "type": "uint64", "id": 50 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 }, "revision": { "type": "uint64", "id": 1020 }, "sequence": { "type": "uint64", "id": 1030 } } }, "AudioWebrtcChannel": { "edition": "proto2", "fields": { "channel_id": { "type": "uint64", "id": 10 }, "webrtc_offer": { "type": "string", "id": 20 }, "webrtc_answer": { "type": "string", "id": 30 } } }, "AudioStatusRequest": { "edition": "proto2", "values": { "AUDIO_STATUS_REQUEST_NONE": 0, "AUDIO_STATUS_REQUEST_MUTE": 10, "AUDIO_STATUS_REQUEST_UNMUTE": 20, "AUDIO_STATUS_REQUEST_LEAVE_TELEPHONY": 30 } }, "AudioStatus": { "edition": "proto2", "fields": { "is_in_session": { "type": "bool", "id": 10 }, "is_mute": { "type": "bool", "id": 20 }, "ssrc": { "type": "uint64", "id": 30 }, "channels": { "rule": "repeated", "type": "AudioWebrtcChannel", "id": 40 } } }, "TelephoneStatus": { "edition": "proto2", "fields": { "is_in_session": { "type": "bool", "id": 10 }, "is_mute": { "type": "bool", "id": 20 }, "is_pure_telephone_user": { "type": "bool", "id": 30 }, "ssrc": { "type": "uint64", "id": 40 }, "twilio_param": { "type": "TwilioRequestParam", "id": 100 }, "join_timestamp": { "type": "uint64", "id": 110 }, "leave_timestamp": { "type": "uint64", "id": 120 } } }, "RosterVideoStatus": { "edition": "proto2", "fields": { "is_in_session": { "type": "bool", "id": 10 }, "is_broadcast": { "type": "bool", "id": 20 } } }, "BoardWaitingUserStatus": { "edition": "proto2", "values": { "BOARD_WAITING_USER_STATUS_NONE": 0, "BOARD_WAITING_USER_STATUS_PENDING": 10, "BOARD_WAITING_USER_STATUS_APPROVED": 20, "BOARD_WAITING_USER_STATUS_DENIED": 30, "BOARD_WAITING_USER_STATUS_BLOCKED": 40 } }, "ActionUserRoster": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "requestor_id": { "type": "string", "id": 11 }, "user": { "type": "User", "id": 30, "options": { "lazy": true } }, "group": { "type": "Group", "id": 35, "options": { "lazy": true } }, "audio_status": { "type": "AudioStatus", "id": 40 }, "audio_status_request": { "type": "AudioStatusRequest", "id": 41 }, "waiting_user_status": { "type": "BoardWaitingUserStatus", "id": 45 }, "is_host": { "type": "bool", "id": 50 }, "is_presenter": { "type": "bool", "id": 60 }, "is_invisible": { "type": "bool", "id": 70 }, "is_from_team": { "type": "bool", "id": 80 }, "participant_number": { "type": "uint64", "id": 100 }, "telephone_status": { "type": "TelephoneStatus", "id": 110 }, "video_status": { "type": "RosterVideoStatus", "id": 200 }, "roster_tag": { "type": "string", "id": 220 }, "is_deleted": { "type": "bool", "id": 20 }, "keep_deleted": { "type": "bool", "id": 121 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 }, "revision": { "type": "uint64", "id": 1020 }, "sequence": { "type": "uint64", "id": 1030 }, "client_uuid": { "type": "string", "id": 1040 }, "assignments": { "type": "string", "id": 1050 } } }, "ActionUserRosterEventItem": { "edition": "proto2", "fields": { "data": { "type": "string", "id": 10 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "ActionUserRosterEvent": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "items": { "rule": "repeated", "type": "ActionUserRosterEventItem", "id": 20 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "ActionUserRosterKeepAliveInfo": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "keepalive_timestamp": { "type": "uint64", "id": 21 }, "is_deleted": { "type": "bool", "id": 20 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 }, "revision": { "type": "uint64", "id": 1020 }, "sequence": { "type": "uint64", "id": 1030 } } }, "VideoStatus": { "edition": "proto2", "values": { "VIDEO_STOPPED": 0, "VIDEO_PLAYING": 10, "VIDEO_PAUSED": 20 } }, "ActionVideoState": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "is_deleted": { "type": "bool", "id": 20 }, "video_sequence": { "type": "uint64", "id": 25 }, "page_sequence": { "type": "uint64", "id": 30 }, "status": { "type": "VideoStatus", "id": 40 }, "action_timestamp": { "type": "uint64", "id": 50 }, "revision": { "type": "uint64", "id": 1020 }, "sequence": { "type": "uint64", "id": 1030 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 200 } } }, "AudioEdgeServer": { "edition": "proto2", "fields": { "availability_zone": { "type": "string", "id": 5 }, "server_url": { "type": "string", "id": 6 }, "server_addr": { "type": "string", "id": 10 }, "server_name": { "type": "string", "id": 15 }, "port": { "type": "uint64", "id": 20 }, "tcp_port": { "type": "uint64", "id": 21 } } }, "AudioConf": { "edition": "proto2", "fields": { "availability_zone": { "type": "string", "id": 5 }, "server_url": { "type": "string", "id": 6 }, "server_addr": { "type": "string", "id": 10 }, "server_name": { "type": "string", "id": 15 }, "geo_domain": { "type": "string", "id": 16 }, "port": { "type": "uint64", "id": 20 }, "tcp_port": { "type": "uint64", "id": 21 }, "fingerprint": { "type": "string", "id": 22 }, "edge_servers": { "rule": "repeated", "type": "AudioEdgeServer", "id": 25 }, "conf_id": { "type": "string", "id": 30 }, "token": { "type": "string", "id": 40 }, "is_deleted": { "type": "bool", "id": 50 }, "client_uuid": { "type": "string", "id": 101 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 }, "revision": { "type": "uint64", "id": 1020 }, "sequence": { "type": "uint64", "id": 1030 } } }, "TelephonyConf": { "edition": "proto2", "fields": { "telephony_domain_id": { "type": "string", "id": 90 }, "numbers": { "rule": "repeated", "type": "TelephoneNumber", "id": 100 }, "is_deleted": { "type": "bool", "id": 50 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 }, "revision": { "type": "uint64", "id": 1020 }, "sequence": { "type": "uint64", "id": 1030 } } }, "SessionStatus": { "edition": "proto2", "values": { "SESSION_ENDED": 0, "SESSION_SCHEDULED": 10, "SESSION_STARTED": 20, "SESSION_EXPIRED": 100 } }, "ExtCalType": { "edition": "proto2", "values": { "CAL_TYPE_OUTLOOK": 0, "CAL_TYPE_GOOGLE": 10 } }, "VendorServiceType": { "edition": "proto2", "values": { "SERVICE_DEFAULT": 0, "SERVICE_ZOOM": 1, "SERVICE_OFFLINE": 2, "SERVICE_MSTEAM": 3, "SERVICE_OTHER": 20 } }, "ActionObject": { "edition": "proto2", "fields": { "board_id": { "type": "string", "id": 10 }, "original_board_id": { "type": "string", "id": 11 }, "parent_board_id": { "type": "string", "id": 12 }, "session_key": { "type": "string", "id": 20 }, "session_password": { "type": "string", "id": 21 }, "password_protected": { "type": "bool", "id": 22 }, "ext_cal_event_id": { "type": "string", "id": 23 }, "ext_cal_type": { "type": "ExtCalType", "id": 24 }, "topic": { "type": "string", "id": 25 }, "agenda": { "type": "string", "id": 26 }, "isnote": { "type": "bool", "id": 27 }, "is_private": { "type": "bool", "id": 28 }, "page_switch": { "type": "ActionPageSwitch", "id": 30 }, "laser_pointer": { "type": "ActionLaserPointer", "id": 40 }, "user_pointer": { "rule": "repeated", "type": "ActionUserPointer", "id": 50 }, "team_roster": { "rule": "repeated", "type": "ActionUserRoster", "id": 55 }, "user_roster": { "rule": "repeated", "type": "ActionUserRoster", "id": 60 }, "keepalive_info": { "rule": "repeated", "type": "ActionUserRosterKeepAliveInfo", "id": 61 }, "events": { "rule": "repeated", "type": "ActionUserRosterEvent", "id": 62 }, "total_rosters": { "type": "uint64", "id": 65 }, "video_state": { "rule": "repeated", "type": "ActionVideoState", "id": 70 }, "audio_conf": { "type": "AudioConf", "id": 80 }, "zone": { "type": "string", "id": 83 }, "is_expired": { "type": "bool", "id": 90 }, "is_locked": { "type": "bool", "id": 100 }, "last_modified_time": { "type": "uint64", "id": 103 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "last_deletion_revision": { "type": "uint64", "id": 121 }, "local_revision": { "type": "uint64", "id": 130 }, "assignments": { "type": "string", "id": 131 }, "timestamp": { "type": "uint64", "id": 200 }, "ds_conf": { "type": "DesktopShareConf", "id": 300 }, "ds_state": { "rule": "repeated", "type": "DesktopShareState", "id": 310 }, "recording_state": { "rule": "repeated", "type": "SessionRecordingState", "id": 350 }, "audio_state": { "rule": "repeated", "type": "SessionAudioState", "id": 360 }, "session_video_state": { "rule": "repeated", "type": "SessionVideoState", "id": 370 }, "telephony_conf": { "type": "TelephonyConf", "id": 380 }, "scheduled_start_time": { "type": "uint64", "id": 400 }, "scheduled_end_time": { "type": "uint64", "id": 410 }, "session_status": { "type": "SessionStatus", "id": 420 }, "start_time": { "type": "uint64", "id": 430 }, "end_time": { "type": "uint64", "id": 440 }, "milliseconds_allowed_to_join_before_start": { "type": "uint64", "id": 450 }, "record_multiple_video_channel": { "type": "bool", "id": 460 }, "auto_recording": { "type": "bool", "id": 470 }, "enable_ringtone": { "type": "bool", "id": 480 }, "reminder_interval": { "type": "int64", "id": 485 }, "vendor_service_type": { "type": "VendorServiceType", "id": 490 }, "vendor_start_url": { "type": "string", "id": 491 }, "vendor_join_url": { "type": "string", "id": 492 }, "vendor_meet_id": { "type": "string", "id": 493 }, "vendor_service_owner": { "type": "string", "id": 494 }, "vendor_occurrence_id": { "type": "string", "id": 495 }, "recording": { "type": "uint64", "id": 500 }, "transcription": { "type": "uint64", "id": 502 }, "transcription_vtt": { "type": "uint64", "id": 503 }, "meet_summary": { "type": "uint64", "id": 504 }, "meet_summary_edited": { "type": "bool", "id": 506 }, "audio_speaker": { "type": "uint64", "id": 510 }, "meet_chat": { "type": "uint64", "id": 520 }, "timezone": { "type": "string", "id": 600, "options": { "default": "America/Los_Angeles" } }, "dtstart": { "type": "uint64", "id": 610 }, "rrule": { "type": "string", "id": 620 }, "exdate": { "type": "string", "id": 621 }, "location": { "type": "string", "id": 630 }, "total_events": { "type": "uint64", "id": 700 }, "active_speaker_id": { "type": "string", "id": 800 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "DesktopShareConf": { "edition": "proto2", "fields": { "server_url": { "type": "string", "id": 6 }, "server_addr": { "type": "string", "id": 10 }, "server_ip": { "type": "string", "id": 15 }, "port": { "type": "uint64", "id": 20 }, "conf_id": { "type": "string", "id": 30 }, "token": { "type": "string", "id": 40 }, "is_deleted": { "type": "bool", "id": 50 }, "client_uuid": { "type": "string", "id": 101 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 }, "revision": { "type": "uint64", "id": 1020 }, "sequence": { "type": "uint64", "id": 1030 } } }, "DesktopShareStatus": { "edition": "proto2", "values": { "DS_STOPPED": 0, "DS_STARTED": 10, "DS_PAUSED": 20, "DS_RESUMED": 25, "DS_REMOTECONTROL": 30 } }, "DesktopShareState": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "is_deleted": { "type": "bool", "id": 20 }, "page_sequence": { "type": "uint64", "id": 30 }, "ds_id": { "type": "string", "id": 35 }, "status": { "type": "DesktopShareStatus", "id": 50 }, "is_annotation_enabled": { "type": "bool", "id": 60 }, "is_cobrowsing": { "type": "bool", "id": 70 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 200 }, "revision": { "type": "uint64", "id": 1020 }, "sequence": { "type": "uint64", "id": 1030 }, "assignments": { "type": "string", "id": 1200 } } }, "SessionRecordingStatus": { "edition": "proto2", "values": { "RECORDING_STOPPED": 0, "RECORDING_STARTED": 10, "RECORDING_PAUSED": 20, "RECORDING_RESUMED": 30, "RECORDING_SAVED": 40, "RECORDING_CANCELLED": 50 } }, "SessionRecordingState": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "status": { "type": "SessionRecordingStatus", "id": 20 }, "destination_board_id": { "type": "string", "id": 30 }, "recording_name": { "type": "string", "id": 31 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 }, "revision": { "type": "uint64", "id": 1020 }, "sequence": { "type": "uint64", "id": 1030 } } }, "SessionAudioStatus": { "edition": "proto2", "values": { "AUDIO_STOPPED": 0, "AUDIO_STARTED": 10 } }, "SessionAudioState": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "status": { "type": "SessionAudioStatus", "id": 20 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 }, "revision": { "type": "uint64", "id": 1020 }, "sequence": { "type": "uint64", "id": 1030 } } }, "SessionVideoStatus": { "edition": "proto2", "values": { "SESSION_VIDEO_STOPPED": 0, "SESSION_VIDEO_STARTED": 10 } }, "SessionVideoState": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "status": { "type": "SessionVideoStatus", "id": 20 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 }, "revision": { "type": "uint64", "id": 1020 }, "sequence": { "type": "uint64", "id": 1030 } } }, "CapacityReport": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 1 }, "audioservers": { "rule": "repeated", "type": "AudioServer", "id": 10 }, "dsservers": { "rule": "repeated", "type": "DesktopShareServer", "id": 20 }, "videoservers": { "rule": "repeated", "type": "VideoServer", "id": 30 }, "pbxservers": { "rule": "repeated", "type": "PbxServer", "id": 40 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "AudioServer": { "edition": "proto2", "fields": { "availability_zone": { "type": "string", "id": 5 }, "server_url": { "type": "string", "id": 6 }, "server_addr": { "type": "string", "id": 10 }, "server_name": { "type": "string", "id": 15 }, "port": { "type": "uint64", "id": 21 }, "tcp_port": { "type": "uint64", "id": 22 }, "capacity": { "type": "uint64", "id": 31 }, "keepalive_timestamp": { "type": "uint64", "id": 40 }, "cores": { "rule": "repeated", "type": "AudioServerCore", "id": 45 }, "edge_servers": { "rule": "repeated", "type": "AudioEdgeServer", "id": 50 }, "recording_server_addr": { "type": "string", "id": 60 }, "internal_server_addr": { "type": "string", "id": 62 }, "fingerprint": { "type": "string", "id": 70 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "AudioServerCore": { "edition": "proto2", "fields": { "total_sessions": { "type": "uint64", "id": 20 }, "total_rosters": { "type": "uint64", "id": 30 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "DesktopShareServer": { "edition": "proto2", "fields": { "availability_zone": { "type": "string", "id": 5 }, "server_url": { "type": "string", "id": 6 }, "server_addr": { "type": "string", "id": 10 }, "server_ip": { "type": "string", "id": 15 }, "port": { "type": "uint64", "id": 21 }, "capacity": { "type": "uint64", "id": 31 }, "total_sessions": { "type": "uint64", "id": 32 }, "total_rosters": { "type": "uint64", "id": 33 }, "keepalive_timestamp": { "type": "uint64", "id": 40 }, "recording_server_addr": { "type": "string", "id": 60 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "VideoServer": { "edition": "proto2", "fields": { "availability_zone": { "type": "string", "id": 5 }, "server_addr": { "type": "string", "id": 10 }, "port": { "type": "uint64", "id": 21 }, "capacity": { "type": "uint64", "id": 31 }, "keepalive_timestamp": { "type": "uint64", "id": 40 }, "recording_server_addr": { "type": "string", "id": 60 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "PbxServer": { "edition": "proto2", "fields": { "server_addr": { "type": "string", "id": 10 }, "port": { "type": "uint64", "id": 20 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "TelephoneNumber": { "edition": "proto2", "fields": { "number": { "type": "string", "id": 10 }, "location": { "type": "string", "id": 20 }, "plain_number": { "type": "string", "id": 30 }, "prompt_meetid": { "type": "uint64", "id": 100 }, "prompt_participantnumber": { "type": "uint64", "id": 110 }, "prompt_joined": { "type": "uint64", "id": 120 }, "prompt_left": { "type": "uint64", "id": 130 }, "prompt_ended": { "type": "uint64", "id": 140 }, "prompt_retrymeetid": { "type": "uint64", "id": 150 }, "prompt_invalidmeetid": { "type": "uint64", "id": 160 }, "prompt_notinprogress": { "type": "uint64", "id": 170 }, "prompt_noinput": { "type": "uint64", "id": 180 }, "prompt_goodbye": { "type": "uint64", "id": 190 }, "prompt_waiting": { "type": "uint64", "id": 200 }, "prompt_decline": { "type": "uint64", "id": 210 }, "prompt_muted": { "type": "uint64", "id": 220 }, "prompt_unmuted": { "type": "uint64", "id": 221 }, "prompt_mute_instruction": { "type": "uint64", "id": 222 }, "prompt_password": { "type": "uint64", "id": 230 }, "prompt_password_invalid": { "type": "uint64", "id": 231 }, "prompt_password_try_later": { "type": "uint64", "id": 232 }, "prompt_recording": { "type": "uint64", "id": 240 }, "prompt_first_attendee": { "type": "uint64", "id": 250 }, "resources": { "rule": "repeated", "type": "UserResource", "id": 300 }, "is_default": { "type": "bool", "id": 400 }, "sequence": { "type": "uint64", "id": 1000 }, "client_uuid": { "type": "string", "id": 1010 }, "revision": { "type": "uint64", "id": 1020 }, "is_deleted": { "type": "bool", "id": 1030 }, "local_revision": { "type": "uint64", "id": 1130 }, "created_time": { "type": "uint64", "id": 1040 }, "updated_time": { "type": "uint64", "id": 1050 }, "assignments": { "type": "string", "id": 1200 } } }, "TelephonyDomainSmsProviderType": { "edition": "proto2", "values": { "SMS_PROVIDER_INVALID": 0, "SMS_PROVIDER_TWILIO": 10 } }, "TelephonyDomainSmsProvider": { "edition": "proto2", "fields": { "type": { "type": "TelephonyDomainSmsProviderType", "id": 10 }, "twilio_api_url": { "type": "string", "id": 100 }, "twilio_account_sid": { "type": "string", "id": 110 }, "twilio_auth_token": { "type": "string", "id": 120 }, "twilio_from_number": { "type": "string", "id": 130 } } }, "TelephonyDomainPartner": { "edition": "proto2", "fields": { "partner": { "type": "Partner", "id": 10 } } }, "TelephonyDomain": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "name": { "type": "string", "id": 20 }, "description": { "type": "string", "id": 30 }, "numbers": { "rule": "repeated", "type": "TelephoneNumber", "id": 100 }, "sms_provider": { "type": "TelephonyDomainSmsProvider", "id": 110 }, "partner": { "type": "TelephonyDomainPartner", "id": 200 }, "client_uuid": { "type": "string", "id": 1010 }, "revision": { "type": "uint64", "id": 1020 }, "is_deleted": { "type": "bool", "id": 1030 }, "local_revision": { "type": "uint64", "id": 1130 }, "created_time": { "type": "uint64", "id": 1040 }, "updated_time": { "type": "uint64", "id": 1050 } } }, "CacheMessage": { "edition": "proto2", "fields": { "source_id": { "type": "string", "id": 10 }, "source_session_id": { "type": "string", "id": 20 }, "destination_id": { "type": "string", "id": 100 }, "destination_session_id": { "type": "string", "id": 110 }, "sequence": { "type": "string", "id": 150 }, "timestamp": { "type": "uint64", "id": 155 }, "updated_time": { "type": "uint64", "id": 160 }, "object": { "type": "CacheObject", "id": 200 }, "apn_payload_json": { "type": "string", "id": 410 }, "gcm_payload": { "type": "GCMPushNotificationData", "id": 450 } } }, "CacheObjectChange": { "edition": "proto2", "fields": { "latest_change": { "type": "CacheObject", "id": 10 } } }, "CacheObject": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10 }, "group": { "type": "Group", "id": 20 }, "partner": { "type": "Partner", "id": 21 }, "saml_service": { "type": "SystemSamlService", "id": 22 }, "telephony_domain": { "type": "TelephonyDomain", "id": 23 }, "board": { "type": "Board", "id": 30 }, "webapp": { "type": "WebApp", "id": 40 }, "recording": { "type": "ObjectRecording", "id": 50 }, "usage": { "type": "UsageStatistics", "id": 60 }, "audio_report": { "type": "CapacityReport", "id": 70 }, "ds_report": { "type": "CapacityReport", "id": 71 }, "video_report": { "type": "CapacityReport", "id": 72 }, "system_config": { "type": "SystemConfig", "id": 77 }, "session": { "type": "ActionObject", "id": 80 }, "audio": { "type": "AudioRecording", "id": 90 }, "presence": { "type": "Presence", "id": 100 }, "contacts": { "type": "Contacts", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "previous_revision": { "type": "uint64", "id": 111 }, "updated_time": { "type": "uint64", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "activity_logs": { "rule": "repeated", "type": "ActivityLog", "id": 1000 }, "activity_stats": { "type": "ActivityStatistics", "id": 1010 }, "local_changes": { "rule": "repeated", "type": "ClientRequest", "id": 3000 }, "latest_changes": { "rule": "repeated", "type": "CacheObjectChange", "id": 4000 }, "request_id": { "type": "string", "id": 5000 } } }, "Contacts": { "edition": "proto2", "fields": { "contacts": { "rule": "repeated", "type": "User", "id": 10 }, "revision": { "type": "uint64", "id": 110 }, "previous_revision": { "type": "uint64", "id": 120 }, "previous_server": { "type": "string", "id": 200 }, "previous_timestamp": { "type": "uint64", "id": 210 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "Presence": { "edition": "proto2", "fields": { "issuer": { "type": "User", "id": 10 }, "connection_id": { "type": "string", "id": 20 }, "buddies": { "rule": "repeated", "type": "User", "id": 100 }, "uptime": { "type": "uint64", "id": 150 } } }, "HttpHeader": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "string_value": { "type": "string", "id": 20 } } }, "UserType": { "edition": "proto2", "options": { "allow_alias": true }, "values": { "USER_TYPE_NORMAL": 0, "USER_TYPE_INVALID": 1, "USER_TYPE_DEVICE": 10, "USER_TYPE_AGENT": 30, "USER_TYPE_SSO": 50, "USER_TYPE_SERVICE": 90, "USER_TYPE_BOT": 100, "USER_TYPE_WEBAPP": 100, "USER_TYPE_LOCAL": 120 } }, "UserOSType": { "edition": "proto2", "values": { "OS_TYPE_WINDOWS": 40, "OS_TYPE_WINDOWS_XP": 0, "OS_TYPE_WINDOWS_VISTA": 10, "OS_TYPE_WINDOWS_7": 20, "OS_TYPE_WINDOWS_8": 30, "OS_TYPE_MAC": 100, "OS_TYPE_MAC_10_6": 110, "OS_TYPE_MAC_10_7": 120, "OS_TYPE_MAC_10_8": 130, "OS_TYPE_MAC_10_9": 140, "OS_TYPE_MAC_10_10": 150, "OS_TYPE_IOS": 200, "OS_TYPE_ANDROID": 210, "OS_TYPE_CLOUD": 300 } }, "NotificationLevel": { "edition": "proto2", "values": { "NOTIFICATION_LEVEL_ALL": 0, "NOTIFICATION_LEVEL_RELATED": 10, "NOTIFICATION_LEVEL_NOTHING": 20 } }, "UserResourceType": { "edition": "proto2", "values": { "USER_RESOURCE_ORINGINAL": 0, "USER_RESOURCE_PICTURE": 10, "USER_RESOURCE_PICTURE2x": 20, "USER_RESOURCE_PICTURE4x": 30, "USER_RESOURCE_CSV_IMPORT_INPUT": 40, "USER_RESOURCE_CSV_IMPORT_OUTPUT": 50, "USER_RESOURCE_STAMP": 60 } }, "UserResource": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "type": { "type": "UserResourceType", "id": 20 }, "content_type": { "type": "string", "id": 21 }, "content_length": { "type": "uint64", "id": 22 }, "sha256_hash": { "type": "string", "id": 29 }, "hash": { "type": "string", "id": 30 }, "original_url": { "type": "string", "id": 40 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UserTag": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "string_value": { "type": "string", "id": 20 }, "uint64_value": { "type": "uint64", "id": 30 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UserBoard": { "edition": "proto2", "fields": { "board": { "type": "Board", "id": 10, "options": { "lazy": true } }, "status": { "type": "BoardUserStatus", "id": 20 }, "type": { "type": "BoardAccessType", "id": 25 }, "category": { "type": "uint64", "id": 30 }, "category_uuid": { "type": "string", "id": 31 }, "order_number": { "type": "string", "id": 40 }, "is_default": { "type": "bool", "id": 50 }, "is_group": { "type": "bool", "id": 55 }, "feed_unread_count": { "type": "uint64", "id": 58 }, "first_unread_feed_sequence": { "type": "uint64", "id": 59 }, "accessed_time": { "type": "uint64", "id": 60 }, "first_unread_feed_timestamp": { "type": "uint64", "id": 63 }, "enabled_time": { "type": "uint64", "id": 62 }, "dismissed_time": { "type": "uint64", "id": 64 }, "is_favorite": { "type": "bool", "id": 70 }, "is_archive": { "type": "bool", "id": 71 }, "archived_time": { "type": "uint64", "id": 72 }, "is_notification_off": { "type": "bool", "id": 80 }, "push_notification_level": { "type": "NotificationLevel", "id": 81 }, "waiting_signatures": { "type": "uint64", "id": 90 }, "original_sequence": { "type": "uint64", "id": 200 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UserBoardCategory": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "is_archive": { "type": "bool", "id": 30 }, "order_number": { "type": "string", "id": 90 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UserToken": { "edition": "proto2", "fields": { "token": { "type": "string", "id": 10 }, "apple_device_token": { "type": "string", "id": 20 }, "apple_voip_token": { "type": "string", "id": 21 }, "client_version": { "type": "uint64", "id": 30 }, "android_device_token": { "type": "string", "id": 40 }, "vendor": { "type": "uint64", "id": 41 }, "vendor_ext": { "type": "string", "id": 42 }, "is_access_token": { "type": "bool", "id": 50 }, "is_refresh_token": { "type": "bool", "id": 51 }, "access_token": { "type": "uint64", "id": 52 }, "client_ua": { "type": "string", "id": 60 }, "client_accept_language": { "type": "string", "id": 70 }, "client_id": { "type": "string", "id": 80 }, "uid": { "type": "string", "id": 200 }, "token_sequence": { "type": "uint64", "id": 210 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UserQRToken": { "edition": "proto2", "fields": { "token": { "type": "string", "id": 10 }, "creator": { "type": "BoardActor", "id": 20 }, "last_updated_time": { "type": "uint64", "id": 30 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UserAgent": { "edition": "proto2", "fields": { "agent": { "type": "User", "id": 10, "options": { "lazy": true } }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UserContactStatus": { "edition": "proto2", "values": { "CONTACT_NORMAL": 0, "CONTACT_PENDING": 100, "CONTACT_INVITED": 200 } }, "UserContact": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10, "options": { "lazy": true } }, "group": { "type": "Group", "id": 15, "options": { "lazy": true } }, "status": { "type": "UserContactStatus", "id": 20 }, "has_push_notification": { "type": "bool", "id": 30 }, "is_private": { "type": "bool", "id": 50 }, "is_from_team": { "type": "bool", "id": 60 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "keep_deleted": { "type": "bool", "id": 121 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UserRole": { "edition": "proto2", "values": { "USER_ROLE_NORMAL": 0, "USER_ROLE_CRASH_REPORT_READ": 10, "USER_ROLE_SERVER_STATUS_READ": 100, "USER_ROLE_SUPERADMIN_READONLY": 140, "USER_ROLE_SUPERADMIN": 150, "USER_ROLE_OBJECT_READ": 200, "USER_ROLE_OBJECT_WRITE": 300 } }, "UserLevel": { "edition": "proto2", "values": { "USER_LEVEL_FREE": 0, "USER_LEVEL_TRIAL": 10, "USER_LEVEL_PRO": 20, "USER_LEVEL_BETA": 100 } }, "UserCap": { "edition": "proto2", "fields": { "user_boards_max": { "type": "uint64", "id": 10 }, "open_flow_boards_max": { "type": "uint64", "id": 12 }, "board_users_max": { "type": "uint64", "id": 20 }, "board_pages_max": { "type": "uint64", "id": 30 }, "board_history_max": { "type": "uint64", "id": 31 }, "board_notification_max": { "type": "uint64", "id": 32 }, "session_users_max": { "type": "uint64", "id": 40 }, "user_cloud_max": { "type": "uint64", "id": 50 }, "client_max_body_size": { "type": "uint64", "id": 69 }, "client_allowed_file_types": { "rule": "repeated", "type": "string", "id": 71 }, "meet_duration_max": { "type": "uint64", "id": 70 }, "allow_meet_recording": { "type": "bool", "id": 72 }, "allow_meet_voip": { "type": "bool", "id": 74 }, "allow_meet_telephony": { "type": "bool", "id": 76 }, "user_agent_max": { "type": "uint64", "id": 80 }, "user_integrations_max": { "type": "uint64", "id": 84 }, "group_boards_max": { "type": "uint64", "id": 100 }, "team_users_max": { "type": "uint64", "id": 110 }, "user_relations_max": { "type": "uint64", "id": 200 } } }, "UserFavorite": { "edition": "proto2", "fields": { "board": { "type": "Board", "id": 10, "options": { "lazy": true } }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UserMentionMe": { "edition": "proto2", "fields": { "board": { "type": "Board", "id": 10, "options": { "lazy": true } }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "CallType": { "edition": "proto2", "values": { "CALL_TYPE_INVALID": 0, "CALL_TYPE_OUTBOUND": 10, "CALL_TYPE_INBOUND": 20 } }, "CallStatus": { "edition": "proto2", "values": { "CALL_STATUS_INVALID": 0, "CALL_STATUS_INITIALIZED": 100, "CALL_STATUS_RINGING": 110, "CALL_STATUS_CONNECTING": 120, "CALL_STATUS_CONNECTED": 130, "CALL_STATUS_CANCELLED": 200, "CALL_STATUS_NOANSWER": 210, "CALL_STATUS_DECLINED": 220, "CALL_STATUS_ENDED": 230, "CALL_STATUS_FAILED": 240 } }, "ClientType": { "edition": "proto2", "values": { "CLIENT_TYPE_INVALID": 0, "CLIENT_TYPE_UC": 10, "CLIENT_TYPE_PHONE": 20 } }, "CallUser": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10, "options": { "lazy": true } }, "client_type": { "type": "ClientType", "id": 20 }, "sip_address": { "type": "string", "id": 110 }, "call_sequence": { "type": "uint64", "id": 200 } } }, "UserCallLog": { "edition": "proto2", "fields": { "call_id": { "type": "string", "id": 10 }, "type": { "type": "CallType", "id": 20 }, "status": { "type": "CallStatus", "id": 30 }, "peer": { "type": "CallUser", "id": 40 }, "start_time": { "type": "uint64", "id": 50 }, "end_time": { "type": "uint64", "id": 60 }, "board_id": { "type": "string", "id": 200 }, "session_key": { "type": "string", "id": 210 }, "session_id": { "type": "string", "id": 300 }, "client_type": { "type": "ClientType", "id": 310 }, "sip_call_id": { "type": "string", "id": 320 }, "dtmf_digits": { "type": "string", "id": 400 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "ACDType": { "edition": "proto2", "values": { "ACD_TYPE_INVALID": 0, "ACD_TYPE_CHAT": 10, "ACD_TYPE_MEET": 20 } }, "ACDStatus": { "edition": "proto2", "values": { "ACD_STATUS_INVALID": 0, "ACD_STATUS_INITIALIZED": 100, "ACD_STATUS_QUEUED": 110, "ACD_STATUS_CONNECTING": 120, "ACD_STATUS_CONNECTED": 130, "ACD_STATUS_CANCELLED": 200, "ACD_STATUS_NOANSWER": 210, "ACD_STATUS_DECLINED": 220, "ACD_STATUS_ENDED": 230, "ACD_STATUS_FAILED": 240 } }, "UserACDLog": { "edition": "proto2", "fields": { "call_id": { "type": "string", "id": 10 }, "type": { "type": "ACDType", "id": 20 }, "status": { "type": "ACDStatus", "id": 30 }, "peer": { "type": "User", "id": 40 }, "start_time": { "type": "uint64", "id": 50 }, "end_time": { "type": "uint64", "id": 60 }, "board_id": { "type": "string", "id": 200 }, "session_key": { "type": "string", "id": 210 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UserConnection": { "edition": "proto2", "fields": { "session_id": { "type": "string", "id": 10 }, "connection_id": { "type": "string", "id": 20 }, "updated_time": { "type": "uint64", "id": 100 } } }, "DateRange": { "edition": "proto2", "fields": { "start_time": { "type": "uint64", "id": 10 }, "end_time": { "type": "uint64", "id": 20 } } }, "OutOfOfficeStatus": { "edition": "proto2", "fields": { "status": { "type": "bool", "id": 10 }, "start_time": { "type": "uint64", "id": 20 }, "end_time": { "type": "uint64", "id": 30 }, "message": { "type": "string", "id": 40 }, "backup": { "type": "User", "id": 50, "options": { "lazy": true } }, "r_out_of_office": { "rule": "repeated", "type": "DateRange", "id": 60 } } }, "UserRelationStatus": { "edition": "proto2", "values": { "RELATION_INIT": 0, "RELATION_PENDING": 100, "RELATION_NORMAL": 200 } }, "UserRelation": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10, "options": { "lazy": true } }, "status": { "type": "UserRelationStatus", "id": 20 }, "invitation_code": { "type": "string", "id": 30 }, "line_invitation_code": { "type": "string", "id": 31 }, "whatsapp_invitation_code": { "type": "string", "id": 35 }, "whatsapp_chat_id": { "type": "string", "id": 32 }, "whatsapp_chat_status": { "type": "string", "id": 33 }, "whatsapp_invitation_url": { "type": "string", "id": 34 }, "invited_time": { "type": "uint64", "id": 80 }, "order_number": { "type": "string", "id": 90 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BotUserRelation": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10, "options": { "lazy": true } }, "status": { "type": "UserRelationStatus", "id": 20 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "HashAlgorithm": { "edition": "proto2", "values": { "HASH_ALGORITHM_SHA256": 0, "HASH_ALGORITHM_PKCS5_PBKDF2_SHA256": 10 } }, "SignatureStyle": { "edition": "proto2", "values": { "SIGNATURE_STYLE_NONE": 0, "SIGNATURE_STYLE_PRESELECTED": 10, "SIGNATURE_STYLE_DRAWN_ON_DEVICE": 20 } }, "UserDevice": { "edition": "proto2", "fields": { "device_id": { "type": "string", "id": 10 }, "client_ua": { "type": "string", "id": 20 }, "timestamp": { "type": "uint64", "id": 30 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UserNotification": { "edition": "proto2", "fields": { "payload": { "type": "ApplePushNotificationPayload", "id": 30 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardMemberNotificationSetting": { "edition": "proto2", "fields": { "not_join_within": { "type": "uint64", "id": 100 }, "not_join_within_to_owner": { "type": "uint64", "id": 200 } } }, "ActionNotificationSetting": { "edition": "proto2", "fields": { "on_due": { "type": "bool", "id": 100, "options": { "default": true } }, "before_due": { "type": "uint64", "id": 200 }, "after_due": { "type": "uint64", "id": 300 }, "after_due_repeat": { "type": "uint64", "id": 310 } } }, "BoardNotificationSetting": { "edition": "proto2", "fields": { "on_due": { "type": "bool", "id": 100, "options": { "default": true } }, "before_due": { "type": "uint64", "id": 200 }, "after_due": { "type": "uint64", "id": 300, "options": { "default": 86400000 } }, "after_due_repeat": { "type": "uint64", "id": 310, "options": { "default": 100 } } } }, "User": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "name": { "type": "string", "id": 11 }, "first_name": { "type": "string", "id": 12 }, "last_name": { "type": "string", "id": 13 }, "type": { "type": "UserType", "id": 14, "options": { "default": "USER_TYPE_NORMAL" } }, "phone_number": { "type": "string", "id": 15 }, "online": { "type": "bool", "id": 16 }, "in_meet": { "type": "bool", "id": 903 }, "os_type": { "type": "UserOSType", "id": 17 }, "disabled": { "type": "bool", "id": 18 }, "passcode_protected": { "type": "bool", "id": 19 }, "email": { "type": "string", "id": 20 }, "unique_id": { "type": "string", "id": 22 }, "apple_device_token": { "type": "string", "id": 23 }, "agent_user_id": { "type": "string", "id": 24 }, "work_phone_number": { "type": "string", "id": 26 }, "sip_configuration": { "type": "string", "id": 27 }, "extension_phone_number": { "type": "string", "id": 28 }, "title": { "type": "string", "id": 29 }, "display_email": { "type": "string", "id": 1600 }, "display_phone_number": { "type": "string", "id": 1610 }, "display_id": { "type": "string", "id": 1620 }, "pass": { "type": "string", "id": 30 }, "hashed_pass": { "type": "bytes", "id": 31 }, "pass_algorithm": { "type": "HashAlgorithm", "id": 35 }, "old_pass": { "type": "string", "id": 32 }, "address": { "type": "string", "id": 33 }, "email_onboarded": { "type": "bool", "id": 34 }, "tokens": { "rule": "repeated", "type": "string", "id": 40 }, "user_tokens": { "rule": "repeated", "type": "UserToken", "id": 41 }, "qr_tokens": { "rule": "repeated", "type": "UserQRToken", "id": 42 }, "email_verification_token": { "type": "string", "id": 50 }, "email_verification_code": { "type": "string", "id": 51 }, "code_updated_time": { "type": "uint64", "id": 52 }, "failed_attempts": { "type": "uint64", "id": 53 }, "last_login_timestamp": { "type": "uint64", "id": 54 }, "reset_password_timestamps": { "rule": "repeated", "type": "uint64", "id": 55 }, "lookup_domain_timestamps": { "rule": "repeated", "type": "uint64", "id": 56 }, "email_verified": { "type": "bool", "id": 60 }, "token_updated_time": { "type": "uint64", "id": 61 }, "picture": { "type": "uint64", "id": 71 }, "picture2x": { "type": "uint64", "id": 72 }, "picture4x": { "type": "uint64", "id": 73 }, "picture_path": { "type": "string", "id": 74 }, "picture2x_path": { "type": "string", "id": 75 }, "picture4x_path": { "type": "string", "id": 76 }, "picture_url": { "type": "string", "id": 77 }, "android_device_token": { "type": "string", "id": 80 }, "apple_voip_token": { "type": "string", "id": 90 }, "ios_app_id": { "type": "string", "id": 91 }, "android_app_pkg_name": { "type": "string", "id": 92 }, "boards": { "rule": "repeated", "type": "UserBoard", "id": 101 }, "personal_rooms": { "rule": "repeated", "type": "UserBoard", "id": 150 }, "resources": { "rule": "repeated", "type": "UserResource", "id": 200 }, "tags": { "rule": "repeated", "type": "UserTag", "id": 220 }, "agents": { "rule": "repeated", "type": "UserAgent", "id": 230 }, "call_logs": { "rule": "repeated", "type": "UserCallLog", "id": 250 }, "acd_logs": { "rule": "repeated", "type": "UserACDLog", "id": 270 }, "group_boards": { "rule": "repeated", "type": "UserBoard", "id": 280 }, "role": { "type": "UserRole", "id": 300 }, "level": { "type": "UserLevel", "id": 310, "options": { "default": "USER_LEVEL_FREE" } }, "cap": { "type": "UserCap", "id": 320 }, "total_cloud_size": { "type": "uint64", "id": 330 }, "boards_owned": { "type": "uint64", "id": 340 }, "boards_invited": { "type": "uint64", "id": 341 }, "boards_total": { "type": "uint64", "id": 342 }, "boards_owned_pages": { "type": "uint64", "id": 343 }, "boards_owned_comments": { "type": "uint64", "id": 344 }, "boards_owned_todos": { "type": "uint64", "id": 345 }, "boards_owned_invitees": { "type": "uint64", "id": 346 }, "meet_hosted": { "type": "uint64", "id": 350 }, "meet_invited": { "type": "uint64", "id": 351 }, "contacts_total": { "type": "uint64", "id": 360 }, "relations_total": { "type": "uint64", "id": 361 }, "feed_unread_total": { "type": "uint64", "id": 362 }, "feed_unread_sr": { "type": "uint64", "id": 363 }, "agents_total": { "type": "uint64", "id": 370 }, "accessed_time": { "type": "uint64", "id": 380 }, "last_archive_time": { "type": "uint64", "id": 390 }, "last_active_time": { "type": "uint64", "id": 391 }, "groups": { "rule": "repeated", "type": "UserGroup", "id": 400 }, "webapps": { "rule": "repeated", "type": "UserWebApp", "id": 410 }, "partners": { "rule": "repeated", "type": "UserPartner", "id": 420 }, "managed_teams": { "rule": "repeated", "type": "UserGroup", "id": 430 }, "collab_teams": { "rule": "repeated", "type": "UserGroup", "id": 440 }, "categories": { "rule": "repeated", "type": "UserBoardCategory", "id": 500 }, "feeds": { "rule": "repeated", "type": "ObjectFeed", "id": 600 }, "enable_notification_emails": { "type": "bool", "id": 701, "options": { "default": true } }, "enable_digest_email": { "type": "bool", "id": 702, "options": { "default": true } }, "last_digest_email_timestamp": { "type": "uint64", "id": 703 }, "timezone": { "type": "string", "id": 710, "options": { "default": "America/Los_Angeles" } }, "language": { "type": "string", "id": 720, "options": { "default": "en" } }, "contacts": { "rule": "repeated", "type": "UserContact", "id": 900 }, "customized_presence_status": { "type": "uint64", "id": 901 }, "customized_presence_message": { "type": "string", "id": 902 }, "has_push_notification": { "type": "bool", "id": 910 }, "collaborators": { "rule": "repeated", "type": "UserContact", "id": 930 }, "connections": { "rule": "repeated", "type": "UserConnection", "id": 950 }, "relations": { "rule": "repeated", "type": "UserRelation", "id": 960 }, "bot_relations": { "rule": "repeated", "type": "BotUserRelation", "id": 961 }, "board_notification_level": { "type": "NotificationLevel", "id": 920 }, "session_notification_level": { "type": "NotificationLevel", "id": 921 }, "board_member_notification_settings": { "type": "BoardMemberNotificationSetting", "id": 922 }, "action_notification_settings": { "type": "ActionNotificationSetting", "id": 923 }, "board_notification_settings": { "type": "BoardNotificationSetting", "id": 924 }, "favorites": { "rule": "repeated", "type": "UserFavorite", "id": 1300 }, "mentionmes": { "rule": "repeated", "type": "UserMentionMe", "id": 1310 }, "action_items": { "rule": "repeated", "type": "UserBoard", "id": 1320 }, "action_accessed_time": { "type": "uint64", "id": 1321 }, "notifications": { "rule": "repeated", "type": "UserNotification", "id": 1330 }, "notification_accessed_time": { "type": "uint64", "id": 1331 }, "broadcasts": { "rule": "repeated", "type": "UserBroadcast", "id": 1350 }, "ext_broadcasts": { "rule": "repeated", "type": "UserBoard", "id": 1360 }, "keep_short": { "type": "bool", "id": 1200 }, "sip_registration_status": { "type": "bool", "id": 1400 }, "sip_registration_message": { "type": "string", "id": 1410 }, "sip_unread_voice_mail_count": { "type": "uint64", "id": 1420 }, "sip_has_unread_voice_mail": { "type": "bool", "id": 1421 }, "signature": { "type": "uint64", "id": 1500 }, "initials": { "type": "uint64", "id": 1501 }, "initials_text": { "type": "string", "id": 1502 }, "legal_name": { "type": "string", "id": 1503 }, "signature_style": { "type": "SignatureStyle", "id": 1504 }, "signature_path": { "type": "string", "id": 1510 }, "initials_path": { "type": "string", "id": 1511 }, "out_of_office": { "type": "OutOfOfficeStatus", "id": 1521 }, "social": { "type": "string", "id": 1700 }, "social_id": { "type": "string", "id": 1701 }, "social_avatar": { "type": "string", "id": 1702 }, "social_status": { "type": "string", "id": 1703 }, "invitation_code": { "type": "string", "id": 1704 }, "connector": { "type": "uint64", "id": 1705 }, "social_updated_time": { "type": "uint64", "id": 1706 }, "line_social_id": { "type": "string", "id": 1711 }, "line_social_avatar": { "type": "string", "id": 1712 }, "line_social_status": { "type": "string", "id": 1713 }, "line_invitation_code": { "type": "string", "id": 1714 }, "line_connector": { "type": "uint64", "id": 1715 }, "whatsapp_social_id": { "type": "string", "id": 1721 }, "whatsapp_social_avatar": { "type": "string", "id": 1722 }, "whatsapp_social_status": { "type": "string", "id": 1725 }, "whatsapp_invitation_code": { "type": "string", "id": 1723 }, "whatsapp_connector": { "type": "uint64", "id": 1724 }, "custom1": { "type": "string", "id": 1801 }, "custom2": { "type": "string", "id": 1802 }, "custom3": { "type": "string", "id": 1803 }, "user_devices": { "rule": "repeated", "type": "UserDevice", "id": 2001 }, "flow_templates": { "rule": "repeated", "type": "UserBoard", "id": 2200 }, "weekdays": { "rule": "repeated", "type": "RoutingWeekday", "id": 2210 }, "special_days": { "rule": "repeated", "type": "RoutingSpecialDay", "id": 2211 }, "meeting_buffer_time": { "type": "uint64", "id": 2220 }, "self_service_templates": { "rule": "repeated", "type": "UserBoard", "id": 2300 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "GroupUserStatus": { "edition": "proto2", "values": { "GROUP_INVALID": 0, "GROUP_INVITED": 30, "GROUP_MEMBER": 40 } }, "GroupAccessType": { "edition": "proto2", "values": { "GROUP_NO_ACCESS": 0, "GROUP_READONLY_ACCESS": 50, "GROUP_MEMBER_ACCESS": 100, "GROUP_ADMIN_READONLY_ACCESS": 150, "GROUP_ADMIN_ACCESS": 200, "GROUP_OWNER_ACCESS": 300 } }, "UserBroadcast": { "edition": "proto2", "fields": { "title": { "type": "string", "id": 10 }, "contacts": { "rule": "repeated", "type": "UserContact", "id": 20 }, "msg_count": { "type": "uint64", "id": 30 }, "msg_sent_timestamp": { "type": "uint64", "id": 40 }, "last_modified_timestamp": { "type": "uint64", "id": 50 }, "is_default": { "type": "bool", "id": 60 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 130 }, "local_revision": { "type": "uint64", "id": 140 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BroadcastStatus": { "edition": "proto2", "values": { "BROADCAST_QUEUED": 0, "BROADCAST_SCHEDULED": 10, "BROADCAST_PROCESSING": 20, "BROADCAST_COMPLETED": 30 } }, "BroadcastChannel": { "edition": "proto2", "values": { "BROADCAST_CHANNEL_WORKSPACE": 0, "BROADCAST_CHANNEL_INBOX": 10 } }, "BroadcastTarget": { "edition": "proto2", "values": { "BROADCAST_TARGET_USER_LISTS": 0, "BROADCAST_TARGET_SPECIFIC_USERS": 10, "BROADCAST_TARGET_MY_CLIENTS": 20, "BROADCAST_TARGET_ALL_CLIENTS": 30, "BROADCAST_TARGET_CDL": 40 } }, "BoardBroadcast": { "edition": "proto2", "fields": { "user_list": { "type": "UserBroadcast", "id": 10 }, "status": { "type": "BroadcastStatus", "id": 20 }, "target": { "type": "BroadcastTarget", "id": 30 }, "channel": { "type": "BroadcastChannel", "id": 40 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 130 }, "local_revision": { "type": "uint64", "id": 140 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UserGroup": { "edition": "proto2", "fields": { "group": { "type": "Group", "id": 10, "options": { "lazy": true } }, "status": { "type": "GroupUserStatus", "id": 20 }, "type": { "type": "GroupAccessType", "id": 25 }, "role": { "type": "uint64", "id": 26 }, "roles": { "rule": "repeated", "type": "uint64", "id": 27 }, "onboarded_time": { "type": "uint64", "id": 28 }, "group_sequence": { "type": "uint64", "id": 30 }, "member_alias": { "type": "string", "id": 40 }, "order_number": { "type": "string", "id": 90 }, "tac_agreed_time": { "type": "uint64", "id": 200 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UserPartner": { "edition": "proto2", "fields": { "partner": { "type": "Partner", "id": 10, "options": { "lazy": true } }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "GroupUser": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10, "options": { "lazy": true } }, "status": { "type": "GroupUserStatus", "id": 20 }, "type": { "type": "GroupAccessType", "id": 30 }, "onboarded_time": { "type": "uint64", "id": 21 }, "alias": { "type": "string", "id": 62 }, "is_invalid": { "type": "bool", "id": 70 }, "message": { "type": "string", "id": 71 }, "invalid_fields": { "rule": "repeated", "type": "string", "id": 72 }, "order_number": { "type": "string", "id": 80 }, "role": { "type": "uint64", "id": 90 }, "roles": { "rule": "repeated", "type": "uint64", "id": 95 }, "role_assigned_time": { "type": "uint64", "id": 91 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "keep_deleted": { "type": "bool", "id": 121 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "GroupBoard": { "edition": "proto2", "fields": { "board": { "type": "Board", "id": 10, "options": { "lazy": true } }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "GroupEmailConfig": { "edition": "proto2", "fields": { "username": { "type": "string", "id": 10 }, "password": { "type": "string", "id": 20 }, "server_address": { "type": "string", "id": 30 }, "server_port": { "type": "uint64", "id": 40 }, "from_address": { "type": "string", "id": 50 }, "is_ssl": { "type": "bool", "id": 60 } } }, "GroupIntegrationType": { "edition": "proto2", "values": { "GROUP_INTEGRATION_SAML": 20, "GROUP_INTEGRATION_APP": 30, "GROUP_INTEGRATION_LOCAL": 40, "GROUP_INTEGRATION_WECHAT": 50, "GROUP_INTEGRATION_WHATSAPP": 60, "GROUP_INTEGRATION_LINE": 70, "GROUP_INTEGRATION_SMTP": 100, "GROUP_INTEGRATION_CRM": 200, "GROUP_INTEGRATION_OUTGOING": 300 } }, "GroupIntegration": { "edition": "proto2", "fields": { "type": { "type": "GroupIntegrationType", "id": 10 }, "enable_auto_provision": { "type": "bool", "id": 210 }, "webapp": { "type": "WebApp", "id": 301 }, "user": { "type": "GroupUser", "id": 302 }, "group": { "type": "Group", "id": 303 }, "board": { "type": "GroupBoard", "id": 304 }, "is_free_subscription": { "type": "bool", "id": 305 }, "category": { "type": "string", "id": 310 }, "idp_conf": { "type": "SamlIdentityProviderConfig", "id": 400 }, "saml_user_role_types": { "rule": "repeated", "type": "GroupUserRoleType", "id": 402 }, "domain": { "type": "string", "id": 500 }, "email_config": { "type": "GroupEmailConfig", "id": 510 }, "connector": { "type": "string", "id": 600 }, "crm_configuration": { "type": "string", "id": 610 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "keep_deleted": { "type": "bool", "id": 121 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "GroupSubscriptionStatus": { "edition": "proto2", "values": { "GROUP_TRIAL_SUBSCRIPTION": 0, "GROUP_NORMAL_SUBSCRIPTION": 100, "GROUP_PAST_DUE_SUBSCRIPTION": 120, "GROUP_CANCELED_SUBSCRIPTION": 200, "GROUP_EXPIRED_SUBSCRIPTION": 300, "GROUP_DISABLED_SUBSCRIPTION": 400 } }, "GroupContact": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10, "options": { "lazy": true } } } }, "GroupSupport": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10, "options": { "lazy": true } } } }, "GroupPartner": { "edition": "proto2", "fields": { "partner": { "type": "Partner", "id": 10, "options": { "lazy": true } } } }, "GroupCap": { "edition": "proto2", "fields": { "is_enterprise": { "type": "bool", "id": 10 }, "has_sip": { "type": "bool", "id": 12 }, "has_board_owner_delegate": { "type": "bool", "id": 13 }, "has_email_privacy": { "type": "bool", "id": 14 }, "has_push_privacy": { "type": "bool", "id": 15 }, "has_user_archive": { "type": "bool", "id": 16 }, "has_saml": { "type": "bool", "id": 20, "options": { "default": true } }, "is_online_billing": { "type": "bool", "id": 30 }, "hide_moxtra_logo": { "type": "bool", "id": 40 }, "has_app_portal": { "type": "bool", "id": 50 }, "has_app_subscription": { "type": "bool", "id": 51 }, "has_audit": { "type": "bool", "id": 60 }, "has_org_report": { "type": "bool", "id": 61 }, "has_read_receipt": { "type": "bool", "id": 65 }, "ignore_email_verification": { "type": "bool", "id": 70 }, "has_branding": { "type": "bool", "id": 100, "options": { "default": true } }, "has_configuration": { "type": "bool", "id": 110, "options": { "default": true } }, "disable_invite_client": { "type": "bool", "id": 200 }, "enable_acd": { "type": "bool", "id": 210 }, "enable_service_request": { "type": "bool", "id": 220 }, "enable_phone_number_sign_up": { "type": "bool", "id": 300 }, "primary_sign_up_phone_number": { "type": "bool", "id": 310 }, "enable_channel_subscription": { "type": "bool", "id": 320 }, "enable_direct_invitation": { "type": "bool", "id": 330 }, "enable_hard_deletion": { "type": "bool", "id": 340 }, "hide_saml_configuration": { "type": "bool", "id": 350 }, "disable_user_creation": { "type": "bool", "id": 360 }, "enable_sfdc_integration": { "type": "bool", "id": 370 }, "enable_globalrelay_integration": { "type": "bool", "id": 371 }, "enable_hubspot_integration": { "type": "bool", "id": 372 }, "enable_dynamics_integration": { "type": "bool", "id": 373 }, "enable_filevine_integration": { "type": "bool", "id": 374 }, "enable_redtail_integration": { "type": "bool", "id": 375 }, "enable_zoho_integration": { "type": "bool", "id": 376 }, "enable_smarsh_integration": { "type": "bool", "id": 377 }, "enable_advisorengine_integration": { "type": "bool", "id": 378 }, "enable_meeting_audio_ringtone": { "type": "bool", "id": 380 }, "enable_meeting_transcription": { "type": "bool", "id": 381 }, "enable_xero_integration": { "type": "bool", "id": 390 }, "enable_wealthbox_integration": { "type": "bool", "id": 400 }, "enable_referrer_check": { "type": "bool", "id": 500 }, "enable_access_control_allow_origin": { "type": "bool", "id": 510 }, "enable_xsrf_token": { "type": "bool", "id": 520 }, "enable_2fa": { "type": "bool", "id": 600 }, "enable_2fa_trust_device": { "type": "bool", "id": 601, "options": { "default": true } }, "keep_identification_for_deleted_user": { "type": "bool", "id": 700 }, "enforce_logout_account_when_quiting_app": { "type": "bool", "id": 710 }, "enable_client_delete_account": { "type": "bool", "id": 720 }, "enable_apple_sign_in": { "type": "bool", "id": 800, "options": { "default": true } }, "enable_google_sign_in": { "type": "bool", "id": 810, "options": { "default": true } }, "enable_salesforce_sign_in": { "type": "bool", "id": 812 }, "enforce_password_login": { "type": "bool", "id": 820 }, "share_org_flow_templates": { "type": "bool", "id": 830 }, "is_freemium": { "type": "bool", "id": 840 }, "share_org_content_library": { "type": "bool", "id": 850 }, "enable_custom_smtp_integration": { "type": "bool", "id": 860 }, "enable_new_frame": { "type": "bool", "id": 870 }, "enable_chat_workspace": { "type": "bool", "id": 880, "options": { "default": true } }, "hide_client_dashboard": { "type": "bool", "id": 890 }, "server_session_timeout_interval": { "type": "uint64", "id": 900 }, "restrict_new_message_email_sms": { "type": "bool", "id": 910 }, "enable_share_link": { "type": "bool", "id": 920, "options": { "default": true } } } }, "GroupSetting": { "edition": "proto2", "fields": { "content_editable_interval": { "type": "uint64", "id": 10 }, "content_editable_interval_for_client": { "type": "uint64", "id": 11 }, "org_invitation_expiry": { "type": "uint64", "id": 20 }, "enable_private_meet": { "type": "bool", "id": 40 }, "hide_meet_recording": { "type": "bool", "id": 50 }, "disable_meet_recording_sharing": { "type": "bool", "id": 55 }, "enable_meet_auto_recording": { "type": "bool", "id": 56 }, "enable_meet_password": { "type": "bool", "id": 60 }, "meeting_default_password": { "type": "string", "id": 61 }, "enable_mobile_web_meeting_join": { "type": "bool", "id": 70 }, "enable_workflow_event": { "type": "bool", "id": 80 }, "enable_digest_email": { "type": "bool", "id": 90 }, "digest_email_start_timestamp": { "type": "uint64", "id": 100 }, "digest_email_interval": { "type": "uint64", "id": 110 }, "enable_client_self_signup": { "type": "bool", "id": 120 }, "send_service_request_on_client_self_signup": { "type": "bool", "id": 121 }, "enable_client_group": { "type": "bool", "id": 122 }, "enable_content_library": { "type": "bool", "id": 130, "options": { "default": true } }, "enable_client_resources": { "type": "bool", "id": 131, "options": { "default": true } }, "enable_action_library": { "type": "bool", "id": 132, "options": { "default": true } }, "enable_broadcast": { "type": "bool", "id": 140, "options": { "default": true } }, "user_logins_max": { "type": "uint64", "id": 150 }, "expose_contact_info_to_clients": { "type": "bool", "id": 160, "options": { "default": true } }, "expose_client_contact_info_to_internals": { "type": "bool", "id": 161, "options": { "default": true } }, "enable_flow_template_library": { "type": "bool", "id": 170 }, "account_lock_duration": { "type": "uint64", "id": 180 }, "enforce_signature_jwt_validation_for_client_users": { "type": "bool", "id": 190 }, "enable_magic_link": { "type": "bool", "id": 200, "options": { "default": true } }, "enable_magic_link_for_internal_users": { "type": "bool", "id": 201, "options": { "default": true } }, "binder_view_token_timeout": { "type": "uint64", "id": 210, "options": { "default": ********* } }, "binder_view_token_timeout_for_internal_users": { "type": "uint64", "id": 211, "options": { "default": ********* } }, "use_browser_open_jwt": { "type": "bool", "id": 220 }, "user_boards_auto_archive_threshold": { "type": "uint64", "id": 230, "options": { "default": 5000 } }, "terms_group_name": { "type": "string", "id": 240 }, "enable_meet_log_uploading": { "type": "bool", "id": 250 }, "enable_user_data_downloading": { "type": "bool", "id": 260, "options": { "default": false } }, "include_coc_in_signed_file": { "type": "bool", "id": 270 }, "enable_workspace_report_auditing": { "type": "bool", "id": 280, "options": { "default": true } }, "enable_sms_for_email_based_client_user": { "type": "bool", "id": 290 }, "enable_client_distribution_list": { "type": "bool", "id": 300 }, "enable_broadcast_recipient": { "type": "bool", "id": 310 }, "enable_inbox": { "type": "bool", "id": 320 }, "enable_ai": { "type": "bool", "id": 330 }, "enable_client_group_to_internal_users": { "type": "bool", "id": 340 } } }, "GroupType": { "edition": "proto2", "values": { "GROUP_TYPE_ORG": 0, "GROUP_TYPE_TEAM": 10, "GROUP_TYPE_CLIENT_TEAM": 20, "GROUP_TYPE_TEAM_FLEXIBLE": 30 } }, "GroupPlanCode": { "edition": "proto2", "fields": { "plan_code": { "type": "string", "id": 10 }, "plan_quantity": { "type": "uint64", "id": 20 }, "is_capped": { "type": "bool", "id": 30 } } }, "GroupTelephonyDomain": { "edition": "proto2", "fields": { "telephony_domain": { "type": "TelephonyDomain", "id": 10, "options": { "lazy": true } } } }, "CachePlan": { "edition": "proto2", "fields": { "plan_code": { "type": "string", "id": 10 }, "plan_cap": { "type": "UserCap", "id": 20 }, "trial_end_interval": { "type": "uint64", "id": 30 }, "plan_code_free_user": { "type": "string", "id": 200, "options": { "default": "freeuser" } }, "plan_code_trial_user": { "type": "string", "id": 210, "options": { "default": "trialuser" } }, "plan_code_pro_user": { "type": "string", "id": 220, "options": { "default": "prouser" } }, "plan_code_beta_user": { "type": "string", "id": 230, "options": { "default": "betauser" } }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardMemberPrivileges": { "edition": "proto2", "values": { "BOARD_DELETE_OTHERS_MSG": 1, "BOARD_DELETE_OTHERS_COMMENT": 2, "BOARD_DELETE_OTHERS_FILE": 4, "BOARD_DELETE_OTHERS_ANNOTATION": 8, "BOARD_HISTORY_FROM_JOIN": 16, "BOARD_INVITE_BOARD_MEMBER": 32, "BOARD_SHARE_CONTENT": 64, "BOARD_SEND_MSG": 128, "BOARD_ADD_COMMENT": 256, "BOARD_UPLOAD_FILE": 512, "BOARD_SIGN_FILE": 1024, "BOARD_ADD_ANNOTATION": 2048, "BOARD_COPY_TO_OTHER_BINDER": 4096, "BOARD_SAVE_TO_ALBUM": 8192 } }, "GroupUserRoleType": { "edition": "proto2", "values": { "ROLE_TYPE_NORMAL": 10, "ROLE_TYPE_LOCAL": 20, "ROLE_TYPE_GUEST": 30 } }, "ChatPrivilege": { "edition": "proto2", "fields": { "can_start_chat": { "type": "bool", "id": 10, "options": { "default": true } }, "can_invite_from_contact": { "type": "bool", "id": 20, "options": { "default": true } }, "can_invite_by_email": { "type": "bool", "id": 30, "options": { "default": true } }, "can_select_delegate": { "type": "bool", "id": 40, "options": { "default": true } }, "can_send_message": { "type": "bool", "id": 50, "options": { "default": true } }, "can_show_read_receipt": { "type": "bool", "id": 60, "options": { "default": true } }, "can_owner_remove_member": { "type": "bool", "id": 70, "options": { "default": true } } } }, "MeetPrivilege": { "edition": "proto2", "fields": { "can_start_instant_meet": { "type": "bool", "id": 10, "options": { "default": true } }, "can_scheduled_meet": { "type": "bool", "id": 20, "options": { "default": true } }, "can_start_video": { "type": "bool", "id": 30, "options": { "default": true } }, "can_share_screen": { "type": "bool", "id": 40, "options": { "default": true } }, "can_share_file": { "type": "bool", "id": 50, "options": { "default": true } }, "can_do_co_browsing": { "type": "bool", "id": 60, "options": { "default": true } }, "can_turn_on_camera": { "type": "bool", "id": 70, "options": { "default": true } }, "can_download_and_share_meeting_recording": { "type": "bool", "id": 80, "options": { "default": true } } } }, "RelationPrivilege": { "edition": "proto2", "fields": { "can_add_local_user": { "type": "bool", "id": 10, "options": { "default": true } } } }, "FilePrivilege": { "edition": "proto2", "fields": { "can_add_file": { "type": "bool", "id": 10, "options": { "default": true } }, "can_create_clip": { "type": "bool", "id": 20, "options": { "default": true } }, "can_share_publiclink": { "type": "bool", "id": 30, "options": { "default": true } }, "can_share_internally": { "type": "bool", "id": 40, "options": { "default": true } }, "can_create_note": { "type": "bool", "id": 50, "options": { "default": true } }, "can_create_location": { "type": "bool", "id": 70, "options": { "default": true } }, "can_download_file": { "type": "bool", "id": 80, "options": { "default": true } }, "can_create_whiteboard": { "type": "bool", "id": 90, "options": { "default": true } }, "can_add_spot_comment": { "type": "bool", "id": 91, "options": { "default": true } }, "can_create_sign": { "type": "bool", "id": 100, "options": { "default": true } }, "can_sign_files": { "type": "bool", "id": 110, "options": { "default": true } }, "can_create_folder": { "type": "bool", "id": 150, "options": { "default": true } }, "can_create_todo": { "type": "bool", "id": 160, "options": { "default": true } }, "can_create_approval": { "type": "bool", "id": 170, "options": { "default": true } }, "can_create_acknowledge": { "type": "bool", "id": 180, "options": { "default": true } }, "can_create_file_request": { "type": "bool", "id": 190, "options": { "default": true } }, "can_create_meet_request": { "type": "bool", "id": 200, "options": { "default": true } }, "can_create_form": { "type": "bool", "id": 210, "options": { "default": true } }, "can_create_pdf_form": { "type": "bool", "id": 211, "options": { "default": true } }, "can_create_launch_web_app": { "type": "bool", "id": 220, "options": { "default": true } }, "can_rename_file": { "type": "bool", "id": 400, "options": { "default": true } }, "can_move_file": { "type": "bool", "id": 410, "options": { "default": true } } } }, "RoutingPrivilege": { "edition": "proto2", "fields": { "can_accept_acd": { "type": "bool", "id": 10, "options": { "default": true } }, "can_accept_service_request": { "type": "bool", "id": 20, "options": { "default": true } } } }, "GroupPrivilege": { "edition": "proto2", "fields": { "can_audit_user": { "type": "bool", "id": 10, "options": { "default": false } }, "can_manage_subscription_channel": { "type": "bool", "id": 20, "options": { "default": false } }, "can_access_org_report": { "type": "bool", "id": 30, "options": { "default": false } }, "can_manage_content_library": { "type": "bool", "id": 40, "options": { "default": false } }, "can_manage_client_resources": { "type": "bool", "id": 41, "options": { "default": true } }, "can_manage_workflow": { "type": "bool", "id": 50, "options": { "default": false } }, "can_create_shared_flow_template": { "type": "bool", "id": 60, "options": { "default": false } } } }, "ContactPrivilege": { "edition": "proto2", "fields": { "can_access_business_directory": { "type": "bool", "id": 10 }, "can_view_all_clients": { "type": "bool", "id": 20 } } }, "GroupRoleCategory": { "edition": "proto2", "values": { "ROLE_CATEGORY_NONE": 0, "ROLE_CATEGORY_DISTRIBUTION_LIST": 10 } }, "GroupUserRole": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "is_default": { "type": "bool", "id": 11 }, "type": { "type": "GroupUserRoleType", "id": 12 }, "description": { "type": "string", "id": 13 }, "include_all_admins": { "type": "bool", "id": 14 }, "include_all_internal_users": { "type": "bool", "id": 15 }, "category": { "type": "GroupRoleCategory", "id": 20 }, "chat": { "type": "ChatPrivilege", "id": 200 }, "meet": { "type": "MeetPrivilege", "id": 210 }, "relation": { "type": "RelationPrivilege", "id": 220 }, "file": { "type": "FilePrivilege", "id": 240 }, "routing": { "type": "RoutingPrivilege", "id": 250 }, "audit": { "type": "GroupPrivilege", "id": 260 }, "contact": { "type": "ContactPrivilege", "id": 270 }, "role_template": { "type": "string", "id": 230 }, "users_total": { "type": "uint64", "id": 300 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "AsyncTaskStatus": { "edition": "proto2", "values": { "ASYNC_TASK_STATUS_INVALID": 0, "ASYNC_TASK_STATUS_QUEUED": 10, "ASYNC_TASK_STATUS_IN_PROGRESS": 20, "ASYNC_TASK_STATUS_SUCCESS": 30, "ASYNC_TASK_STATUS_FAILED": 40 } }, "AsyncTask": { "edition": "proto2", "fields": { "request": { "type": "ClientRequest", "id": 100 }, "input_resource": { "type": "uint64", "id": 110 }, "actor": { "type": "User", "id": 120 }, "token": { "type": "AccessToken", "id": 130 }, "status": { "type": "AsyncTaskStatus", "id": 200 }, "message": { "type": "string", "id": 202 }, "detail_code": { "type": "ClientResponseDetailCode", "id": 203 }, "detail_message": { "type": "string", "id": 204 }, "processed_items": { "type": "uint64", "id": 210 }, "total_items": { "type": "uint64", "id": 220 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "local_revision": { "type": "uint64", "id": 35 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 41 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "GroupAppConfig": { "edition": "proto2", "fields": { "ios_app_version_recommended": { "type": "uint64", "id": 100 }, "android_app_version_recommended": { "type": "uint64", "id": 200 } } }, "RoutingChannel": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "picture": { "type": "uint64", "id": 12 }, "description": { "type": "string", "id": 20 }, "order_number": { "type": "string", "id": 30 }, "teams": { "rule": "repeated", "type": "UserGroup", "id": 40 }, "include_all_admins": { "type": "bool", "id": 42 }, "user": { "type": "GroupUser", "id": 44 }, "board": { "type": "Board", "id": 46 }, "unassigned_client_only": { "type": "bool", "id": 50 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "keep_deleted": { "type": "bool", "id": 121 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "RoutingWeekday": { "edition": "proto2", "fields": { "day_of_week": { "type": "uint64", "id": 10 }, "start_time": { "type": "string", "id": 20 }, "end_time": { "type": "string", "id": 30 }, "is_close": { "type": "bool", "id": 40 } } }, "RoutingSpecialDay": { "edition": "proto2", "fields": { "date": { "type": "string", "id": 10 }, "start_time": { "type": "string", "id": 20 }, "end_time": { "type": "string", "id": 30 }, "is_close": { "type": "bool", "id": 40 } } }, "RoutingConfig": { "edition": "proto2", "fields": { "acd_max_conns_per_agent": { "type": "uint64", "id": 10 }, "acd_channels": { "rule": "repeated", "type": "RoutingChannel", "id": 20 }, "sr_channels": { "rule": "repeated", "type": "RoutingChannel", "id": 30 }, "weekdays": { "rule": "repeated", "type": "RoutingWeekday", "id": 40 }, "special_days": { "rule": "repeated", "type": "RoutingSpecialDay", "id": 50 }, "acd_connection_timeout": { "type": "uint64", "id": 60, "options": { "default": 90000 } }, "template_messages": { "rule": "repeated", "type": "UserTag", "id": 70 }, "template_messages_sr": { "rule": "repeated", "type": "UserTag", "id": 80 }, "prompt_leave_message": { "type": "string", "id": 200 }, "disable_acd_leave_message": { "type": "bool", "id": 210 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "local_revision": { "type": "uint64", "id": 112 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "GroupUserSetting": { "edition": "proto2", "fields": { "include_all_admins": { "type": "bool", "id": 10 }, "include_all_internal_users": { "type": "bool", "id": 20 } } }, "PropertyType": { "edition": "proto2", "values": { "PROPERTY_TYPE_INVALID": 0, "PROPERTY_TYPE_LIST": 10, "PROPERTY_TYPE_TEXT": 20 } }, "Property": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "type": { "type": "PropertyType", "id": 20 }, "options": { "rule": "repeated", "type": "string", "id": 30 }, "order_number": { "type": "string", "id": 200 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "Group": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "name": { "type": "string", "id": 20 }, "type": { "type": "GroupType", "id": 21, "options": { "default": "GROUP_TYPE_ORG" } }, "alias": { "type": "string", "id": 22 }, "description": { "type": "string", "id": 23 }, "timezone": { "type": "string", "id": 24, "options": { "default": "America/Los_Angeles" } }, "contact": { "type": "GroupContact", "id": 25 }, "group_caps": { "type": "GroupCap", "id": 26 }, "group_settings": { "type": "GroupSetting", "id": 27 }, "support": { "type": "GroupSupport", "id": 28 }, "client_support": { "type": "GroupSupport", "id": 29 }, "plan_code": { "type": "string", "id": 30 }, "plan_code_local": { "type": "string", "id": 32 }, "plan_quantity": { "type": "uint64", "id": 35 }, "plan_quantity_local": { "type": "uint64", "id": 36 }, "members": { "rule": "repeated", "type": "GroupUser", "id": 40 }, "managers": { "rule": "repeated", "type": "GroupUser", "id": 41 }, "managers_setting": { "type": "GroupUserSetting", "id": 42 }, "status": { "type": "GroupSubscriptionStatus", "id": 50 }, "template_name": { "type": "string", "id": 60 }, "customer_id": { "type": "string", "id": 61 }, "coupon_id": { "type": "string", "id": 62 }, "cancel_subscription_at_period_end": { "type": "bool", "id": 65 }, "scheduled_plan_code": { "type": "string", "id": 66 }, "trial_start_time": { "type": "uint64", "id": 51 }, "trial_end_time": { "type": "uint64", "id": 53 }, "commitment_end_time": { "type": "uint64", "id": 56 }, "cancellation_request_time": { "type": "uint64", "id": 57 }, "partner": { "type": "GroupPartner", "id": 70 }, "picture": { "type": "uint64", "id": 81 }, "tac": { "type": "uint64", "id": 82 }, "web_version": { "type": "string", "id": 83 }, "board_owner_privileges": { "type": "uint64", "id": 90, "options": { "default": 16376 } }, "board_editor_privileges": { "type": "uint64", "id": 91, "options": { "default": 16360 } }, "board_viewer_privileges": { "type": "uint64", "id": 92, "options": { "default": 13376 } }, "boards": { "rule": "repeated", "type": "GroupBoard", "id": 101 }, "recurly_signature": { "type": "string", "id": 200 }, "integrations": { "rule": "repeated", "type": "GroupIntegration", "id": 300 }, "teams": { "rule": "repeated", "type": "UserGroup", "id": 310 }, "cap": { "type": "UserCap", "id": 320 }, "resources": { "rule": "repeated", "type": "UserResource", "id": 400 }, "tags": { "rule": "repeated", "type": "UserTag", "id": 500 }, "group_telephony_domain": { "type": "GroupTelephonyDomain", "id": 600 }, "roles": { "rule": "repeated", "type": "GroupUserRole", "id": 700 }, "tasks": { "rule": "repeated", "type": "AsyncTask", "id": 800 }, "app_config": { "type": "GroupAppConfig", "id": 810 }, "routing_config": { "type": "RoutingConfig", "id": 820 }, "invitation_tokens": { "rule": "repeated", "type": "UserQRToken", "id": 900 }, "redeem_urls": { "rule": "repeated", "type": "UserTag", "id": 905 }, "redeem_url_idx": { "type": "uint64", "id": 906 }, "board_properties": { "rule": "repeated", "type": "Property", "id": 910 }, "shared_content_library_group_id": { "type": "string", "id": 1100 }, "total_members": { "type": "uint64", "id": 1200 }, "total_local_members": { "type": "uint64", "id": 1210 }, "total_content_libraries": { "type": "uint64", "id": 1220 }, "total_managers": { "type": "uint64", "id": 1230 }, "istemp": { "type": "bool", "id": 1300 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "PartnerType": { "edition": "proto2", "values": { "PARTNER_TYPE_RESELLER": 0, "PARTNER_TYPE_APP": 10 } }, "PartnerCap": { "edition": "proto2", "fields": { "has_telephony_domain": { "type": "bool", "id": 10 }, "has_sip": { "type": "bool", "id": 20 }, "has_org_meet_usage": { "type": "bool", "id": 30 } } }, "Partner": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "name": { "type": "string", "id": 20 }, "type": { "type": "PartnerType", "id": 25, "options": { "default": "PARTNER_TYPE_RESELLER" } }, "description": { "type": "string", "id": 30 }, "configuration": { "type": "string", "id": 35 }, "contact": { "type": "PartnerContact", "id": 40 }, "partner_caps": { "type": "PartnerCap", "id": 45 }, "allow_trial": { "type": "bool", "id": 60 }, "max_trial_days": { "type": "uint64", "id": 61 }, "upgrade_info": { "type": "string", "id": 70 }, "shared_content_library_group_id": { "type": "string", "id": 80 }, "members": { "rule": "repeated", "type": "PartnerUser", "id": 200 }, "integrations": { "rule": "repeated", "type": "PartnerIntegration", "id": 300 }, "plan_codes": { "rule": "repeated", "type": "PartnerPlanCode", "id": 400 }, "default_plan_code": { "type": "string", "id": 410 }, "default_plan_code_local": { "type": "string", "id": 412 }, "group_templates": { "rule": "repeated", "type": "PartnerGroup", "id": 422 }, "webapps": { "rule": "repeated", "type": "PartnerWebApp", "id": 500 }, "groups": { "rule": "repeated", "type": "PartnerGroup", "id": 600 }, "partner_telephony_domains": { "rule": "repeated", "type": "PartnerTelephonyDomain", "id": 700 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "PartnerUser": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10, "options": { "lazy": true } }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "keep_deleted": { "type": "bool", "id": 121 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "PartnerPlanCode": { "edition": "proto2", "fields": { "plan_code": { "type": "string", "id": 10 }, "plan_cap": { "type": "UserCap", "id": 20 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "PartnerIntegration": { "edition": "proto2", "fields": { "idp_conf": { "type": "SamlIdentityProviderConfig", "id": 10 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "PartnerWebApp": { "edition": "proto2", "fields": { "webapp": { "type": "WebApp", "id": 10, "options": { "lazy": true } }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "PartnerContact": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10, "options": { "lazy": true } } } }, "PartnerGroup": { "edition": "proto2", "fields": { "group": { "type": "Group", "id": 10, "options": { "lazy": true } }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "PartnerTelephonyDomain": { "edition": "proto2", "fields": { "telephony_domain": { "type": "TelephonyDomain", "id": 10 }, "is_default": { "type": "bool", "id": 50 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "SystemSamlService": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "service_providers": { "rule": "repeated", "type": "SamlServiceProvider", "id": 20 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "SamlServiceProvider": { "edition": "proto2", "fields": { "sp_conf": { "type": "SamlServiceProviderConfig", "id": 10 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "SamlIdentityProviderConfig": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "type": { "type": "string", "id": 20 }, "idpid": { "type": "string", "id": 110 }, "spid": { "type": "string", "id": 120 }, "authncontextclassref": { "type": "string", "id": 130 }, "targetparameter": { "type": "string", "id": 140 }, "idploginurl": { "type": "string", "id": 150 }, "assertionconsumerserviceurl": { "type": "string", "id": 155 }, "cert": { "type": "string", "id": 160 }, "nameidformat": { "type": "string", "id": 170 }, "nameid_has_unique_id": { "type": "bool", "id": 172 }, "idpinitiated": { "type": "bool", "id": 180 }, "postprofile": { "type": "bool", "id": 190 }, "authnrequestsigned": { "type": "bool", "id": 200 } } }, "SamlServiceProviderConfig": { "edition": "proto2", "fields": { "idpid": { "type": "string", "id": 10 }, "spid": { "type": "string", "id": 20 }, "authncontextclassref": { "type": "string", "id": 30 }, "targetparameter": { "type": "string", "id": 40 }, "sploginurl": { "type": "string", "id": 50 }, "nameidformat": { "type": "string", "id": 60 }, "encryption": { "type": "bool", "id": 70 }, "algorithm": { "type": "string", "id": 80 }, "type": { "type": "string", "id": 90 }, "cert": { "type": "string", "id": 100 }, "key": { "type": "string", "id": 110 } } }, "UserWebApp": { "edition": "proto2", "fields": { "webapp": { "type": "WebApp", "id": 10, "options": { "lazy": true } }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "WebAppUser": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10, "options": { "lazy": true } }, "partner": { "type": "Partner", "id": 20, "options": { "lazy": true } } } }, "WebAppType": { "edition": "proto2", "values": { "WEBAPP_TYPE_SDK": 0, "WEBAPP_TYPE_SDK_PLUS_API": 10, "WEBAPP_TYPE_BOT": 20, "WEBAPP_TYPE_SUBSCRIPTION": 30, "WEBAPP_TYPE_EMBEDDED": 40, "WEBAPP_TYPE_CONNECTOR": 50, "WEBAPP_TYPE_INBOX_BOT": 60, "WEBAPP_TYPE_AI": 80 } }, "WebApp": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "type": { "type": "WebAppType", "id": 15 }, "app_name": { "type": "string", "id": 20 }, "description": { "type": "string", "id": 30 }, "description_long": { "type": "string", "id": 32 }, "redirect_url": { "type": "string", "id": 40 }, "client_id": { "type": "string", "id": 50 }, "client_secret": { "type": "string", "id": 60 }, "grant_types": { "rule": "repeated", "type": "string", "id": 70 }, "scopes": { "rule": "repeated", "type": "string", "id": 80 }, "picture": { "type": "uint64", "id": 90 }, "category": { "type": "string", "id": 92 }, "verification_token": { "type": "string", "id": 94 }, "verification_url": { "type": "string", "id": 95 }, "callback_url": { "type": "string", "id": 96 }, "template": { "type": "string", "id": 100 }, "template_richtext": { "type": "string", "id": 102 }, "instructions": { "type": "string", "id": 104 }, "note": { "type": "string", "id": 106 }, "desktop_home_url": { "type": "string", "id": 140 }, "mobile_home_url": { "type": "string", "id": 141 }, "status": { "type": "string", "id": 150 }, "nlp_type": { "type": "string", "id": 151 }, "apns_cert": { "type": "string", "id": 200 }, "apns_private_key": { "type": "string", "id": 210 }, "apns_password": { "type": "string", "id": 220 }, "is_apns_cert_expired": { "type": "bool", "id": 211 }, "apns_development": { "type": "bool", "id": 230 }, "voip_cert": { "type": "string", "id": 240 }, "voip_private_key": { "type": "string", "id": 250 }, "voip_password": { "type": "string", "id": 260 }, "is_voip_cert_expired": { "type": "bool", "id": 261 }, "gcm_api_key": { "type": "string", "id": 300 }, "optional_gcm_url": { "type": "string", "id": 310 }, "has_badge": { "type": "bool", "id": 350 }, "ios_app_id": { "type": "string", "id": 800 }, "auth_key_id": { "type": "string", "id": 801 }, "auth_key": { "type": "string", "id": 802 }, "team_id": { "type": "string", "id": 803 }, "bundle_id": { "type": "string", "id": 804 }, "apple_oauth_bundle_id": { "type": "string", "id": 805 }, "android_app_namespace": { "type": "string", "id": 810 }, "android_app_pkg_name": { "type": "string", "id": 811 }, "android_app_fingerprints": { "rule": "repeated", "type": "string", "id": 812 }, "google_oauth_client_id": { "type": "string", "id": 813 }, "owner": { "type": "WebAppUser", "id": 400 }, "sound_default": { "type": "string", "id": 500 }, "sound_meeting_call": { "type": "string", "id": 510 }, "tags": { "rule": "repeated", "type": "UserTag", "id": 600 }, "resources": { "rule": "repeated", "type": "UserResource", "id": 700 }, "vendors": { "rule": "repeated", "type": "NotificationVendor", "id": 900 }, "is_universal": { "type": "bool", "id": 2000 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "NotificationVendor": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "app_id": { "type": "string", "id": 20 }, "app_secret": { "type": "string", "id": 30 }, "authenticate_url": { "type": "string", "id": 40 }, "push_url": { "type": "string", "id": 50 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardResourceType": { "edition": "proto2", "values": { "BOARD_RESOURCE_NONE": 0, "BOARD_RESOURCE_THUMBNAIL": 10, "BOARD_RESOURCE_BACKGROUND": 20, "BOARD_RESOURCE_VECTOR": 30, "BOARD_RESOURCE_EMBEDDED": 40, "BOARD_RESOURCE_VECTOR_THUMBNAIL": 50, "BOARD_RESOURCE_BOARD_AS_PDF": 100, "BOARD_RESOURCE_BOARD_AS_PPT": 110, "BOARD_RESOURCE_RECORDING": 120, "BOARD_RESOURCE_SESSION_AS_VIDEO": 130, "BOARD_RESOURCE_COVER": 140, "BOARD_RESOURCE_BANNER": 141, "BOARD_RESOURCE_AUDIO_RECORDING": 150, "BOARD_RESOURCE_AVATAR": 160, "BOARD_RESOURCE_SIGNATURE_AS_PDF": 170, "BOARD_RESOURCE_SESSION_AUDIO_SPEAKER": 180, "BOARD_RESOURCE_SESSION_MEET_CHAT": 190, "BOARD_RESOURCE_TRANSACTION_AS_PDF": 200, "BOARD_RESOURCE_TRANSACTION_AS_CSV": 201, "BOARD_RESOURCE_SESSION_TRANSCRIPTION": 210, "BOARD_RESOURCE_SESSION_TRANSCRIPTION_VTT": 211, "BOARD_RESOURCE_SESSION_SUMMARY": 212 } }, "BoardResourceStatus": { "edition": "proto2", "values": { "BOARD_RESOURCE_STATUS_NONE": 0, "BOARD_RESOURCE_STATUS_QUEUED": 10, "BOARD_RESOURCE_STATUS_CONVERTING": 20, "BOARD_RESOURCE_STATUS_CONVERTED": 30, "BOARD_RESOURCE_STATUS_CONVERT_FAILED": 40, "BOARD_RESOURCE_STATUS_KEEP_UNCONVERTED": 50, "BOARD_RESOURCE_STATUS_TOO_MANY_PAGES": 60, "BOARD_RESOURCE_STATUS_TOO_LARGE": 70, "BOARD_RESOURCE_STATUS_INVALID_PASSWORD": 80 } }, "BoardResource": { "edition": "proto2", "fields": { "part": { "type": "string", "id": 1 }, "origin": { "type": "string", "id": 5 }, "use_origin_grouping": { "type": "bool", "id": 6 }, "name": { "type": "string", "id": 10 }, "path": { "type": "string", "id": 11 }, "type": { "type": "BoardResourceType", "id": 20 }, "content_type": { "type": "string", "id": 21 }, "content_length": { "type": "uint64", "id": 22 }, "media_length": { "type": "uint64", "id": 23 }, "is_password_protected": { "type": "bool", "id": 24 }, "password": { "type": "string", "id": 25 }, "sha256_hash": { "type": "string", "id": 29 }, "hash": { "type": "string", "id": 30 }, "rotate": { "type": "uint64", "id": 31 }, "width": { "type": "uint64", "id": 32 }, "height": { "type": "uint64", "id": 33 }, "OBSOLETE_creator_sequence": { "type": "uint64", "id": 41 }, "OBSOLETE_creator": { "type": "User", "id": 40, "options": { "lazy": true } }, "creator": { "type": "BoardActor", "id": 42 }, "upload_sequence": { "type": "string", "id": 50 }, "status": { "type": "BoardResourceStatus", "id": 51 }, "converted_pages": { "type": "uint64", "id": 52 }, "total_pages": { "type": "uint64", "id": 53 }, "max_pages": { "type": "uint64", "id": 54 }, "pages": { "rule": "repeated", "type": "BoardPage", "id": 60 }, "session": { "type": "ActionObject", "id": 65 }, "email_subject": { "type": "string", "id": 70 }, "from_email": { "type": "string", "id": 71 }, "from_name": { "type": "string", "id": 72 }, "is_email_empty": { "type": "bool", "id": 73 }, "file": { "type": "string", "id": 80 }, "destination_board": { "type": "Board", "id": 85 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "original_resource_sequence": { "type": "uint64", "id": 155 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardTagName": { "edition": "proto2", "fields": { "meet_agenda": { "type": "string", "id": 10, "options": { "default": "MEET_AGENDA" } }, "meet_document_board_id": { "type": "string", "id": 20, "options": { "default": "MEET_DOCUMENT_BOARD_ID" } } } }, "BoardTag": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "string_value": { "type": "string", "id": 20 }, "uint64_value": { "type": "uint64", "id": 30 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardUserStatus": { "edition": "proto2", "values": { "BOARD_MEMBER": 0, "BOARD_INVITED": 10, "BOARD_MEMBER_REQUESTED": 20, "BOARD_VISITED": 30 } }, "BoardAccessType": { "edition": "proto2", "values": { "BOARD_NO_ACCESS": 0, "BOARD_UPLOAD_ONLY": 90, "BOARD_READ": 100, "BOARD_READ_WRITE": 200, "BOARD_OWNER": 300 } }, "BoardRoutingStatus": { "edition": "proto2", "values": { "ROUTING_STATUS_INVALID": 0, "ROUTING_STATUS_OPEN": 10, "ROUTING_STATUS_OPEN_NO_TIMEOUT": 12, "ROUTING_STATUS_IN_PROGRESS": 20, "ROUTING_STATUS_AGENT_COMPLETE": 30, "ROUTING_STATUS_CLOSE": 40, "ROUTING_STATUS_CLOSE_TIMEOUT": 50, "ROUTING_STATUS_OFFICE_CLOSE": 60, "ROUTING_STATUS_BOT_IN_PROGRESS": 70 } }, "BoardUserAOSM": { "edition": "proto2", "fields": { "timestamp": { "type": "uint64", "id": 100 }, "reply": { "type": "RSVPStatus", "id": 101 } } }, "RequestingUserStatus": { "edition": "proto2", "values": { "REQUESTING_USER_STATUS_INVALID": 0, "REQUESTING_USER_STATUS_PENDING": 10, "REQUESTING_USER_STATUS_APPROVED": 20, "REQUESTING_USER_STATUS_DENIED": 30, "REQUESTING_USER_STATUS_JOINED": 40 } }, "BoardUser": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10, "options": { "lazy": true } }, "group": { "type": "Group", "id": 15, "options": { "lazy": true } }, "status": { "type": "BoardUserStatus", "id": 20 }, "type": { "type": "BoardAccessType", "id": 30 }, "is_notification_off": { "type": "bool", "id": 40 }, "push_notification_level": { "type": "NotificationLevel", "id": 41 }, "type_indication_timestamp": { "type": "uint64", "id": 50 }, "is_alternative_host": { "type": "bool", "id": 60 }, "is_owner_delegate": { "type": "bool", "id": 61 }, "accessed_time": { "type": "uint64", "id": 70 }, "first_unread_feed_timestamp": { "type": "uint64", "id": 71 }, "invited_time": { "type": "uint64", "id": 80 }, "is_invited_in_session": { "type": "bool", "id": 81 }, "action": { "type": "BoardUserAOSM", "id": 82 }, "is_from_team": { "type": "bool", "id": 90 }, "teams": { "rule": "repeated", "type": "uint64", "id": 91 }, "participant_number": { "type": "uint64", "id": 92 }, "requesting_user_status": { "type": "RequestingUserStatus", "id": 210 }, "responder": { "type": "BoardActor", "id": 220 }, "invite_msg": { "type": "string", "id": 230 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "keep_deleted": { "type": "bool", "id": 121 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardSession": { "edition": "proto2", "fields": { "session": { "type": "ActionObject", "id": 10, "options": { "lazy": true } }, "previous_session": { "type": "ActionObject", "id": 20, "options": { "lazy": true } }, "is_not_recurring": { "type": "bool", "id": 200 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "RichTextFormat": { "edition": "proto2", "values": { "TEXT_FORMAT_BBCODE": 10, "TEXT_FORMAT_CARD": 20 } }, "BoardComment": { "edition": "proto2", "fields": { "OBSOLETE_creator_sequence": { "type": "uint64", "id": 11 }, "user": { "type": "User", "id": 10, "options": { "lazy": true } }, "creator": { "type": "BoardActor", "id": 12 }, "roster": { "type": "ActionUserRoster", "id": 60 }, "text": { "type": "string", "id": 20 }, "rich_text": { "type": "string", "id": 21 }, "rich_text_format": { "type": "RichTextFormat", "id": 22 }, "x": { "type": "int64", "id": 30 }, "y": { "type": "int64", "id": 40 }, "position_comment_index": { "type": "uint64", "id": 45 }, "resource": { "type": "uint64", "id": 50 }, "resource_view_token": { "type": "string", "id": 51 }, "resource_path": { "type": "string", "id": 52 }, "resource_length": { "type": "uint64", "id": 53 }, "url_preview": { "type": "string", "id": 65 }, "timestamp": { "type": "uint64", "id": 70 }, "is_modified": { "type": "bool", "id": 71 }, "is_position_comment": { "type": "bool", "id": 80 }, "original_resource_sequence": { "type": "uint64", "id": 155 }, "original_comment": { "type": "uint64", "id": 165 }, "original_page_group": { "type": "string", "id": 166 }, "original_session": { "type": "uint64", "id": 167 }, "original_signature": { "type": "uint64", "id": 168 }, "original_transaction": { "type": "uint64", "id": 169 }, "original_reference_link": { "type": "uint64", "id": 170 }, "custom_info": { "type": "string", "id": 200 }, "social_custom_info": { "type": "string", "id": 201 }, "custom_data": { "type": "string", "id": 205 }, "pin": { "type": "uint64", "id": 220 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 5 }, "revision": { "type": "uint64", "id": 110 }, "revisions": { "rule": "repeated", "type": "BoardComment", "id": 112 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardFolderType": { "edition": "proto2", "values": { "FOLDER_TYPE_NONE": 0, "FOLDER_TYPE_EMAIL": 10, "FOLDER_TYPE_TRANSACTION": 20 } }, "BoardFolder": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "is_recycled": { "type": "bool", "id": 30 }, "folder_type": { "type": "BoardFolderType", "id": 40 }, "folders": { "rule": "repeated", "type": "BoardFolder", "id": 200 }, "files": { "rule": "repeated", "type": "BoardPageGroup", "id": 300 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 110 }, "local_revision": { "type": "uint64", "id": 112 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardPageGroup": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "description": { "type": "string", "id": 11 }, "order_number": { "type": "string", "id": 12 }, "original": { "type": "uint64", "id": 15 }, "original_signature": { "type": "uint64", "id": 16 }, "is_recycled": { "type": "bool", "id": 30 }, "reference_link": { "type": "uint64", "id": 40 }, "thumbnail": { "type": "uint64", "id": 200 }, "first_page": { "type": "uint64", "id": 201 }, "resources": { "rule": "repeated", "type": "BoardResource", "id": 300 }, "total_changes": { "type": "uint64", "id": 330 }, "custom_info": { "type": "string", "id": 340 }, "social_custom_info": { "type": "string", "id": 341 }, "total_used_count": { "type": "uint64", "id": 350 }, "last_used_timestamp": { "type": "uint64", "id": 351 }, "pin": { "type": "uint64", "id": 360 }, "display_name": { "type": "string", "id": 1300 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 110 }, "local_revision": { "type": "uint64", "id": 112 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "original_created_time": { "type": "uint64", "id": 140 }, "last_modified_time": { "type": "uint64", "id": 150 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardViewToken": { "edition": "proto2", "fields": { "token": { "type": "string", "id": 10 }, "expire_timestamp": { "type": "uint64", "id": 20 }, "type": { "type": "BoardAccessType", "id": 30, "options": { "default": "BOARD_READ" } }, "actor": { "type": "BoardUser", "id": 35 }, "actor_file_as": { "type": "User", "id": 36, "options": { "lazy": true } }, "creator": { "type": "BoardActor", "id": 37 }, "pages": { "rule": "repeated", "type": "BoardPage", "id": 40 }, "resources": { "rule": "repeated", "type": "BoardResource", "id": 50 }, "page_groups": { "rule": "repeated", "type": "BoardPageGroup", "id": 60 }, "folders": { "rule": "repeated", "type": "BoardFolder", "id": 70 }, "signatures": { "rule": "repeated", "type": "BoardSignature", "id": 80 }, "is_invitation_token": { "type": "bool", "id": 200 }, "note": { "type": "string", "id": 300 }, "is_outgoing": { "type": "bool", "id": 310 }, "disabled": { "type": "bool", "id": 320 }, "users": { "rule": "repeated", "type": "BoardUser", "id": 410 }, "variables": { "rule": "repeated", "type": "WorkflowVar", "id": 420 }, "is_share_token": { "type": "bool", "id": 500 }, "code": { "type": "string", "id": 510 }, "auto_approve": { "type": "bool", "id": 520 }, "member_only": { "type": "bool", "id": 530 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardPageElement": { "edition": "proto2", "fields": { "svg_tag": { "type": "string", "id": 100 }, "highlight": { "type": "bool", "id": 110 }, "readonly": { "type": "bool", "id": 111 }, "resource": { "type": "uint64", "id": 120 }, "resource_path": { "type": "string", "id": 121 }, "resource_name": { "type": "string", "id": 122 }, "creator_sequence": { "type": "uint64", "id": 131 }, "OBSOLETE_user": { "type": "User", "id": 130, "options": { "lazy": true } }, "creator": { "type": "BoardActor", "id": 132 }, "tags": { "rule": "repeated", "type": "BoardTag", "id": 200 }, "assignments": { "type": "string", "id": 300 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "local_revision": { "type": "uint64", "id": 35 }, "is_deleted": { "type": "bool", "id": 40 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardPageType": { "edition": "proto2", "values": { "PAGE_TYPE_WHITEBOARD": 0, "PAGE_TYPE_NOT_SUPPORTED": 1, "PAGE_TYPE_IMAGE": 10, "PAGE_TYPE_WEB": 20, "PAGE_TYPE_VIDEO": 30, "PAGE_TYPE_AUDIO": 40, "PAGE_TYPE_PDF": 50, "PAGE_TYPE_URL": 60, "PAGE_TYPE_NOTE": 70, "PAGE_TYPE_DESKTOPSHARE": 80, "PAGE_TYPE_GEO": 90, "PAGE_TYPE_ANY": 999 } }, "FormFieldType": { "edition": "proto2", "values": { "FORM_FIELD_TYPE_INVLIAD": 0, "FORM_FIELD_TYPE_PUSH_BUTTON": 10, "FORM_FIELD_TYPE_RADIO_BUTTON": 20, "FORM_FIELD_TYPE_CHECKBOX": 30, "FORM_FIELD_TYPE_TEXT": 40, "FORM_FIELD_TYPE_CHOICE": 50, "FORM_FIELD_TYPE_SIGNATURE": 60 } }, "BoardPageFormField": { "edition": "proto2", "fields": { "type": { "type": "FormFieldType", "id": 100 }, "name": { "type": "string", "id": 110 }, "x": { "type": "uint64", "id": 120 }, "y": { "type": "uint64", "id": 130 }, "width": { "type": "uint64", "id": 140 }, "height": { "type": "uint64", "id": 150 }, "flags": { "type": "uint64", "id": 160 }, "is_multiple_line": { "type": "bool", "id": 200 }, "is_password": { "type": "bool", "id": 210 }, "default_value": { "type": "string", "id": 220 }, "is_multiple_select": { "type": "bool", "id": 300 }, "choices": { "rule": "repeated", "type": "string", "id": 310 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "is_deleted": { "type": "bool", "id": 40 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardPage": { "edition": "proto2", "fields": { "creator_sequence": { "type": "uint64", "id": 80 }, "OBSOLETE_user": { "type": "User", "id": 81, "options": { "lazy": true } }, "creator": { "type": "BoardActor", "id": 82 }, "name": { "type": "string", "id": 90 }, "svg_begining_tag": { "type": "string", "id": 100 }, "contents": { "rule": "repeated", "type": "BoardPageElement", "id": 110 }, "svg_ending_tag": { "type": "string", "id": 120 }, "original_resource_name": { "type": "string", "id": 150 }, "original_page_number": { "type": "uint64", "id": 151 }, "original_resource_upload_sequence": { "type": "string", "id": 153 }, "original_resource_sequence": { "type": "uint64", "id": 155 }, "inherited_original_resource_sequence": { "type": "uint64", "id": 156 }, "is_original_resource_from_page": { "type": "bool", "id": 157 }, "update_if_revision_match": { "type": "uint64", "id": 161 }, "resources": { "rule": "repeated", "type": "BoardResource", "id": 200 }, "longitude": { "type": "string", "id": 215 }, "latitude": { "type": "string", "id": 216 }, "tags": { "rule": "repeated", "type": "BoardTag", "id": 220 }, "page_number": { "type": "string", "id": 230 }, "page_group": { "type": "string", "id": 235 }, "file": { "type": "string", "id": 236 }, "page_type": { "type": "BoardPageType", "id": 240 }, "original": { "type": "uint64", "id": 241 }, "vector": { "type": "uint64", "id": 244 }, "background": { "type": "uint64", "id": 245 }, "thumbnail": { "type": "uint64", "id": 246 }, "text": { "type": "uint64", "id": 290 }, "thumbnail_view_token": { "type": "string", "id": 243 }, "media_length": { "type": "uint64", "id": 242 }, "original_path": { "type": "string", "id": 201 }, "vector_path": { "type": "string", "id": 247 }, "background_path": { "type": "string", "id": 248 }, "thumbnail_path": { "type": "string", "id": 249 }, "width": { "type": "uint64", "id": 250 }, "height": { "type": "uint64", "id": 260 }, "url": { "type": "string", "id": 270 }, "rotate": { "type": "uint64", "id": 280 }, "comments": { "rule": "repeated", "type": "BoardComment", "id": 300 }, "total_comments": { "type": "uint64", "id": 301 }, "total_position_comments": { "type": "uint64", "id": 302 }, "original_session_key": { "type": "string", "id": 320 }, "editor": { "type": "uint64", "id": 400 }, "editor_actor": { "type": "BoardActor", "id": 401 }, "editor_time": { "type": "uint64", "id": 410 }, "editor_type": { "type": "BoardEditorType", "id": 420, "options": { "default": "EDITOR_TYPE_ALL" } }, "description": { "type": "string", "id": 430 }, "vector_thumbnail": { "type": "uint64", "id": 431 }, "vector_thumbnail_path": { "type": "string", "id": 432 }, "form_fields": { "rule": "repeated", "type": "BoardPageFormField", "id": 500 }, "card": { "type": "string", "id": 510 }, "ddrs": { "rule": "repeated", "type": "BoardDataReference", "id": 600 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "local_revision": { "type": "uint64", "id": 35 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 41 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardEditorType": { "edition": "proto2", "values": { "EDITOR_TYPE_ALL": 0, "EDITOR_TYPE_INTERNAL_ONLY": 10, "EDITOR_TYPE_ASSIGNEE_ONLY": 20 } }, "BoardReferenceLink": { "edition": "proto2", "fields": { "board": { "type": "Board", "id": 20, "options": { "lazy": true } }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 5 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardReferenceType": { "edition": "proto2", "values": { "REFERENCE_TYPE_ATTACHEMENT": 0, "REFERENCE_TYPE_SUPPORT_FILE": 5, "REFERENCE_TYPE_FILE_REPLY": 10, "REFERENCE_TYPE_PDF_FORM_FILE": 20 } }, "BoardReference": { "edition": "proto2", "fields": { "board": { "type": "Board", "id": 40, "options": { "lazy": true } }, "creator_sequence": { "type": "uint64", "id": 80 }, "OBSOLETE_user": { "type": "User", "id": 81, "options": { "lazy": true } }, "creator": { "type": "BoardActor", "id": 82 }, "type": { "type": "BoardReferenceType", "id": 90 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 5 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "custom_data": { "type": "string", "id": 200 }, "keep_deleted": { "type": "bool", "id": 121 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardReminder": { "edition": "proto2", "fields": { "creator_sequence": { "type": "uint64", "id": 80 }, "OBSOLETE_user": { "type": "User", "id": 81, "options": { "lazy": true } }, "creator": { "type": "BoardActor", "id": 82 }, "reminder_time": { "type": "uint64", "id": 90 }, "reminder_interval": { "type": "int64", "id": 95 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 5 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "DueTimeFrameType": { "edition": "proto2", "values": { "DUE_TIME_FRAME_DAYS": 0, "DUE_TIME_FRAME_WEEKS": 10 } }, "BoardTodo": { "edition": "proto2", "fields": { "creator_sequence": { "type": "uint64", "id": 80 }, "OBSOLETE_user": { "type": "User", "id": 81, "options": { "lazy": true } }, "creator": { "type": "BoardActor", "id": 82 }, "name": { "type": "string", "id": 90 }, "note": { "type": "string", "id": 100 }, "is_marked": { "type": "bool", "id": 110 }, "assignee_sequence": { "type": "uint64", "id": 120 }, "assignee": { "type": "BoardActor", "id": 121 }, "due_date": { "type": "uint64", "id": 130 }, "due_in_timeframe": { "type": "DueTimeFrameType", "id": 131 }, "exclude_weekends": { "type": "bool", "id": 132 }, "is_completed": { "type": "bool", "id": 150 }, "detail_status": { "type": "DetailStatusCode", "id": 151 }, "is_template": { "type": "bool", "id": 160 }, "template_name": { "type": "string", "id": 161 }, "template_description": { "type": "string", "id": 162 }, "original_client_uuid": { "type": "string", "id": 163 }, "total_used_count": { "type": "uint64", "id": 164 }, "last_used_timestamp": { "type": "uint64", "id": 165 }, "last_modified_time": { "type": "uint64", "id": 166 }, "order_number": { "type": "string", "id": 170 }, "comments": { "rule": "repeated", "type": "BoardComment", "id": 300 }, "resources": { "rule": "repeated", "type": "BoardResource", "id": 350 }, "references": { "rule": "repeated", "type": "BoardReference", "id": 400 }, "reminders": { "rule": "repeated", "type": "BoardReminder", "id": 500 }, "editable_editor_type": { "type": "BoardEditorType", "id": 510, "options": { "default": "EDITOR_TYPE_ALL" } }, "completable_editor_type": { "type": "BoardEditorType", "id": 520, "options": { "default": "EDITOR_TYPE_ALL" } }, "pin": { "type": "uint64", "id": 540 }, "update_if_revision_match": { "type": "uint64", "id": 800 }, "workflow": { "type": "uint64", "id": 1200 }, "step": { "type": "uint64", "id": 1210 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "local_revision": { "type": "uint64", "id": 35 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 45 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardActor": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10, "options": { "lazy": true } }, "group": { "type": "Group", "id": 15, "options": { "lazy": true } }, "roster": { "type": "ActionUserRoster", "id": 20 }, "is_null": { "type": "bool", "id": 30 } } }, "BoardCallStatus": { "edition": "proto2", "values": { "BOARD_CALL_STATUS_INVALID": 0, "BOARD_CALL_STATUS_ENDED": 10, "BOARD_CALL_STATUS_CANCELLED": 20 } }, "BoardCallLog": { "edition": "proto2", "fields": { "from": { "type": "BoardActor", "id": 10 }, "to": { "type": "BoardActor", "id": 20 }, "start_time": { "type": "uint64", "id": 30 }, "end_time": { "type": "uint64", "id": 40 }, "status": { "type": "BoardCallStatus", "id": 50 }, "call_status": { "type": "CallStatus", "id": 60 }, "creator": { "type": "BoardActor", "id": 90 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardSignatureStatus": { "edition": "proto2", "values": { "SIGNATURE_STATUS_INVALID": 0, "SIGNATURE_STATUS_PREPARING": 10, "SIGNATURE_STATUS_EDITING": 15, "SIGNATURE_STATUS_IN_PROGRESS": 20, "SIGNATURE_STATUS_COMPLETED": 30, "SIGNATURE_STATUS_DECLINED": 40 } }, "DetailStatusCode": { "edition": "proto2", "values": { "DETAIL_STATUS_NONE": 0, "DETAIL_STATUS_MARK_AS_COMPLETED": 50 } }, "BoardSigneeStatus": { "edition": "proto2", "values": { "SIGNEE_STATUS_NONE": 0, "SIGNEE_STATUS_SKIPPED": 50 } }, "BoardSignee": { "edition": "proto2", "fields": { "elements": { "rule": "repeated", "type": "uint64", "id": 100 }, "submitted_elements": { "rule": "repeated", "type": "uint64", "id": 110 }, "actor": { "type": "BoardActor", "id": 200 }, "is_submitted": { "type": "bool", "id": 210 }, "status": { "type": "BoardSigneeStatus", "id": 211 }, "msg": { "type": "string", "id": 220 }, "order_number": { "type": "string", "id": 300 }, "requested_time": { "type": "uint64", "id": 400 }, "viewed_time": { "type": "uint64", "id": 410 }, "submitted_time": { "type": "uint64", "id": 420 }, "submitted_ip": { "type": "string", "id": 430 }, "submitted_via": { "type": "ObjectFeedViaSource", "id": 440 }, "signature": { "type": "uint64", "id": 500 }, "initials_text": { "type": "string", "id": 502 }, "signature_style": { "type": "SignatureStyle", "id": 504 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 50 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardSignature": { "edition": "proto2", "fields": { "creator": { "type": "BoardActor", "id": 100 }, "status": { "type": "BoardSignatureStatus", "id": 110 }, "detail_status": { "type": "DetailStatusCode", "id": 111 }, "signees": { "rule": "repeated", "type": "BoardSignee", "id": 120 }, "enable_preparation": { "type": "bool", "id": 131 }, "editor": { "type": "BoardActor", "id": 132 }, "doc_id": { "type": "string", "id": 190 }, "name": { "type": "string", "id": 200 }, "original_name": { "type": "string", "id": 201 }, "is_from_pdf_form": { "type": "bool", "id": 202 }, "pages": { "rule": "repeated", "type": "BoardPage", "id": 210 }, "original": { "type": "uint64", "id": 220 }, "coc": { "type": "uint64", "id": 222 }, "original_with_coc": { "type": "uint64", "id": 224 }, "file": { "type": "string", "id": 225 }, "description": { "type": "string", "id": 230 }, "order_number": { "type": "string", "id": 300 }, "is_template": { "type": "bool", "id": 350 }, "template_name": { "type": "string", "id": 351 }, "template_description": { "type": "string", "id": 352 }, "original_client_uuid": { "type": "string", "id": 360 }, "sign_by_order": { "type": "bool", "id": 400, "options": { "default": true } }, "started_time": { "type": "uint64", "id": 410 }, "started_by_ip": { "type": "string", "id": 411 }, "started_via": { "type": "ObjectFeedViaSource", "id": 412 }, "ended_time": { "type": "uint64", "id": 420 }, "due_date": { "type": "uint64", "id": 430 }, "due_in_timeframe": { "type": "DueTimeFrameType", "id": 431 }, "exclude_weekends": { "type": "bool", "id": 432 }, "references": { "rule": "repeated", "type": "BoardReference", "id": 440 }, "resources": { "rule": "repeated", "type": "BoardResource", "id": 500 }, "original_resource_sequence": { "type": "uint64", "id": 510 }, "pin": { "type": "uint64", "id": 520 }, "total_used_count": { "type": "uint64", "id": 530 }, "last_used_timestamp": { "type": "uint64", "id": 531 }, "last_modified_time": { "type": "uint64", "id": 600 }, "workflow": { "type": "uint64", "id": 1200 }, "step": { "type": "uint64", "id": 1210 }, "is_workflow_source": { "type": "bool", "id": 1215 }, "ddrs": { "rule": "repeated", "type": "BoardDataReference", "id": 1220 }, "has_custom_folder": { "type": "bool", "id": 1240 }, "custom_folder_name": { "type": "string", "id": 1241 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "local_revision": { "type": "uint64", "id": 35 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 50 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardUserActivity": { "edition": "proto2", "fields": { "actor": { "type": "BoardActor", "id": 100 }, "timestamp": { "type": "uint64", "id": 200 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 41 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "TransactionActionStyle": { "edition": "proto2", "values": { "ACTION_STYLE_BUTTON": 0, "ACTION_STYLE_CHECKBOX": 10 } }, "TransactionStepType": { "edition": "proto2", "values": { "STEP_TYPE_GENERIC": 0, "STEP_TYPE_DOCUSIGN_CC": 100, "STEP_TYPE_REVIEWER": 200 } }, "TransactionStepStatus": { "edition": "proto2", "values": { "STEP_STATUS_INITIAL": 0, "STEP_STATUS_PENDING": 10, "STEP_STATUS_COMPLETED": 20, "STEP_STATUS_CANCELED": 30, "STEP_STATUS_SKIPPED": 40, "STEP_STATUS_REOPENED": 50 } }, "TransactionStatus": { "edition": "proto2", "values": { "TRANSACTION_STATUS_EDITING": 5, "TRANSACTION_STATUS_ACTIVE": 10, "TRANSACTION_STATUS_INACTIVE": 20, "TRANSACTION_STATUS_COMPLETED": 30, "TRANSACTION_STATUS_CANCELED": 40, "TRANSACTION_STATUS_EXPIRED": 50 } }, "TransactionActionLog": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 100 }, "click_btn_id": { "type": "string", "id": 200 }, "click_btn_timestamp": { "type": "uint64", "id": 300 }, "click_btn_from_ip": { "type": "string", "id": 400 }, "custom_action": { "type": "string", "id": 410 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 50 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "TransactionElement": { "edition": "proto2", "fields": { "string_value": { "type": "string", "id": 100 }, "creator": { "type": "BoardActor", "id": 200 }, "order_number": { "type": "string", "id": 210 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "local_revision": { "type": "uint64", "id": 35 }, "is_deleted": { "type": "bool", "id": 40 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "TransactionStep": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 100 }, "assignee": { "type": "BoardActor", "id": 200 }, "action_style": { "type": "TransactionActionStyle", "id": 290 }, "actions": { "type": "string", "id": 300 }, "action_logs": { "rule": "repeated", "type": "TransactionActionLog", "id": 400 }, "order_number": { "type": "string", "id": 500 }, "step_group": { "type": "string", "id": 510 }, "type": { "type": "TransactionStepType", "id": 590 }, "status": { "type": "TransactionStepStatus", "id": 600, "options": { "default": "STEP_STATUS_INITIAL" } }, "viewed_time": { "type": "uint64", "id": 700 }, "custom_data": { "type": "string", "id": 710 }, "contents": { "rule": "repeated", "type": "TransactionElement", "id": 720 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "local_revision": { "type": "uint64", "id": 35 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 50 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "TransactionStepGroup": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 100 }, "completion_type": { "type": "StepGroupCompletionType", "id": 200 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 50 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "StepGroupCompletionType": { "edition": "proto2", "values": { "STEP_GROUP_COMPLETION_TYPE_ALL": 0, "STEP_GROUP_COMPLETION_TYPE_ONE": 10, "STEP_GROUP_COMPLETION_TYPE_MAJORITY": 20 } }, "TransactionType": { "edition": "proto2", "values": { "TRANSACTION_TYPE_GENERIC": 0, "TRANSACTION_TYPE_APPROVAL": 10, "TRANSACTION_TYPE_ACKNOWLEDGE": 20, "TRANSACTION_TYPE_FILE_REQUEST": 30, "TRANSACTION_TYPE_MEET_REQUEST": 40, "TRANSACTION_TYPE_FORM_REQUEST": 50, "TRANSACTION_TYPE_TIME_BOOKING": 60, "TRANSACTION_TYPE_PDF_FORM": 70, "TRANSACTION_TYPE_DOCUSIGN": 75, "TRANSACTION_TYPE_WEBHOOK": 76, "TRANSACTION_TYPE_LAUNCH_WEB_APP": 77, "TRANSACTION_TYPE_INTEGRATION": 78, "TRANSACTION_TYPE_TODO": 79, "TRANSACTION_TYPE_DECISION": 80, "TRANSACTION_TYPE_AWAIT": 90 } }, "BoardTransaction": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 90 }, "title": { "type": "string", "id": 100 }, "sub_title": { "type": "string", "id": 101 }, "content": { "type": "string", "id": 110 }, "content_format": { "type": "RichTextFormat", "id": 111 }, "display_status": { "type": "string", "id": 120 }, "is_active": { "type": "bool", "id": 130 }, "expiration_date": { "type": "uint64", "id": 140 }, "is_expired": { "type": "bool", "id": 141 }, "due_in_timeframe": { "type": "DueTimeFrameType", "id": 142 }, "exclude_weekends": { "type": "bool", "id": 143 }, "status": { "type": "TransactionStatus", "id": 150, "options": { "default": "TRANSACTION_STATUS_ACTIVE" } }, "detail_status": { "type": "DetailStatusCode", "id": 151 }, "callback_url": { "type": "string", "id": 160 }, "is_template": { "type": "bool", "id": 170 }, "template_name": { "type": "string", "id": 171 }, "template_description": { "type": "string", "id": 172 }, "original_client_uuid": { "type": "string", "id": 180 }, "type": { "type": "TransactionType", "id": 190 }, "creator": { "type": "BoardActor", "id": 200 }, "enable_preparation": { "type": "bool", "id": 211 }, "editor": { "type": "BoardActor", "id": 212 }, "enable_decline": { "type": "bool", "id": 220 }, "steps": { "rule": "repeated", "type": "TransactionStep", "id": 500 }, "step_groups": { "rule": "repeated", "type": "TransactionStepGroup", "id": 510 }, "step_timeout": { "type": "uint64", "id": 501 }, "references": { "rule": "repeated", "type": "BoardReference", "id": 600 }, "view_tokens": { "rule": "repeated", "type": "BoardViewToken", "id": 700 }, "card": { "type": "string", "id": 800 }, "card_description": { "type": "string", "id": 801 }, "custom_data": { "type": "string", "id": 810 }, "sub_type": { "type": "string", "id": 820 }, "resources": { "rule": "repeated", "type": "BoardResource", "id": 830 }, "reminders": { "rule": "repeated", "type": "BoardReminder", "id": 840 }, "custom_result": { "type": "string", "id": 850 }, "segments": { "rule": "repeated", "type": "TransactionElement", "id": 860 }, "pin": { "type": "uint64", "id": 900 }, "total_used_count": { "type": "uint64", "id": 920 }, "last_used_timestamp": { "type": "uint64", "id": 921 }, "last_modified_time": { "type": "uint64", "id": 923 }, "workflow": { "type": "uint64", "id": 1200 }, "step": { "type": "uint64", "id": 1210 }, "is_workflow_source": { "type": "bool", "id": 1220 }, "original": { "type": "uint64", "id": 1310 }, "original_masked": { "type": "uint64", "id": 1311 }, "original_csv": { "type": "uint64", "id": 1312 }, "original_csv_masked": { "type": "uint64", "id": 1313 }, "has_custom_folder": { "type": "bool", "id": 1330 }, "custom_folder_name": { "type": "string", "id": 1331 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "local_revision": { "type": "uint64", "id": 35 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 50 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "SocialType": { "edition": "proto2", "values": { "SOCIAL_TYPE_INVALID": 0, "SOCIAL_TYPE_WECHAT": 10, "SOCIAL_TYPE_LINE": 20, "SOCIAL_TYPE_WHATSAPP": 30 } }, "RSVPStatus": { "edition": "proto2", "values": { "RSVP_NEEDS_ACTION": 0, "RSVP_ACCEPTED": 10, "RSVP_DECLINED": 20, "RSVP_TENTATIVE": 30 } }, "RSVPReply": { "edition": "proto2", "fields": { "from_email": { "type": "string", "id": 100 }, "original": { "type": "uint64", "id": 110 }, "timezone": { "type": "string", "id": 600, "options": { "default": "America/Los_Angeles" } }, "dtstart": { "type": "uint64", "id": 610 }, "dtend": { "type": "uint64", "id": 611 }, "rrule": { "type": "string", "id": 620 }, "partstat": { "type": "RSVPStatus", "id": 700 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "local_revision": { "type": "uint64", "id": 35 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 41 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardUserRSVP": { "edition": "proto2", "fields": { "actor": { "type": "BoardActor", "id": 100 }, "replies": { "rule": "repeated", "type": "RSVPReply", "id": 110 }, "resources": { "rule": "repeated", "type": "BoardResource", "id": 200 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "local_revision": { "type": "uint64", "id": 35 }, "is_deleted": { "type": "bool", "id": 40 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardPin": { "edition": "proto2", "fields": { "actor": { "type": "BoardActor", "id": 100 }, "board": { "type": "Board", "id": 110, "options": { "lazy": true } }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 50 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardDataReference": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 100 }, "value": { "type": "string", "id": 120 }, "label": { "type": "string", "id": 130 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 50 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "WorkflowType": { "edition": "proto2", "values": { "WORKFLOW_TYPE_DEFAULT": 0, "WORKFLOW_TYPE_FLEXIBLE": 10 } }, "WorkflowStatus": { "edition": "proto2", "values": { "WORKFLOW_STATUS_INITIAL": 0, "WORKFLOW_STATUS_RUNNING": 10, "WORKFLOW_STATUS_COMPLETED": 20, "WORKFLOW_STATUS_CANCELED": 30, "WORKFLOW_STATUS_FAULTED": 40 } }, "WorkflowVarParameter": { "edition": "proto2", "values": { "VAR_PARAM_USER_ID_TO_GROUP_ID": 10, "VAR_PARAM_QUERY_REPEATED": 20, "VAR_PARAM_EXPEND_ARRAY": 30, "VAR_PARAM_FORMAT": 40 } }, "WorkflowVarParam": { "edition": "proto2", "fields": { "name": { "type": "WorkflowVarParameter", "id": 10 }, "string_value": { "type": "string", "id": 20 }, "uint64_value": { "type": "uint64", "id": 30 } } }, "WorkflowVarType": { "edition": "proto2", "values": { "VAR_TYPE_DEFAULT": 0, "VAR_TYPE_ALL_FILES": 100, "VAR_TYPE_FORM_PDF": 101, "VAR_TYPE_FORM_CSV": 102, "VAR_TYPE_WORKSPACE_VARIABLE": 200, "VAR_TYPE_WORKSPACE_EMAIL": 210, "VAR_TYPE_WORKSPACE_URL": 220 } }, "WorkflowVar": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "string_value": { "type": "string", "id": 20 }, "default_value": { "type": "string", "id": 21 }, "default_actor": { "type": "BoardActor", "id": 22 }, "label": { "type": "string", "id": 30 }, "refer_to": { "type": "string", "id": 40 }, "resolved": { "type": "bool", "id": 41 }, "type": { "type": "string", "id": 50 }, "custom_data": { "type": "string", "id": 60 }, "params": { "rule": "repeated", "type": "WorkflowVarParam", "id": 70 }, "var_type": { "type": "WorkflowVarType", "id": 80 }, "step_uuid": { "type": "string", "id": 81 }, "timeout": { "type": "uint64", "id": 82 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "WorkflowStepType": { "edition": "proto2", "values": { "WORKFLOW_STEP_TYPE_INVALID": 0, "WORKFLOW_STEP_TYPE_MILESTONE": 1, "WORKFLOW_STEP_TYPE_AB": 2, "WORKFLOW_STEP_TYPE_AUTOMATION": 3, "WORKFLOW_STEP_TYPE_CB": 4, "WORKFLOW_STEP_TYPE_SHADOW_FLOW": 5, "WORKFLOW_STEP_TYPE_SEND_FILE": 10, "WORKFLOW_STEP_TYPE_TRANSACTION": 19, "WORKFLOW_STEP_TYPE_FORM_REQUEST": 20, "WORKFLOW_STEP_TYPE_FILE_REQUEST": 30, "WORKFLOW_STEP_TYPE_MEET_REQUEST": 31, "WORKFLOW_STEP_TYPE_APPROVAL": 40, "WORKFLOW_STEP_TYPE_ACKNOWLEDGE": 41, "WORKFLOW_STEP_TYPE_SIGNATURE": 50, "WORKFLOW_STEP_TYPE_TODO": 60, "WORKFLOW_STEP_TYPE_TIME_BOOKING": 65, "WORKFLOW_STEP_TYPE_DOCUSIGN": 70, "WORKFLOW_STEP_TYPE_WEBHOOK": 71, "WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP": 72, "WORKFLOW_STEP_TYPE_INTEGRATION": 73, "WORKFLOW_STEP_TYPE_TODO_TRANSACTION": 74, "WORKFLOW_STEP_TYPE_DECISION": 75, "WORKFLOW_STEP_TYPE_AWAIT": 76, "WORKFLOW_STEP_TYPE_PDF_FORM": 77, "WORKFLOW_STEP_TYPE_SHADOW_ACTION": 78 } }, "WorkflowStepStatus": { "edition": "proto2", "values": { "WORKFLOW_STEP_STATUS_INITIAL": 0, "WORKFLOW_STEP_STATUS_PREPARING": 5, "WORKFLOW_STEP_STATUS_READY": 6, "WORKFLOW_STEP_STATUS_STARTED": 10, "WORKFLOW_STEP_STATUS_COMPLETED": 20, "WORKFLOW_STEP_STATUS_CANCELED": 30, "WORKFLOW_STEP_STATUS_FAULTED": 40, "WORKFLOW_STEP_STATUS_SKIPPED": 50, "WORKFLOW_STEP_STATUS_PRECONDITION_NOT_MET": 60 } }, "WorkflowOutgoing": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "feed": { "type": "ObjectFeed", "id": 20 }, "payload": { "type": "string", "id": 30 }, "webhooks": { "type": "string", "id": 40 }, "triggers": { "type": "string", "id": 50 }, "integrations": { "type": "string", "id": 60 }, "integrations_ext": { "type": "string", "id": 61 }, "queue_type": { "type": "WorkflowOutgoingQueueType", "id": 70 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "WorkflowOutgoingQueueType": { "edition": "proto2", "values": { "QUEUE_TYPE_GROUP": 0, "QUEUE_TYPE_BOARD": 10 } }, "WorkflowConditionCategory": { "edition": "proto2", "values": { "CONDITION_DEFAULT": 0, "CONDITION_EXECUTION_ORDER": 10, "CONDITION_EXECUTION_BRANCH": 20, "CONDITION_EXECUTION_MIXED": 30 } }, "WorkflowCondition": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "expression": { "type": "string", "id": 20 }, "result": { "type": "string", "id": 30 }, "original_result": { "type": "string", "id": 31 }, "resolved": { "type": "bool", "id": 40 }, "category": { "type": "WorkflowConditionCategory", "id": 50 }, "conditional_step": { "type": "string", "id": 200 }, "waiting_steps": { "rule": "repeated", "type": "string", "id": 210 }, "waiting_milestones": { "rule": "repeated", "type": "string", "id": 220 }, "waiting_conditional_steps": { "rule": "repeated", "type": "string", "id": 230 }, "variables": { "rule": "repeated", "type": "WorkflowVar", "id": 310 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "WorkflowActionType": { "edition": "proto2", "values": { "WORKFLOW_ACTION_TYPE_INVALID": 0, "WORKFLOW_ACTION_TYPE_SMS": 10, "WORKFLOW_ACTION_TYPE_EMAIL": 20, "WORKFLOW_ACTION_TYPE_COMMENT": 30, "WORKFLOW_ACTION_TYPE_APPROVAL": 40, "WORKFLOW_ACTION_TYPE_ACKNOWLEDGE": 41, "WORKFLOW_ACTION_TYPE_SIGNATURE": 50, "WORKFLOW_ACTION_TYPE_TODO": 60 } }, "WorkflowActionStatus": { "edition": "proto2", "values": { "WORKFLOW_ACTION_STATUS_INITIAL": 0, "WORKFLOW_ACTION_STATUS_RUNNING": 10, "WORKFLOW_ACTION_STATUS_COMPLETED": 20, "WORKFLOW_ACTION_STATUS_FAULTED": 30 } }, "WorkflowAction": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "status": { "type": "WorkflowActionStatus", "id": 20 }, "type": { "type": "WorkflowActionType", "id": 40 }, "board_id": { "type": "string", "id": 60 }, "board_view_token": { "type": "string", "id": 70 }, "input": { "type": "BoardReference", "id": 80 }, "destination_board_id": { "type": "string", "id": 90 }, "output": { "type": "BoardReference", "id": 200 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "WorkflowCheckpoint": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "feed": { "type": "ObjectFeed", "id": 20 }, "action": { "type": "WorkflowAction", "id": 30 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "WorkflowStep": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "description": { "type": "string", "id": 11 }, "order_number": { "type": "string", "id": 20 }, "is_parallel_with_prev_step": { "type": "bool", "id": 25 }, "status": { "type": "WorkflowStepStatus", "id": 30 }, "enable_preparation": { "type": "bool", "id": 31 }, "editor": { "type": "BoardActor", "id": 32 }, "hide_before_started": { "type": "bool", "id": 33 }, "type": { "type": "WorkflowStepType", "id": 40 }, "board_id": { "type": "string", "id": 60 }, "board_view_token": { "type": "string", "id": 70 }, "input": { "type": "BoardReference", "id": 80 }, "output": { "type": "BoardReference", "id": 200 }, "outgoings": { "rule": "repeated", "type": "WorkflowOutgoing", "id": 300 }, "outbound_data": { "type": "string", "id": 310 }, "inbound_data": { "type": "string", "id": 320 }, "parent_step": { "type": "string", "id": 400 }, "is_optional": { "type": "bool", "id": 500 }, "preconditions": { "rule": "repeated", "type": "WorkflowCondition", "id": 600 }, "condition": { "type": "WorkflowCondition", "id": 650 }, "checkpoints": { "rule": "repeated", "type": "WorkflowCheckpoint", "id": 700 }, "is_holding": { "type": "bool", "id": 800 }, "custom_data": { "type": "string", "id": 900 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "WorkflowObject": { "edition": "proto2", "fields": { "board": { "type": "Board", "id": 10, "options": { "lazy": true } }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "WorkflowMilestone": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "description": { "type": "string", "id": 11 }, "order_number": { "type": "string", "id": 20 }, "processed_steps": { "type": "uint64", "id": 30 }, "total_steps": { "type": "uint64", "id": 40 }, "custom_data": { "type": "string", "id": 50 }, "preconditions": { "rule": "repeated", "type": "WorkflowCondition", "id": 60 }, "reverse_workflow_order": { "type": "bool", "id": 70 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "WorkflowTriggerType": { "edition": "proto2", "values": { "WORKFLOW_TRIGGER_TYPE_NONE": 0, "WORKFLOW_TRIGGER_TYPE_WEBHOOK": 10, "WORKFLOW_TRIGGER_TYPE_APPS": 20 } }, "WorkflowTriggerError": { "edition": "proto2", "values": { "WORKFLOW_TRIGGER_ERROR_NONE": 0, "WORKFLOW_TRIGGER_ERROR_INVALID_CREDENTIAL": 10, "WORKFLOW_TRIGGER_ERROR_SYSTEM": 20 } }, "BoardWorkflow": { "edition": "proto2", "fields": { "creator": { "type": "BoardActor", "id": 100 }, "user": { "type": "BoardActor", "id": 101 }, "template_board_id": { "type": "string", "id": 110 }, "original_template_board_id": { "type": "string", "id": 111 }, "update_from_original": { "type": "bool", "id": 112 }, "template_name": { "type": "string", "id": 120 }, "name": { "type": "string", "id": 200 }, "description": { "type": "string", "id": 210 }, "welcome_msg": { "type": "string", "id": 211 }, "welcome_msg_comment_sequence": { "type": "uint64", "id": 212 }, "error_msg": { "type": "string", "id": 213 }, "error_code": { "type": "ClientResponseCode", "id": 214 }, "status": { "type": "WorkflowStatus", "id": 220 }, "definition": { "type": "string", "id": 230 }, "nodes": { "type": "string", "id": 240 }, "objects": { "rule": "repeated", "type": "WorkflowObject", "id": 250 }, "processed_steps": { "type": "uint64", "id": 300 }, "total_steps": { "type": "uint64", "id": 310 }, "current_step": { "type": "uint64", "id": 320 }, "is_template": { "type": "bool", "id": 400 }, "is_active": { "type": "bool", "id": 401 }, "process_in_parallel": { "type": "bool", "id": 402 }, "type": { "type": "WorkflowType", "id": 403 }, "variables": { "rule": "repeated", "type": "WorkflowVar", "id": 410 }, "steps": { "rule": "repeated", "type": "WorkflowStep", "id": 420 }, "milestones": { "rule": "repeated", "type": "WorkflowMilestone", "id": 430 }, "outgoings": { "rule": "repeated", "type": "WorkflowOutgoing", "id": 500 }, "total_used_count": { "type": "uint64", "id": 600 }, "last_used_timestamp": { "type": "uint64", "id": 610 }, "last_modified_time": { "type": "uint64", "id": 611 }, "completed_time": { "type": "uint64", "id": 612 }, "original_signature": { "type": "uint64", "id": 625 }, "original_transaction": { "type": "uint64", "id": 626 }, "reference_id": { "type": "string", "id": 700 }, "trigger_type": { "type": "WorkflowTriggerType", "id": 800 }, "trigger_activation_time": { "type": "uint64", "id": 810 }, "trigger_error": { "type": "WorkflowTriggerError", "id": 820 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "local_revision": { "type": "uint64", "id": 35 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 50 }, "internal_board": { "type": "Board", "id": 2222 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardType": { "edition": "proto2", "values": { "BOARD_TYPE_DEFAULT": 0, "BOARD_TYPE_WORKFLOW": 100, "BOARD_TYPE_WORKFLOW_TEMPLATE": 200, "BOARD_TYPE_CONTENT_LIBRARY_ACTION": 300, "BOARD_TYPE_CONTENT_LIBRARY_FILE": 400, "BOARD_TYPE_CONTENT_LIBRARY_MILESTONE": 800, "BOARD_TYPE_SCHEDULE": 500, "BOARD_TYPE_SELF_SERVICE_TEMPLATE": 600, "BOARD_TYPE_WORKFLOW_TEMPLATE_FOLDER": 700, "BOARD_TYPE_BROADCAST": 900, "BOARD_TYPE_AI": 1000 } }, "BoardProperty": { "edition": "proto2", "fields": { "group_sequence": { "type": "uint64", "id": 100 }, "name": { "type": "string", "id": 110 }, "value": { "type": "string", "id": 120 }, "sequence": { "type": "uint64", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "revision": { "type": "uint64", "id": 30 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 50 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "WaitingRoomAudience": { "edition": "proto2", "values": { "WAITING_ROOM_AUDIENCE_GUEST": 0, "WAITING_ROOM_AUDIENCE_ALL": 10 } }, "Board": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "client_uuid": { "type": "string", "id": 20 }, "islive": { "type": "bool", "id": 50 }, "isconversation": { "type": "bool", "id": 51 }, "isdefault": { "type": "bool", "id": 52 }, "isnote": { "type": "bool", "id": 53 }, "istemp": { "type": "bool", "id": 54 }, "is_restricted": { "type": "bool", "id": 55 }, "is_team": { "type": "bool", "id": 56 }, "use_member_name_as_name": { "type": "bool", "id": 57 }, "use_member_avatar_as_cover": { "type": "bool", "id": 58 }, "is_relation": { "type": "bool", "id": 61 }, "is_transaction": { "type": "bool", "id": 62 }, "is_inactive": { "type": "bool", "id": 63 }, "inactive_time": { "type": "uint64", "id": 64 }, "is_bot_relation": { "type": "bool", "id": 65 }, "is_duplicate": { "type": "bool", "id": 70 }, "is_file": { "type": "bool", "id": 71 }, "is_todo": { "type": "bool", "id": 72 }, "is_signature": { "type": "bool", "id": 73 }, "is_flexible": { "type": "bool", "id": 80 }, "is_external": { "type": "bool", "id": 81 }, "iscall": { "type": "bool", "id": 82 }, "is_inbox": { "type": "bool", "id": 83 }, "is_personal_room": { "type": "bool", "id": 84 }, "milliseconds_personal_room_waiting_timeout": { "type": "uint64", "id": 85 }, "is_app_subscription": { "type": "bool", "id": 90 }, "is_shadow_flow": { "type": "bool", "id": 91 }, "is_owner_delegate_enabled": { "type": "bool", "id": 95 }, "social_type": { "type": "SocialType", "id": 96 }, "is_client_editing_enabled": { "type": "bool", "id": 97 }, "name": { "type": "string", "id": 100 }, "description": { "type": "string", "id": 101 }, "type": { "type": "BoardType", "id": 102 }, "workspace_id": { "type": "string", "id": 103 }, "invite_code": { "type": "string", "id": 104 }, "thumbnail_need_migrate": { "type": "string", "id": 110 }, "thumbnail": { "type": "uint64", "id": 111 }, "thumbnail_view_token": { "type": "string", "id": 112 }, "banner": { "type": "uint64", "id": 113 }, "banner_mobile": { "type": "uint64", "id": 114 }, "board_pdf": { "type": "uint64", "id": 120 }, "board_ppt": { "type": "uint64", "id": 121 }, "board_recording": { "type": "uint64", "id": 122 }, "thumbnail_source_page": { "type": "uint64", "id": 130 }, "thumbnail_source_resource": { "type": "uint64", "id": 131 }, "email_address": { "type": "string", "id": 140 }, "phone_number": { "type": "string", "id": 150 }, "is_acd": { "type": "bool", "id": 161 }, "is_service_request": { "type": "bool", "id": 162 }, "routing_status": { "type": "BoardRoutingStatus", "id": 163 }, "routing_channel": { "type": "uint64", "id": 164 }, "is_channel_subscription": { "type": "bool", "id": 170 }, "is_content_library": { "type": "bool", "id": 171 }, "is_client_resources": { "type": "bool", "id": 172 }, "enable_waiting_room": { "type": "bool", "id": 175 }, "waiting_room_audience": { "type": "WaitingRoomAudience", "id": 176 }, "teams": { "rule": "repeated", "type": "BoardUser", "id": 180 }, "pages": { "rule": "repeated", "type": "BoardPage", "id": 200 }, "resources": { "rule": "repeated", "type": "BoardResource", "id": 210 }, "tags": { "rule": "repeated", "type": "BoardTag", "id": 220 }, "page_groups": { "rule": "repeated", "type": "BoardPageGroup", "id": 240 }, "view_tokens": { "rule": "repeated", "type": "BoardViewToken", "id": 250 }, "folders": { "rule": "repeated", "type": "BoardFolder", "id": 260 }, "reference_links": { "rule": "repeated", "type": "BoardReferenceLink", "id": 270 }, "users": { "rule": "repeated", "type": "BoardUser", "id": 300 }, "owner": { "type": "uint64", "id": 310 }, "user_rsvps": { "rule": "repeated", "type": "BoardUserRSVP", "id": 320 }, "sessions": { "rule": "repeated", "type": "BoardSession", "id": 400 }, "calls": { "rule": "repeated", "type": "BoardCallLog", "id": 410 }, "comments": { "rule": "repeated", "type": "BoardComment", "id": 700 }, "todos": { "rule": "repeated", "type": "BoardTodo", "id": 710 }, "signatures": { "rule": "repeated", "type": "BoardSignature", "id": 720 }, "transactions": { "rule": "repeated", "type": "BoardTransaction", "id": 725 }, "workflows": { "rule": "repeated", "type": "BoardWorkflow", "id": 750 }, "feeds": { "rule": "repeated", "type": "ObjectFeed", "id": 800 }, "waiting_users": { "rule": "repeated", "type": "ActionUserRoster", "id": 820 }, "user_activities": { "rule": "repeated", "type": "BoardUserActivity", "id": 830 }, "user_activities_last": { "type": "uint64", "id": 840 }, "pin_editor_type": { "type": "BoardEditorType", "id": 860 }, "pins": { "rule": "repeated", "type": "BoardPin", "id": 870 }, "reminders": { "rule": "repeated", "type": "BoardReminder", "id": 880 }, "requesting_users": { "rule": "repeated", "type": "BoardUser", "id": 890 }, "total_pages": { "type": "uint64", "id": 900 }, "total_members": { "type": "uint64", "id": 910 }, "total_comments": { "type": "uint64", "id": 920 }, "total_todos": { "type": "uint64", "id": 930 }, "total_open_todos": { "type": "uint64", "id": 931 }, "has_folder": { "type": "bool", "id": 940 }, "total_signatures": { "type": "uint64", "id": 950 }, "total_emails": { "type": "uint64", "id": 951 }, "total_hits": { "type": "uint64", "id": 952 }, "total_creators": { "type": "uint64", "id": 953 }, "total_transactions": { "type": "uint64", "id": 954 }, "total_pins": { "type": "uint64", "id": 955 }, "total_open_signatures": { "type": "uint64", "id": 956 }, "total_open_transactions": { "type": "uint64", "id": 957 }, "total_size": { "type": "uint64", "id": 1200 }, "access_control_board_id": { "type": "string", "id": 1600 }, "board_member_notification_settings": { "type": "BoardMemberNotificationSetting", "id": 1701 }, "action_notification_settings": { "type": "ActionNotificationSetting", "id": 1702 }, "board_notification_settings": { "type": "BoardNotificationSetting", "id": 1703 }, "archive_after": { "type": "uint64", "id": 1710 }, "properties": { "rule": "repeated", "type": "BoardProperty", "id": 1720 }, "previous_due_date": { "type": "uint64", "id": 1739 }, "due_date": { "type": "uint64", "id": 1740 }, "due_in_timeframe": { "type": "DueTimeFrameType", "id": 1741 }, "exclude_weekends": { "type": "bool", "id": 1742 }, "original_board_id": { "type": "string", "id": 1730 }, "broadcasts": { "rule": "repeated", "type": "BoardBroadcast", "id": 1800 }, "revision": { "type": "uint64", "id": 30 }, "is_deleted": { "type": "bool", "id": 40 }, "assignments": { "type": "string", "id": 41 }, "local_revision": { "type": "uint64", "id": 35 }, "index_local_field_after_revision": { "type": "uint64", "id": 36 }, "index_revision": { "type": "uint64", "id": 32 }, "index_version": { "type": "uint64", "id": 33 }, "revision_in_previous_update_member_job": { "type": "uint64", "id": 1300 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "CombinedPushNotificationPayload": { "edition": "proto2", "fields": { "payload": { "type": "ApplePushNotificationPayload", "id": 10 }, "data": { "type": "GCMPushNotificationData", "id": 100 } } }, "ApplePushNotification": { "edition": "proto2", "fields": { "payload": { "type": "ApplePushNotificationPayload", "id": 10 }, "apple_device_token": { "type": "string", "id": 20 }, "apple_device_tokens": { "rule": "repeated", "type": "string", "id": 21 }, "message_sequence": { "type": "uint64", "id": 30 }, "user_id": { "type": "string", "id": 40 }, "client_id": { "type": "string", "id": 50 }, "is_voip": { "type": "bool", "id": 60 }, "registration_ids": { "rule": "repeated", "type": "string", "id": 100 }, "data": { "type": "GCMPushNotificationData", "id": 200 } } }, "ApplePushNotificationPayload": { "edition": "proto2", "fields": { "aps": { "type": "ApplePushNotificationAps", "id": 10 }, "request": { "type": "ClientRequest", "id": 20 }, "id": { "type": "string", "id": 30 }, "moxtra": { "type": "string", "id": 40 }, "sender_org_id": { "type": "string", "id": 100 }, "sender_unique_id": { "type": "string", "id": 101 }, "sender_name": { "type": "string", "id": 102 }, "receiver_org_id": { "type": "string", "id": 110 }, "receiver_unique_id": { "type": "string", "id": 111 }, "receiver_language": { "type": "string", "id": 112 }, "receiver_email": { "type": "string", "id": 113 }, "receiver_phone_number": { "type": "string", "id": 114 }, "category": { "type": "string", "id": 120 }, "board_id": { "type": "string", "id": 130 }, "is_privacy": { "type": "bool", "id": 140 }, "original_loc_key": { "type": "string", "id": 150 }, "original_action_loc_key": { "type": "string", "id": 160 }, "extended_loc_key": { "type": "string", "id": 170 }, "workspace_type": { "type": "string", "id": 180 } } }, "ApplePushNotificationAps": { "edition": "proto2", "fields": { "alert": { "type": "ApplePushNotificationAlert", "id": 10 }, "badge": { "type": "uint64", "id": 20 }, "sound": { "type": "string", "id": 30 }, "content_available": { "type": "uint64", "id": 40 }, "category": { "type": "string", "id": 50 } } }, "ApplePushNotificationAlert": { "edition": "proto2", "fields": { "title": { "type": "string", "id": 5 }, "body": { "type": "string", "id": 10 }, "action_loc_key": { "type": "string", "id": 20 }, "loc_key": { "type": "string", "id": 30 }, "loc_args": { "rule": "repeated", "type": "string", "id": 40 }, "launch_image": { "type": "string", "id": 50 } } }, "ApplePushNotificationResponse": { "edition": "proto2", "fields": { "reason": { "type": "string", "id": 10 }, "timestamp": { "type": "uint64", "id": 20 } } }, "GCMPushNotification": { "edition": "proto2", "fields": { "registration_ids": { "rule": "repeated", "type": "string", "id": 10 }, "data": { "type": "GCMPushNotificationData", "id": 20 }, "client_id": { "type": "string", "id": 30 }, "priority": { "type": "string", "id": 40 }, "restricted_package_name": { "type": "string", "id": 50 }, "notification": { "type": "GCMPushNotificationMessage", "id": 60 } } }, "GCMPushNotificationMessage": { "edition": "proto2", "fields": { "title": { "type": "string", "id": 10 }, "body": { "type": "string", "id": 20 }, "click_action": { "type": "string", "id": 30 }, "android_channel_id": { "type": "string", "id": 40 }, "tag": { "type": "string", "id": 50 } } }, "GCMPushNotificationData": { "edition": "proto2", "fields": { "body": { "type": "string", "id": 10 }, "action_loc_key": { "type": "string", "id": 20 }, "loc_key": { "type": "string", "id": 30 }, "arg1": { "type": "string", "id": 100 }, "arg2": { "type": "string", "id": 110 }, "arg3": { "type": "string", "id": 120 }, "arg4": { "type": "string", "id": 130 }, "badge": { "type": "uint64", "id": 200 }, "sound": { "type": "string", "id": 210 }, "session_key": { "type": "string", "id": 300 }, "board_id": { "type": "string", "id": 310 }, "page_sequence": { "type": "uint64", "id": 340 }, "feed_sequence": { "type": "uint64", "id": 350 }, "user_id": { "type": "string", "id": 360 }, "id": { "type": "string", "id": 370 }, "moxtra": { "type": "string", "id": 380 }, "request": { "type": "string", "id": 390 }, "title": { "type": "string", "id": 400 }, "board_name": { "type": "string", "id": 500 }, "board_feed_unread_count": { "type": "uint64", "id": 510 }, "sender_org_id": { "type": "string", "id": 600 }, "sender_unique_id": { "type": "string", "id": 601 }, "sender_name": { "type": "string", "id": 602 }, "receiver_org_id": { "type": "string", "id": 610 }, "receiver_unique_id": { "type": "string", "id": 611 }, "receiver_language": { "type": "string", "id": 612 }, "receiver_email": { "type": "string", "id": 613 }, "receiver_phone_number": { "type": "string", "id": 614 }, "category": { "type": "string", "id": 620 }, "is_privacy": { "type": "bool", "id": 630 }, "original_loc_key": { "type": "string", "id": 640 }, "original_action_loc_key": { "type": "string", "id": 650 }, "extended_loc_key": { "type": "string", "id": 660 }, "workspace_type": { "type": "string", "id": 670 } } }, "GCMPushNotificationResponse": { "edition": "proto2", "fields": { "multicast_id": { "type": "string", "id": 10 }, "success": { "type": "uint64", "id": 20 }, "failure": { "type": "uint64", "id": 30 }, "canonical_ids": { "type": "uint64", "id": 40 }, "results": { "rule": "repeated", "type": "GCMResult", "id": 50 } } }, "GCMResult": { "edition": "proto2", "fields": { "message_id": { "type": "string", "id": 10 }, "registration_id": { "type": "string", "id": 20 }, "error": { "type": "string", "id": 30 } } }, "PushNotificationProxyResponse": { "edition": "proto2", "fields": { "message": { "type": "string", "id": 10 }, "timestamp": { "type": "uint64", "id": 20 }, "user_id": { "type": "string", "id": 30 }, "bad_apple_device_tokens": { "rule": "repeated", "type": "string", "id": 100 }, "bad_registration_ids": { "rule": "repeated", "type": "string", "id": 200 } } }, "ObjectFeedType": { "edition": "proto2", "values": { "FEED_INVALID": 0, "FEED_BOARD_CREATE": 100, "FEED_BOARD_NAME_CHANGE": 101, "FEED_BOARD_COMMENT": 102, "FEED_BOARD_VOICE_COMMENT": 103, "FEED_BOARD_COMMENT_DELETE": 104, "FEED_BOARD_DUE_DATE_UPDATE": 107, "FEED_BOARD_DUE_DATE_ARRIVE": 108, "FEED_PAGES_CREATE": 200, "FEED_PAGES_CREATE_WITH_ANNOTATION": 201, "FEED_PAGES_ANNOTATION": 230, "FEED_PAGES_UPDATE": 240, "FEED_PAGES_DELETE": 250, "FEED_PAGES_COMMENT": 260, "FEED_PAGES_POSITION_COMMENT": 261, "FEED_PAGES_COMMENT_DELETE": 262, "FEED_PAGES_RENAME": 270, "FEED_PAGES_RECYCLE": 271, "FEED_PAGES_MOVE": 272, "FEED_EMAIL_RECEIVE": 300, "FEED_RELATIONSHIP_JOIN": 500, "FEED_RELATIONSHIP_LEAVE": 501, "FEED_RELATIONSHIP_INVITE": 502, "FEED_RELATIONSHIP_DECLINE": 503, "FEED_RELATIONSHIP_CANCEL": 504, "FEED_RELATIONSHIP_REMOVE": 505, "FEED_RELATIONSHIP_CHANGE_ROLE": 506, "FEED_TODO_CREATE": 600, "FEED_TODO_CREATE_WITH_RESOURCE": 601, "FEED_TODO_UPDATE": 602, "FEED_TODO_DELETE": 603, "FEED_TODO_ASSIGN": 604, "FEED_TODO_COMMENT": 605, "FEED_TODO_ATTACHMENT": 606, "FEED_TODO_DUE_DATE": 607, "FEED_TODO_COMPLETE": 608, "FEED_TODO_REOPEN": 609, "FEED_TODO_DUE_DATE_ARRIVE": 610, "FEED_TODO_COMMENT_DELETE": 611, "FEED_TODO_MARK_AS_COMPLETED": 612, "FEED_FOLDER_CREATE": 700, "FEED_FOLDER_RENAME": 701, "FEED_FOLDER_RECYCLE": 702, "FEED_FOLDER_DELETE": 703, "FEED_SESSION_SCHEDULE": 800, "FEED_SESSION_RESCHEDULE": 801, "FEED_SESSION_START": 802, "FEED_SESSION_END": 803, "FEED_SESSION_RECORDING_READY": 804, "FEED_SESSION_CANCEL": 805, "FEED_SESSION_RENAME": 806, "FEED_NOTE_CREATE": 810, "FEED_PIN": 900, "FEED_CALL_LOG": 1000, "FEED_AUDIO_CALL_LOG": 1010, "FEED_RELATIONSHIP_INVITE_PENDING": 1100, "FEED_RELATIONSHIP_REMOVE_PENDING": 1110, "FEED_RELATIONSHIP_JOIN_PENDING": 1120, "FEED_REQUESTING_USER_CREATE": 1121, "FEED_REQUESTING_USER_UPDATE": 1122, "FEED_SIGNATURE_STATUS_UPDATE": 1200, "FEED_SIGNATURE_DELETE": 1201, "FEED_SIGNATURE_RENAME": 1202, "FEED_SIGNATURE_CONVERTED": 1203, "FEED_SIGNATURE_DUE_DATE_UPDATE": 1204, "FEED_SIGNATURE_DUE_DATE_ARRIVE": 1205, "FEED_SIGNATURE_FILE_REPLY": 1206, "FEED_SIGNATURE_UPDATE": 1207, "FEED_SIGNATURE_REOPEN": 1208, "FEED_SIGNATURE_UPDATE_REOPEN": 1209, "FEED_VIEWTOKEN_CREATE": 1210, "FEED_VIEWTOKEN_DELETE": 1211, "FEED_VIEWTOKEN_UPDATE": 1212, "FEED_SIGNATURE_UPDATE_EDITING": 1213, "FEED_SIGNATURE_UPDATE_READY": 1214, "FEED_SIGNATURE_MARK_AS_COMPLETED": 1215, "FEED_TRANSACTION_CREATE": 1220, "FEED_TRANSACTION_DELETE": 1221, "FEED_TRANSACTION_UPDATE": 1222, "FEED_TRANSACTION_UPDATE_REOPEN": 1223, "FEED_TRANSACTION_STEP_SUBMIT": 1225, "FEED_TRANSACTION_ATTACHMENT": 1226, "FEED_TRANSACTION_EXPIRATION_DATE_ARRIVE": 1227, "FEED_TRANSACTION_STEP_SUBMIT_BATCH": 1228, "FEED_TRANSACTION_STEP_REOPEN": 1229, "FEED_TRANSACTION_EXPIRATION_UPDATE": 1230, "FEED_TRANSACTION_FILE_REPLY": 1231, "FEED_TRANSACTION_REOPEN": 1232, "FEED_TRANSACTION_FORM_CONVERTED": 1233, "FEED_TRANSACTION_UPDATE_CUSTOM_RESULT": 1234, "FEED_TRANSACTION_UPDATE_EDITING": 1235, "FEED_TRANSACTION_UPDATE_READY": 1236, "FEED_TRANSACTION_MARK_AS_COMPLETED": 1237, "FEED_WORKFLOW_STEP_PREPARING": 1300, "FEED_WORKFLOW_STEP_READY": 1301, "FEED_WORKFLOW_STEP_SKIPPED": 1302, "FEED_WORKFLOW_STEP_REOPEN": 1303, "FEED_WORKFLOW_STARTED": 1310, "FEED_WORKFLOW_RESTARTED": 1311, "FEED_WORKFLOW_COMPLETED": 1320, "FEED_WORKFLOW_CANCELED": 1321, "FEED_WORKFLOW_CONTINUED": 1322, "FEED_WORKFLOW_CREATE": 1330, "FEED_WORKFLOW_UPDATE": 1331, "FEED_WORKFLOW_DELETE": 1332, "FEED_REASSIGN": 1400, "FEED_SERVICE_REQUEST_CREATE": 1500, "FEED_SERVICE_REQUEST_UPDATE": 1501, "FEED_SERVICE_REQUEST_COMPLETE": 1502, "FEED_SERVICE_REQUEST_REOPEN": 1503, "FEED_ACD_REQUEST_END": 1550, "FEED_ACD_REQUEST_TIMEOUT": 1551 } }, "ObjectFeedStatus": { "edition": "proto2", "values": { "FEED_STATUS_INVALID": 0, "FEED_STATUS_PENDING": 10, "FEED_STATUS_APPROVED": 20, "FEED_STATUS_DENIED": 30 } }, "ObjectFeedViaSource": { "edition": "proto2", "values": { "FEED_VIA_UNKNOWN": 0, "FEED_VIA_IOS": 10, "FEED_VIA_ANDROID": 20, "FEED_VIA_WEB": 30, "FEED_VIA_DESKTOP": 40, "FEED_VIA_WORKFLOW": 50 } }, "FeedReaction": { "edition": "proto2", "fields": { "creator": { "type": "BoardActor", "id": 10 }, "text": { "type": "string", "id": 20 }, "timestamp": { "type": "uint64", "id": 30 } } }, "ObjectFeed": { "edition": "proto2", "fields": { "actor": { "type": "User", "id": 10, "options": { "lazy": true } }, "view_token": { "type": "BoardViewToken", "id": 11 }, "roster": { "type": "ActionUserRoster", "id": 12 }, "type": { "type": "ObjectFeedType", "id": 20 }, "board": { "type": "Board", "id": 40, "options": { "lazy": true } }, "timestamp": { "type": "uint64", "id": 50 }, "is_pinned": { "type": "bool", "id": 60 }, "delegate": { "type": "User", "id": 200, "options": { "lazy": true } }, "status": { "type": "ObjectFeedStatus", "id": 210 }, "via": { "type": "ObjectFeedViaSource", "id": 220 }, "reactions": { "rule": "repeated", "type": "FeedReaction", "id": 300 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "ObjectActivity": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "server": { "type": "string", "id": 15 }, "actor": { "type": "User", "id": 20 }, "request_object": { "type": "CacheObject", "id": 90 }, "object": { "type": "CacheObject", "id": 100 }, "revision": { "type": "uint64", "id": 110 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "ObjectRecording": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "activities": { "rule": "repeated", "type": "ObjectActivity", "id": 110 }, "recording_count": { "type": "uint64", "id": 150 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "AudioRecording": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "session_key": { "type": "string", "id": 20 }, "start_time": { "type": "uint64", "id": 30 }, "end_time": { "type": "uint64", "id": 40 }, "name": { "type": "string", "id": 50 }, "hash": { "type": "string", "id": 60 }, "server_addr": { "type": "string", "id": 70 } } }, "VideoRecording": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "session_key": { "type": "string", "id": 20 }, "start_time": { "type": "uint64", "id": 30 }, "end_time": { "type": "uint64", "id": 40 }, "name": { "type": "string", "id": 50 }, "hash": { "type": "string", "id": 60 }, "server_addr": { "type": "string", "id": 70 } } }, "DsRecording": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 10 }, "session_key": { "type": "string", "id": 20 }, "start_time": { "type": "uint64", "id": 30 }, "end_time": { "type": "uint64", "id": 40 }, "name": { "type": "string", "id": 50 }, "server_addr": { "type": "string", "id": 60 } } }, "PublicViewTokenType": { "edition": "proto2", "values": { "EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL": 1, "EMAIL_TOKEN_PICTURE_PAGE_THUMBNAIL": 2, "EMAIL_TOKEN_PICTURE_USER_PICTURE": 3, "EMAIL_TOKEN_VIDEO_COMMENT_HASH": 4, "EMAIL_TOKEN_VIDEO_COMMENT": 5, "EMAIL_TOKEN_VERIFY_USER_EMAIL": 10, "EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL_INVIATION": 11, "EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL_INVIATION_NON_REGISTERED": 12, "EMAIL_TOKEN_PICTURE_USER_PICTURE_INVIATION": 13, "EMAIL_TOKEN_PICTURE_USER_PICTURE_INVIATION_NON_REGISTERED": 14, "PUBLIC_VIEW_TOKEN_BOARD": 20, "TRANSACTION_VIEW_TOKEN": 30, "BOARD_RESOURCE_VIEW_TOKEN": 40, "BOARD_PUBLIC_ACCESS_TOKEN": 50, "GROUP_INVITATION_TOKEN": 200, "PARTNER_INVITATION_TOKEN": 260, "BOARD_INVITATION_TOKEN": 210, "CONTACT_INVITATION_TOKEN": 230, "SESSION_ROSTER_TOKEN": 220, "GROUP_USER_VIEW_TOKEN": 250, "GROUP_USER_INVITATION_TOKEN": 270 } }, "PublicViewToken": { "edition": "proto2", "fields": { "version": { "type": "uint64", "id": 1 }, "token": { "type": "string", "id": 5 }, "user_id": { "type": "string", "id": 10 }, "board_id": { "type": "string", "id": 15 }, "board_token": { "type": "uint64", "id": 16 }, "session_key": { "type": "string", "id": 17 }, "roster_index": { "type": "uint64", "id": 18 }, "roster_channel": { "type": "uint64", "id": 19 }, "type": { "type": "PublicViewTokenType", "id": 50 }, "resource_hash": { "type": "string", "id": 60 }, "resource_seq": { "type": "uint64", "id": 61 }, "resource_origin": { "type": "string", "id": 62 }, "actor_id": { "type": "string", "id": 20 }, "feed_seq": { "type": "uint64", "id": 30 }, "contact_seq": { "type": "uint64", "id": 40 }, "transaction_seq": { "type": "uint64", "id": 70 }, "boarduser_seq": { "type": "uint64", "id": 80 }, "created_time": { "type": "uint64", "id": 100 }, "user_email": { "type": "string", "id": 200 }, "user_phone_number": { "type": "string", "id": 201 }, "group_id": { "type": "string", "id": 210 }, "groupuser_seq": { "type": "uint64", "id": 211 }, "invitation_token_seq": { "type": "uint64", "id": 212 }, "invitation_token_created_timestamp": { "type": "uint64", "id": 213 }, "partner_id": { "type": "string", "id": 220 }, "token_created_timestamp": { "type": "uint64", "id": 230 }, "token_expire_timestamp": { "type": "uint64", "id": 240 } } }, "GroupUsageItemType": { "edition": "proto2", "values": { "GROUP_LICENSE_USAGE_TYPE": 10, "GROUP_LICENSE_USAGE_TYPE_LOCAL": 20 } }, "GroupUsageItem": { "edition": "proto2", "fields": { "group": { "type": "Group", "id": 10 }, "item_type": { "type": "GroupUsageItemType", "id": 20 }, "timestamp": { "type": "uint64", "id": 30 }, "quantity_used": { "type": "uint64", "id": 40 }, "quantity_active": { "type": "uint64", "id": 42 }, "quantity_unassigned": { "type": "uint64", "id": 44 }, "quantity_committed": { "type": "uint64", "id": 50 }, "quantity": { "type": "uint64", "id": 60 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "GroupUsageCount": { "edition": "proto2", "fields": { "partners": { "type": "uint64", "id": 10 }, "groups": { "type": "uint64", "id": 20 }, "users": { "type": "uint64", "id": 30 } } }, "SessionUsageItem": { "edition": "proto2", "fields": { "board": { "type": "Board", "id": 10 }, "session": { "type": "ActionObject", "id": 20 }, "total_minutes": { "type": "uint64", "id": 30 }, "total_telephony_minutes": { "type": "uint64", "id": 40 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "BoardUsageItem": { "edition": "proto2", "fields": { "board": { "type": "Board", "id": 10 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "UsageStatistics": { "edition": "proto2", "fields": { "id": { "type": "string", "id": 1 }, "group_usage_items": { "rule": "repeated", "type": "GroupUsageItem", "id": 30 }, "session_items": { "rule": "repeated", "type": "SessionUsageItem", "id": 40 }, "total_minutes": { "type": "uint64", "id": 41 }, "total_telephony_minutes": { "type": "uint64", "id": 42 }, "board_items": { "rule": "repeated", "type": "BoardUsageItem", "id": 50 }, "group_counts": { "type": "GroupUsageCount", "id": 200 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "ActivityLog": { "edition": "proto2", "fields": { "token": { "type": "AccessToken", "id": 10 }, "request": { "type": "ClientRequest", "id": 100 }, "response": { "type": "ClientResponse", "id": 200 }, "user": { "type": "User", "id": 400 }, "stats": { "type": "ActivityStatistics", "id": 900 }, "logs": { "rule": "repeated", "type": "TransactionLog", "id": 1000 }, "created_time": { "type": "uint64", "id": 2000 } } }, "ActivityStatistics": { "edition": "proto2", "fields": { "start_time": { "type": "uint64", "id": 10 }, "end_time": { "type": "uint64", "id": 20 }, "app_stats": { "type": "AppStatistics", "id": 800 }, "revision": { "type": "uint64", "id": 103 }, "is_deleted": { "type": "bool", "id": 104 }, "assignments": { "type": "string", "id": 105 }, "local_revision": { "type": "uint64", "id": 106 }, "created_time": { "type": "uint64", "id": 108 }, "updated_time": { "type": "uint64", "id": 109 } } }, "AppStatCategoryLeftSidePanel": { "edition": "proto2", "fields": { "click_clients": { "type": "uint64", "id": 1110 }, "click_internal": { "type": "uint64", "id": 1111 }, "click_files": { "type": "uint64", "id": 1112 }, "click_files_plus": { "type": "uint64", "id": 1113 }, "click_meetings": { "type": "uint64", "id": 1114 }, "click_meetings_plus": { "type": "uint64", "id": 1115 }, "click_esign": { "type": "uint64", "id": 1116 }, "click_esign_plus": { "type": "uint64", "id": 1117 }, "click_todos": { "type": "uint64", "id": 1118 }, "click_todos_plus": { "type": "uint64", "id": 1119 }, "click_contacts": { "type": "uint64", "id": 1120 }, "click_broadcast": { "type": "uint64", "id": 1121 }, "click_updates": { "type": "uint64", "id": 1122 } } }, "AppStatCategoryMainNewPlusPanel": { "edition": "proto2", "fields": { "click_file": { "type": "uint64", "id": 1210 }, "click_esign": { "type": "uint64", "id": 1211 }, "click_todos": { "type": "uint64", "id": 1212 }, "click_start_meeting": { "type": "uint64", "id": 1213 }, "click_schedule_meeting": { "type": "uint64", "id": 1214 } } }, "AppStatCategoryTopNavBar": { "edition": "proto2", "fields": { "click_manage": { "type": "uint64", "id": 1310 }, "click_global_search": { "type": "uint64", "id": 1311 }, "click_action_items": { "type": "uint64", "id": 1312 }, "click_mentions": { "type": "uint64", "id": 1313 } } }, "AppStatCategoryMentionList": { "edition": "proto2", "fields": { "click_dismiss_all": { "type": "uint64", "id": 1410 }, "click_dismiss_item": { "type": "uint64", "id": 1411 }, "click_mention_item": { "type": "uint64", "id": 1412 } } }, "AppStatCategoryActionItems": { "edition": "proto2", "fields": { "click_action_item": { "type": "uint64", "id": 1510 } } }, "AppStatCategoryTimeline": { "edition": "proto2", "fields": { "click_action_items": { "type": "uint64", "id": 1610 }, "click_mentions": { "type": "uint64", "id": 1611 }, "click_global_search": { "type": "uint64", "id": 1612 }, "click_filter_conversation": { "type": "uint64", "id": 1613 } } }, "AppStatCategoryBinderView": { "edition": "proto2", "fields": { "click_meeting": { "type": "uint64", "id": 1710 }, "click_search": { "type": "uint64", "id": 1711 }, "click_files_tab": { "type": "uint64", "id": 1712 }, "click_todo_tab": { "type": "uint64", "id": 1713 } } }, "AppStatCategoryOverview": { "edition": "proto2", "fields": { "click_overview_tab": { "type": "uint64", "id": 1810 }, "click_files_view_all": { "type": "uint64", "id": 1811 }, "click_file_item": { "type": "uint64", "id": 1812 }, "click_esign_view_all": { "type": "uint64", "id": 1813 }, "click_sign_now": { "type": "uint64", "id": 1814 }, "click_todos_view_all": { "type": "uint64", "id": 1815 }, "click_todo_item": { "type": "uint64", "id": 1816 } } }, "AppStatCategoryNewFlowWorkspace": { "edition": "proto2", "fields": { "total_launched_by_webhook": { "type": "uint64", "id": 3310 }, "total_launched_by_webhook_with_newly_invited_client": { "type": "uint64", "id": 3311 }, "total_launched_from_template": { "type": "uint64", "id": 3312 }, "total_launched_from_template_with_newly_invited_client": { "type": "uint64", "id": 3313 }, "total_launched_from_plus_new": { "type": "uint64", "id": 3314 }, "total_launched_from_plus_new_with_newly_invited_client": { "type": "uint64", "id": 3315 }, "total_launched_from_scheduled_flow": { "type": "uint64", "id": 3316 }, "total_launched_from_scheduled_flow_with_newly_invited_client": { "type": "uint64", "id": 3317 }, "total_launched_by_zapier": { "type": "uint64", "id": 3318 }, "total_launched_by_zapier_with_newly_invited_client": { "type": "uint64", "id": 3319 }, "total_launched_by_rest_api": { "type": "uint64", "id": 3320 }, "total_launched_by_rest_api_with_newly_invited_client": { "type": "uint64", "id": 3321 }, "total_launched_from_sr": { "type": "uint64", "id": 3322 }, "total_launched_from_main_flow": { "type": "uint64", "id": 3323 } } }, "AppStatistics": { "edition": "proto2", "fields": { "web_left_side_panel": { "type": "AppStatCategoryLeftSidePanel", "id": 1103 }, "web_main_new_plus_panel": { "type": "AppStatCategoryMainNewPlusPanel", "id": 1203 }, "web_top_nav_bar": { "type": "AppStatCategoryTopNavBar", "id": 1303 }, "ios_mention_list": { "type": "AppStatCategoryMentionList", "id": 1401 }, "android_mention_list": { "type": "AppStatCategoryMentionList", "id": 1402 }, "web_mention_list": { "type": "AppStatCategoryMentionList", "id": 1403 }, "ios_action_items": { "type": "AppStatCategoryActionItems", "id": 1501 }, "android_action_items": { "type": "AppStatCategoryActionItems", "id": 1502 }, "web_action_items": { "type": "AppStatCategoryActionItems", "id": 1503 }, "ios_timeline": { "type": "AppStatCategoryTimeline", "id": 1601 }, "android_timeline": { "type": "AppStatCategoryTimeline", "id": 1602 }, "ios_binder_view": { "type": "AppStatCategoryBinderView", "id": 1701 }, "android_binder_view": { "type": "AppStatCategoryBinderView", "id": 1702 }, "ios_overview": { "type": "AppStatCategoryOverview", "id": 1801 }, "android_overview": { "type": "AppStatCategoryOverview", "id": 1802 }, "ios_new_flow_workspace": { "type": "AppStatCategoryNewFlowWorkspace", "id": 3301 }, "android_new_flow_workspace": { "type": "AppStatCategoryNewFlowWorkspace", "id": 3302 }, "web_new_flow_workspace": { "type": "AppStatCategoryNewFlowWorkspace", "id": 3303 } } }, "ResourceItem": { "edition": "proto2", "fields": { "hash": { "type": "string", "id": 10 }, "accessed_time": { "type": "uint64", "id": 20 }, "created_time": { "type": "uint64", "id": 30 }, "content_length": { "type": "uint64", "id": 40 } } }, "Resources": { "edition": "proto2", "fields": { "resources": { "rule": "repeated", "type": "ResourceItem", "id": 10 } } }, "TwilioRequestParam": { "edition": "proto2", "fields": { "call_sid": { "type": "string", "id": 10 }, "account_sid": { "type": "string", "id": 20 }, "from": { "type": "string", "id": 30 }, "to": { "type": "string", "id": 40 }, "p_asserted_identity": { "type": "string", "id": 45 }, "call_status": { "type": "string", "id": 50 }, "api_version": { "type": "string", "id": 60 } } }, "SystemEmailConfig": { "edition": "proto2", "fields": { "username": { "type": "string", "id": 10 }, "password": { "type": "string", "id": 20 }, "server_address": { "type": "string", "id": 30 }, "server_port": { "type": "uint64", "id": 40 }, "from_address": { "type": "string", "id": 50 }, "default_incoming_domain": { "type": "string", "id": 100 }, "incoming_domains": { "rule": "repeated", "type": "string", "id": 110 }, "alert_emails": { "rule": "repeated", "type": "string", "id": 200 }, "feedback_email": { "type": "string", "id": 210 }, "revision": { "type": "uint64", "id": 1000 }, "is_deleted": { "type": "bool", "id": 1010 }, "created_time": { "type": "uint64", "id": 1020 }, "updated_time": { "type": "uint64", "id": 1030 } } }, "SystemAdminUser": { "edition": "proto2", "fields": { "user": { "type": "User", "id": 10 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "SystemUdpMapping": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "domain": { "type": "string", "id": 20 }, "port": { "type": "uint64", "id": 30 }, "tcp_port": { "type": "uint64", "id": 40 }, "url": { "type": "string", "id": 50 }, "ds_url": { "type": "string", "id": 60 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "SystemAppMapping": { "edition": "proto2", "fields": { "name": { "type": "string", "id": 10 }, "client_id": { "type": "string", "id": 20 }, "sequence": { "type": "uint64", "id": 100 }, "client_uuid": { "type": "string", "id": 101 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } }, "SystemDocumentConverter": { "edition": "proto2", "fields": { "wopi_url": { "type": "string", "id": 10 }, "word_viewer_url": { "type": "string", "id": 20 }, "ppt_viewer_url": { "type": "string", "id": 30 }, "ppt_param_pid": { "type": "string", "id": 40 }, "mac_viewer_url": { "type": "string", "id": 100 }, "resource_domain": { "type": "string", "id": 200 } } }, "SystemFeatures": { "edition": "proto2", "fields": { "disable_register_login": { "type": "bool", "id": 10 }, "disable_meet_recording_feed": { "type": "bool", "id": 100 } } }, "SystemPasswordRule": { "edition": "proto2", "fields": { "character": { "type": "uint64", "id": 10 }, "lowercase": { "type": "uint64", "id": 20 }, "uppercase": { "type": "uint64", "id": 30 }, "digit": { "type": "uint64", "id": 40 }, "special": { "type": "uint64", "id": 50 }, "special_characters": { "type": "string", "id": 200 } } }, "SystemConfig": { "edition": "proto2", "fields": { "domain": { "type": "string", "id": 10 }, "timezone": { "type": "string", "id": 11, "options": { "default": "America/Los_Angeles" } }, "email_config": { "type": "SystemEmailConfig", "id": 15 }, "default_plan_cap": { "type": "UserCap", "id": 20 }, "plans": { "rule": "repeated", "type": "CachePlan", "id": 21 }, "app_mappings": { "rule": "repeated", "type": "SystemAppMapping", "id": 24 }, "udp_mappings": { "rule": "repeated", "type": "SystemUdpMapping", "id": 25 }, "document_converter": { "type": "SystemDocumentConverter", "id": 30 }, "users": { "rule": "repeated", "type": "SystemAdminUser", "id": 40 }, "features": { "type": "SystemFeatures", "id": 50 }, "password_rule": { "type": "SystemPasswordRule", "id": 60 }, "validation_code": { "type": "string", "id": 200 }, "is_validated": { "type": "bool", "id": 210, "options": { "default": true } }, "validation_expires_after": { "type": "uint64", "id": 211 }, "revision": { "type": "uint64", "id": 110 }, "is_deleted": { "type": "bool", "id": 120 }, "local_revision": { "type": "uint64", "id": 130 }, "assignments": { "type": "string", "id": 131 }, "created_time": { "type": "uint64", "id": 1000 }, "updated_time": { "type": "uint64", "id": 1010 } } } } };
