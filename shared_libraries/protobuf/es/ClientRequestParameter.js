export var ClientRequestParameter;
(function (ClientRequestParameter) {
    ClientRequestParameter["USER_REQUEST_REGISTER_NO_QS_BOARDS"] = "USER_REQUEST_REGISTER_NO_QS_BOARDS";
    ClientRequestParameter["USER_REQUEST_SALES_FORCE_CONNECT_URL"] = "USER_REQUEST_SALES_FORCE_CONNECT_URL";
    ClientRequestParameter["USER_REQUEST_READ_SET_COOKIE"] = "USER_REQUEST_READ_SET_COOKIE";
    ClientRequestParameter["USER_REQUEST_GET_ACCESS_TOKEN"] = "USER_REQUEST_GET_ACCESS_TOKEN";
    ClientRequestParameter["USER_REQUEST_READ_TIMESTAMP"] = "USER_REQUEST_READ_TIMESTAMP";
    ClientRequestParameter["BOARD_REQUEST_READ_TIMESTAMP"] = "BOARD_REQUEST_READ_TIMESTAMP";
    ClientRequestParameter["SERVER_OBJECT_READ_TIMESTAMP"] = "SERVER_OBJECT_READ_TIMESTAMP";
    ClientRequestParameter["GROUP_REQUEST_READ_USAGE_TIMESTAMP"] = "GROUP_REQUEST_READ_USAGE_TIMESTAMP";
    ClientRequestParameter["GROUP_REQUEST_READ_TASKS_TIMESTAMP"] = "GROUP_REQUEST_READ_TASKS_TIMESTAMP";
    ClientRequestParameter["USER_REQUEST_READ_COUNT"] = "USER_REQUEST_READ_COUNT";
    ClientRequestParameter["BOARD_REQUEST_READ_COUNT"] = "BOARD_REQUEST_READ_COUNT";
    ClientRequestParameter["USER_REQUEST_READ_TIMESTAMP_FROM"] = "USER_REQUEST_READ_TIMESTAMP_FROM";
    ClientRequestParameter["GROUP_REQUEST_READ_TIMESTAMP_FROM"] = "GROUP_REQUEST_READ_TIMESTAMP_FROM";
    ClientRequestParameter["USER_REQUEST_READ_TIMESTAMP_TO"] = "USER_REQUEST_READ_TIMESTAMP_TO";
    ClientRequestParameter["GROUP_REQUEST_READ_TIMESTAMP_TO"] = "GROUP_REQUEST_READ_TIMESTAMP_TO";
    ClientRequestParameter["USER_REQUEST_READ_TIMESTAMP_OFFSET"] = "USER_REQUEST_READ_TIMESTAMP_OFFSET";
    ClientRequestParameter["GROUP_REQUEST_READ_TIMESTAMP_OFFSET"] = "GROUP_REQUEST_READ_TIMESTAMP_OFFSET";
    ClientRequestParameter["GROUP_REQUEST_EXPORT_REPORT_HEADERS"] = "GROUP_REQUEST_EXPORT_REPORT_HEADERS";
    ClientRequestParameter["GROUP_REQUEST_EXPORT_DATE_LOCALE"] = "GROUP_REQUEST_EXPORT_DATE_LOCALE";
    ClientRequestParameter["USER_REQUEST_REPORT_RUN_BY"] = "USER_REQUEST_REPORT_RUN_BY";
    ClientRequestParameter["OUTPUT_FILTER_STRING"] = "OUTPUT_FILTER_STRING";
    ClientRequestParameter["LOGIN_OUTPUT_USER_FILTER_STRING"] = "LOGIN_OUTPUT_USER_FILTER_STRING";
    ClientRequestParameter["USER_REQUEST_LOGIN_REMEMBER"] = "USER_REQUEST_LOGIN_REMEMBER";
    ClientRequestParameter["USER_REQUEST_LOGIN_OUTPUT_BASIC"] = "USER_REQUEST_LOGIN_OUTPUT_BASIC";
    ClientRequestParameter["USER_REQUEST_LOGIN_LOCAL_USER_EXPECTED"] = "USER_REQUEST_LOGIN_LOCAL_USER_EXPECTED";
    ClientRequestParameter["GROUP_REQUEST_LOCAL_USER_EXPECTED"] = "GROUP_REQUEST_LOCAL_USER_EXPECTED";
    ClientRequestParameter["USER_REQUEST_LOGIN_PARTNER_ADMIN_EXPECTED"] = "USER_REQUEST_LOGIN_PARTNER_ADMIN_EXPECTED";
    ClientRequestParameter["USER_REQUEST_LOGOUT_KEEP_DEVICE_TOKEN"] = "USER_REQUEST_LOGOUT_KEEP_DEVICE_TOKEN";
    ClientRequestParameter["USER_REQUEST_REMEMBER_DEVICE"] = "USER_REQUEST_REMEMBER_DEVICE";
    ClientRequestParameter["USER_REQUEST_RESET_PASSWORD_PARTNER_ADMIN_EXPECTED"] = "USER_REQUEST_RESET_PASSWORD_PARTNER_ADMIN_EXPECTED";
    ClientRequestParameter["USER_REQUEST_PASSWORD_ENCODED"] = "USER_REQUEST_PASSWORD_ENCODED";
    ClientRequestParameter["USER_REQUEST_KEEP_TOKEN"] = "USER_REQUEST_KEEP_TOKEN";
    ClientRequestParameter["USER_REQUEST_ALL_BOARDS"] = "USER_REQUEST_ALL_BOARDS";
    ClientRequestParameter["USER_REQUEST_OLD_BOARDS"] = "USER_REQUEST_OLD_BOARDS";
    ClientRequestParameter["USER_REQUEST_FEEDBACK_MESSAGE"] = "USER_REQUEST_FEEDBACK_MESSAGE";
    ClientRequestParameter["USER_REQUEST_FEEDBACK_SUBJECT"] = "USER_REQUEST_FEEDBACK_SUBJECT";
    ClientRequestParameter["USER_SUBSCRIBE_FILTER_MEET"] = "USER_SUBSCRIBE_FILTER_MEET";
    ClientRequestParameter["USER_REQUEST_FILTER_ACD"] = "USER_REQUEST_FILTER_ACD";
    ClientRequestParameter["USER_REQUEST_FILTER_SERVICE_REQUEST"] = "USER_REQUEST_FILTER_SERVICE_REQUEST";
    ClientRequestParameter["USER_REQUEST_SSO_REDIRECT_URL"] = "USER_REQUEST_SSO_REDIRECT_URL";
    ClientRequestParameter["USER_REQUEST_CODE_TO_REGISTER"] = "USER_REQUEST_CODE_TO_REGISTER";
    ClientRequestParameter["USER_REQUEST_CODE_TO_RESET_PASSWORD"] = "USER_REQUEST_CODE_TO_RESET_PASSWORD";
    ClientRequestParameter["USER_REQUEST_HTML_URL"] = "USER_REQUEST_HTML_URL";
    ClientRequestParameter["USER_REQUEST_EMAIL_CODE"] = "USER_REQUEST_EMAIL_CODE";
    ClientRequestParameter["USER_REQUEST_SMS_CODE"] = "USER_REQUEST_SMS_CODE";
    ClientRequestParameter["USER_REQUEST_APPLE_JWT"] = "USER_REQUEST_APPLE_JWT";
    ClientRequestParameter["USER_REQUEST_GOOGLE_JWT"] = "USER_REQUEST_GOOGLE_JWT";
    ClientRequestParameter["USER_REQUEST_DEVICE_TOKEN_VENDOR"] = "USER_REQUEST_DEVICE_TOKEN_VENDOR";
    ClientRequestParameter["USER_REQUEST_DEVICE_TOKEN_VENDOR_EXT"] = "USER_REQUEST_DEVICE_TOKEN_VENDOR_EXT";
    ClientRequestParameter["USER_REQUEST_CONTACT_INVITE_TOKEN"] = "USER_REQUEST_CONTACT_INVITE_TOKEN";
    ClientRequestParameter["GROUP_REQUEST_INVITE_MESSAGE"] = "GROUP_REQUEST_INVITE_MESSAGE";
    ClientRequestParameter["GROUP_REQUEST_INVITE_TOKEN"] = "GROUP_REQUEST_INVITE_TOKEN";
    ClientRequestParameter["GROUP_REQUEST_USER_TOKEN"] = "GROUP_REQUEST_USER_TOKEN";
    ClientRequestParameter["PARTNER_REQUEST_INVITE_TOKEN"] = "PARTNER_REQUEST_INVITE_TOKEN";
    ClientRequestParameter["BOARD_REQUEST_EXTERNAL_ID"] = "BOARD_REQUEST_EXTERNAL_ID";
    ClientRequestParameter["BOARD_REQUEST_CREATE_AS_TEMP"] = "BOARD_REQUEST_CREATE_AS_TEMP";
    ClientRequestParameter["BOARD_REQUEST_CREATE_AS_DEFAULT"] = "BOARD_REQUEST_CREATE_AS_DEFAULT";
    ClientRequestParameter["BOARD_REQUEST_CLEAR_VIEW_TOKEN"] = "BOARD_REQUEST_CLEAR_VIEW_TOKEN";
    ClientRequestParameter["BOARD_REQUEST_NEW_BOARD"] = "BOARD_REQUEST_NEW_BOARD";
    ClientRequestParameter["BOARD_REQUEST_CREATE_AS_SUBSCRIPTION_CHANNEL"] = "BOARD_REQUEST_CREATE_AS_SUBSCRIPTION_CHANNEL";
    ClientRequestParameter["BOARD_REQUEST_CREATE_SIGNATURE_AS_TEMPLATE"] = "BOARD_REQUEST_CREATE_SIGNATURE_AS_TEMPLATE";
    ClientRequestParameter["BOARD_REQUEST_SUBMIT_SIGNATURE_KEEP_STATUS_UNCHANGED"] = "BOARD_REQUEST_SUBMIT_SIGNATURE_KEEP_STATUS_UNCHANGED";
    ClientRequestParameter["BOARD_REQUEST_READ_UPLOAD_SEQUENCE"] = "BOARD_REQUEST_READ_UPLOAD_SEQUENCE";
    ClientRequestParameter["BOARD_REQUEST_VIEW_TOKEN"] = "BOARD_REQUEST_VIEW_TOKEN";
    ClientRequestParameter["SESSION_REQUEST_ROSTER_TOKEN"] = "SESSION_REQUEST_ROSTER_TOKEN";
    ClientRequestParameter["BOARD_REQUEST_SEARCH_TEXT"] = "BOARD_REQUEST_SEARCH_TEXT";
    ClientRequestParameter["BOARD_REQUEST_SEARCH_START"] = "BOARD_REQUEST_SEARCH_START";
    ClientRequestParameter["BOARD_REQUEST_SEARCH_SIZE"] = "BOARD_REQUEST_SEARCH_SIZE";
    ClientRequestParameter["BOARD_REQUEST_SEARCH_CREATOR"] = "BOARD_REQUEST_SEARCH_CREATOR";
    ClientRequestParameter["BOARD_REQUEST_SEARCH_ID"] = "BOARD_REQUEST_SEARCH_ID";
    ClientRequestParameter["BOARD_REQUEST_SEARCH_BOARD_TYPE"] = "BOARD_REQUEST_SEARCH_BOARD_TYPE";
    ClientRequestParameter["BOARD_REQUEST_KEEP_UNREAD_FEED_TIMESTAMP"] = "BOARD_REQUEST_KEEP_UNREAD_FEED_TIMESTAMP";
    ClientRequestParameter["GROUP_REQUEST_SEARCH_TEXT"] = "GROUP_REQUEST_SEARCH_TEXT";
    ClientRequestParameter["GROUP_REQUEST_SEARCH_START"] = "GROUP_REQUEST_SEARCH_START";
    ClientRequestParameter["GROUP_REQUEST_SEARCH_SIZE"] = "GROUP_REQUEST_SEARCH_SIZE";
    ClientRequestParameter["GROUP_REQUEST_SEARCH_PAGE_NUMBER"] = "GROUP_REQUEST_SEARCH_PAGE_NUMBER";
    ClientRequestParameter["GROUP_REQUEST_SEARCH_SORT_FIELD"] = "GROUP_REQUEST_SEARCH_SORT_FIELD";
    ClientRequestParameter["GROUP_REQUEST_SEARCH_SORT_METHOD"] = "GROUP_REQUEST_SEARCH_SORT_METHOD";
    ClientRequestParameter["PARTNER_REQUEST_SEARCH_TEXT"] = "PARTNER_REQUEST_SEARCH_TEXT";
    ClientRequestParameter["PARTNER_REQUEST_SEARCH_START"] = "PARTNER_REQUEST_SEARCH_START";
    ClientRequestParameter["PARTNER_REQUEST_SEARCH_SIZE"] = "PARTNER_REQUEST_SEARCH_SIZE";
    ClientRequestParameter["GROUP_REQUEST_ACTION_GROUP"] = "GROUP_REQUEST_ACTION_GROUP";
    ClientRequestParameter["GROUP_REQUEST_ACTION_TYPE"] = "GROUP_REQUEST_ACTION_TYPE";
    ClientRequestParameter["GROUP_REQUEST_SEARCH_MEMBER"] = "GROUP_REQUEST_SEARCH_MEMBER";
    ClientRequestParameter["GROUP_REQUEST_SUPPRESS_STATISTICS"] = "GROUP_REQUEST_SUPPRESS_STATISTICS";
    ClientRequestParameter["USER_REQUEST_SEARCH_TEXT"] = "USER_REQUEST_SEARCH_TEXT";
    ClientRequestParameter["USER_REQUEST_SEARCH_START"] = "USER_REQUEST_SEARCH_START";
    ClientRequestParameter["USER_REQUEST_SEARCH_SIZE"] = "USER_REQUEST_SEARCH_SIZE";
    ClientRequestParameter["USER_REQUEST_SEARCH_PAGE_NUMBER"] = "USER_REQUEST_SEARCH_PAGE_NUMBER";
    ClientRequestParameter["USER_REQUEST_SEARCH_CREATOR"] = "USER_REQUEST_SEARCH_CREATOR";
    ClientRequestParameter["USER_REQUEST_SEARCH_ID"] = "USER_REQUEST_SEARCH_ID";
    ClientRequestParameter["USER_REQUEST_SEARCH_SORT_BY_TIME"] = "USER_REQUEST_SEARCH_SORT_BY_TIME";
    ClientRequestParameter["USER_REQUEST_SEARCH_DUE_FROM"] = "USER_REQUEST_SEARCH_DUE_FROM";
    ClientRequestParameter["USER_REQUEST_SEARCH_DUE_TO"] = "USER_REQUEST_SEARCH_DUE_TO";
    ClientRequestParameter["USER_REQUEST_SEARCH_EXCLUDE_CREATOR"] = "USER_REQUEST_SEARCH_EXCLUDE_CREATOR";
    ClientRequestParameter["USER_REQUEST_SEARCH_CREATED_OR_ASSIGNED"] = "USER_REQUEST_SEARCH_CREATED_OR_ASSIGNED";
    ClientRequestParameter["USER_REQUEST_SEARCH_CREATED_OR_SUBMITTED"] = "USER_REQUEST_SEARCH_CREATED_OR_SUBMITTED";
    ClientRequestParameter["USER_REQUEST_SEARCH_INCLUDE_CANCELED"] = "USER_REQUEST_SEARCH_INCLUDE_CANCELED";
    ClientRequestParameter["USER_REQUEST_SEARCH_TIMELINE"] = "USER_REQUEST_SEARCH_TIMELINE";
    ClientRequestParameter["USER_REQUEST_SEARCH_ARCHIVED"] = "USER_REQUEST_SEARCH_ARCHIVED";
    ClientRequestParameter["USER_REQUEST_SEARCH_INCLUDE_EDITING"] = "USER_REQUEST_SEARCH_INCLUDE_EDITING";
    ClientRequestParameter["BOARD_REQUEST_READ_FEEDS_INDEXED"] = "BOARD_REQUEST_READ_FEEDS_INDEXED";
    ClientRequestParameter["BOARD_REQUEST_READ_FEEDS_ORIGINAL"] = "BOARD_REQUEST_READ_FEEDS_ORIGINAL";
    ClientRequestParameter["BOARD_REQUEST_READ_WITH_DETAIL"] = "BOARD_REQUEST_READ_WITH_DETAIL";
    ClientRequestParameter["BOARD_REQUEST_READ_WITHOUT_MEMBERS"] = "BOARD_REQUEST_READ_WITHOUT_MEMBERS";
    ClientRequestParameter["GROUP_REQUEST_READ_GROUP_MEMBER"] = "GROUP_REQUEST_READ_GROUP_MEMBER";
    ClientRequestParameter["GROUP_REQEUEST_READ_MEMBER_PRESENCE"] = "GROUP_REQEUEST_READ_MEMBER_PRESENCE";
    ClientRequestParameter["BOARD_REQUEST_COPY_PAGES_WITH_COMMENTS"] = "BOARD_REQUEST_COPY_PAGES_WITH_COMMENTS";
    ClientRequestParameter["BOARD_REQUEST_COPY_PAGES_WITHOUT_ANNOTATIONS"] = "BOARD_REQUEST_COPY_PAGES_WITHOUT_ANNOTATIONS";
    ClientRequestParameter["BOARD_REQUEST_COPY_TODOS_WITH_COMMENTS"] = "BOARD_REQUEST_COPY_TODOS_WITH_COMMENTS";
    ClientRequestParameter["BOARD_REQUEST_COPY_SIGNATURE_KEEP_CREATOR"] = "BOARD_REQUEST_COPY_SIGNATURE_KEEP_CREATOR";
    ClientRequestParameter["BOARD_REQUEST_COPY_TRANSACTION_KEEP_CREATOR"] = "BOARD_REQUEST_COPY_TRANSACTION_KEEP_CREATOR";
    ClientRequestParameter["BOARD_REQUEST_COPY_WITH_TOTAL_USED_COUNT"] = "BOARD_REQUEST_COPY_WITH_TOTAL_USED_COUNT";
    ClientRequestParameter["BOARD_REQUEST_COPY_WITH_LAST_MODIFIED_TIME"] = "BOARD_REQUEST_COPY_WITH_LAST_MODIFIED_TIME";
    ClientRequestParameter["BOARD_REQUEST_DUPLICATE_WITH_SIGNATURES"] = "BOARD_REQUEST_DUPLICATE_WITH_SIGNATURES";
    ClientRequestParameter["BOARD_REQUEST_DUPLICATE_WITH_TRANSACTIONS"] = "BOARD_REQUEST_DUPLICATE_WITH_TRANSACTIONS";
    ClientRequestParameter["RESOURCE_REQUEST_RESOURCE_TYPE"] = "RESOURCE_REQUEST_RESOURCE_TYPE";
    ClientRequestParameter["RESOURCE_REQUEST_RESOURCE_ROTATION"] = "RESOURCE_REQUEST_RESOURCE_ROTATION";
    ClientRequestParameter["RESOURCE_REQUEST_RESOURCE_WIDTH"] = "RESOURCE_REQUEST_RESOURCE_WIDTH";
    ClientRequestParameter["RESOURCE_REQUEST_RESOURCE_HEIGHT"] = "RESOURCE_REQUEST_RESOURCE_HEIGHT";
    ClientRequestParameter["BOARD_REQUEST_RELATIVE_ORDER_NUMBER"] = "BOARD_REQUEST_RELATIVE_ORDER_NUMBER";
    ClientRequestParameter["RESOURCE_UPLOAD_RESOURCE_MEDIA_LENGTH"] = "RESOURCE_UPLOAD_RESOURCE_MEDIA_LENGTH";
    ClientRequestParameter["RESOURCE_UPLOAD_RESOURCE_DESCRIPTION"] = "RESOURCE_UPLOAD_RESOURCE_DESCRIPTION";
    ClientRequestParameter["RESOURCE_DOWNLOAD_RESOURCE_CONTENT_DISPOSITION"] = "RESOURCE_DOWNLOAD_RESOURCE_CONTENT_DISPOSITION";
    ClientRequestParameter["RESOURCE_UPLOAD_RESOURCE_URL"] = "RESOURCE_UPLOAD_RESOURCE_URL";
    ClientRequestParameter["RESOURCE_UPLOAD_RESOURCE_AUTHORIZATION"] = "RESOURCE_UPLOAD_RESOURCE_AUTHORIZATION";
    ClientRequestParameter["RESOURCE_UPLOAD_RESOURCE_DESTINATION_FOLDER"] = "RESOURCE_UPLOAD_RESOURCE_DESTINATION_FOLDER";
    ClientRequestParameter["RESOURCE_UPLOAD_RESOURCE_CONTENT_TYPE"] = "RESOURCE_UPLOAD_RESOURCE_CONTENT_TYPE";
    ClientRequestParameter["BOARD_REQUEST_INVITEE_EMAIL"] = "BOARD_REQUEST_INVITEE_EMAIL";
    ClientRequestParameter["BOARD_REQUEST_INVITE_MESSAGE"] = "BOARD_REQUEST_INVITE_MESSAGE";
    ClientRequestParameter["BOARD_REQUEST_PUSH_NOTIFICATION_MESSAGE"] = "BOARD_REQUEST_PUSH_NOTIFICATION_MESSAGE";
    ClientRequestParameter["BOARD_REQUEST_PUSH_NOTIFICATION_OFF"] = "BOARD_REQUEST_PUSH_NOTIFICATION_OFF";
    ClientRequestParameter["GROUP_REQUEST_PUSH_NOTIFICATION_OFF"] = "GROUP_REQUEST_PUSH_NOTIFICATION_OFF";
    ClientRequestParameter["BOARD_REQUEST_EMAIL_OFF"] = "BOARD_REQUEST_EMAIL_OFF";
    ClientRequestParameter["USER_REQUEST_EMAIL_OFF"] = "USER_REQUEST_EMAIL_OFF";
    ClientRequestParameter["GROUP_REQUEST_EMAIL_OFF"] = "GROUP_REQUEST_EMAIL_OFF";
    ClientRequestParameter["BOARD_REQUEST_SMS_OFF"] = "BOARD_REQUEST_SMS_OFF";
    ClientRequestParameter["GROUP_REQUEST_SMS_OFF"] = "GROUP_REQUEST_SMS_OFF";
    ClientRequestParameter["BOARD_REQUEST_INVITE_ADD_DIRECTLY"] = "BOARD_REQUEST_INVITE_ADD_DIRECTLY";
    ClientRequestParameter["BOARD_REQUEST_SIGNEE_MESSAGE"] = "BOARD_REQUEST_SIGNEE_MESSAGE";
    ClientRequestParameter["GROUP_REQUEST_RESEND_EMAIL"] = "GROUP_REQUEST_RESEND_EMAIL";
    ClientRequestParameter["GROUP_REQUEST_RESEND_SMS"] = "GROUP_REQUEST_RESEND_SMS";
    ClientRequestParameter["BOARD_REQUEST_INVITE_WITH_INFO_FROM"] = "BOARD_REQUEST_INVITE_WITH_INFO_FROM";
    ClientRequestParameter["BOARD_REQUEST_DELETE_FOLDER_RECURSIVELY"] = "BOARD_REQUEST_DELETE_FOLDER_RECURSIVELY";
    ClientRequestParameter["BOARD_REQUEST_UPDATE_FILE_COVER"] = "BOARD_REQUEST_UPDATE_FILE_COVER";
    ClientRequestParameter["BOARD_REQUEST_CREATE_NEW_FILE"] = "BOARD_REQUEST_CREATE_NEW_FILE";
    ClientRequestParameter["BOARD_REQUEST_NEW_FILE_NAME_SEQUENCE"] = "BOARD_REQUEST_NEW_FILE_NAME_SEQUENCE";
    ClientRequestParameter["BOARD_REQUEST_NEW_FILE_NAME"] = "BOARD_REQUEST_NEW_FILE_NAME";
    ClientRequestParameter["BOARD_REQUEST_ACTOR_NAME"] = "BOARD_REQUEST_ACTOR_NAME";
    ClientRequestParameter["BOARD_REQUEST_ACTOR_PICTURE_URL"] = "BOARD_REQUEST_ACTOR_PICTURE_URL";
    ClientRequestParameter["BOARD_REQUEST_CUSTOM_INFO"] = "BOARD_REQUEST_CUSTOM_INFO";
    ClientRequestParameter["BOARD_REQUEST_CUSTOM_INFO_SEQUENCE"] = "BOARD_REQUEST_CUSTOM_INFO_SEQUENCE";
    ClientRequestParameter["BOARD_REQUEST_ORDER_NUMBER_SEQUENCE"] = "BOARD_REQUEST_ORDER_NUMBER_SEQUENCE";
    ClientRequestParameter["BOARD_REQUEST_SOCIAL_CUSTOM_INFO"] = "BOARD_REQUEST_SOCIAL_CUSTOM_INFO";
    ClientRequestParameter["BOARD_REQUEST_MEMBER_VIEW_TOKEN"] = "BOARD_REQUEST_MEMBER_VIEW_TOKEN";
    ClientRequestParameter["BOARD_REQUEST_RESET_MEMBER_STATUS"] = "BOARD_REQUEST_RESET_MEMBER_STATUS";
    ClientRequestParameter["BOARD_REQUEST_AUTO_COMPLETE_TRANSACTION"] = "BOARD_REQUEST_AUTO_COMPLETE_TRANSACTION";
    ClientRequestParameter["BOARD_REQUEST_VIEW_TOKEN_TO_UPDATE_BOARD"] = "BOARD_REQUEST_VIEW_TOKEN_TO_UPDATE_BOARD";
    ClientRequestParameter["BOARD_REQUEST_DUPLICATE_WITH_WORKFLOW"] = "BOARD_REQUEST_DUPLICATE_WITH_WORKFLOW";
    ClientRequestParameter["BOARD_REQUEST_DUPLICATE_AS_TEMPLATE"] = "BOARD_REQUEST_DUPLICATE_AS_TEMPLATE";
    ClientRequestParameter["BOARD_REQUEST_CREATE_FLOW_VARIABLE"] = "BOARD_REQUEST_CREATE_FLOW_VARIABLE";
    ClientRequestParameter["BOARD_REQUEST_NOT_FILTER_FLOW_VARIABLE"] = "BOARD_REQUEST_NOT_FILTER_FLOW_VARIABLE";
    ClientRequestParameter["BOARD_REQUEST_COPY_KEEP_FLOW_STEP_UUID"] = "BOARD_REQUEST_COPY_KEEP_FLOW_STEP_UUID";
    ClientRequestParameter["BOARD_REQUEST_COPY_BOARD_THUMBNAIL"] = "BOARD_REQUEST_COPY_BOARD_THUMBNAIL";
    ClientRequestParameter["BOARD_REQUEST_COPY_BOARD_BANNER"] = "BOARD_REQUEST_COPY_BOARD_BANNER";
    ClientRequestParameter["BOARD_REQUEST_COPY_SKIP_ASSIGNEE_CHECKING"] = "BOARD_REQUEST_COPY_SKIP_ASSIGNEE_CHECKING";
    ClientRequestParameter["BOARD_REQUEST_COPY_WITH_COMPLETED_TODOS"] = "BOARD_REQUEST_COPY_WITH_COMPLETED_TODOS";
    ClientRequestParameter["BOARD_REQUEST_DUPLICATE_MILESTONE_FROM"] = "BOARD_REQUEST_DUPLICATE_MILESTONE_FROM";
    ClientRequestParameter["BOARD_REQUEST_DUPLICATE_MILESTONE_TO"] = "BOARD_REQUEST_DUPLICATE_MILESTONE_TO";
    ClientRequestParameter["BOARD_REQUEST_COPY_BOARD_PROPERTY"] = "BOARD_REQUEST_COPY_BOARD_PROPERTY";
    ClientRequestParameter["BOARD_REQUEST_UPDATE_WORKFLOW_WITHOUT_LAST_MODIFIED_TIME"] = "BOARD_REQUEST_UPDATE_WORKFLOW_WITHOUT_LAST_MODIFIED_TIME";
    ClientRequestParameter["BOARD_REQUEST_REPLACE_TRANSACTION_WITH_STEPS"] = "BOARD_REQUEST_REPLACE_TRANSACTION_WITH_STEPS";
    ClientRequestParameter["BOARD_REQUEST_COPY_WITH_WORKFLOW"] = "BOARD_REQUEST_COPY_WITH_WORKFLOW";
    ClientRequestParameter["SESSION_REQUEST_INVITE_MESSAGE"] = "SESSION_REQUEST_INVITE_MESSAGE";
    ClientRequestParameter["SESSION_REQUEST_RECORDING"] = "SESSION_REQUEST_RECORDING";
    ClientRequestParameter["SESSION_REQUEST_JOIN_INVISIBLE"] = "SESSION_REQUEST_JOIN_INVISIBLE";
    ClientRequestParameter["GRAB_PRESENTER_WHEN_NOT_SHARING"] = "GRAB_PRESENTER_WHEN_NOT_SHARING";
    ClientRequestParameter["SESSION_REQUEST_SESSION_KEY"] = "SESSION_REQUEST_SESSION_KEY";
    ClientRequestParameter["BOARD_REQUEST_SUPPRESS_FEED"] = "BOARD_REQUEST_SUPPRESS_FEED";
    ClientRequestParameter["BOARD_REQUEST_SUPPRESS_JOB"] = "BOARD_REQUEST_SUPPRESS_JOB";
    ClientRequestParameter["BOARD_REQUEST_SUPPRESS_USER_ACTIVITY"] = "BOARD_REQUEST_SUPPRESS_USER_ACTIVITY";
    ClientRequestParameter["SUBSCRIBE_REQUEST_NOHANG"] = "SUBSCRIBE_REQUEST_NOHANG";
    ClientRequestParameter["BOARD_REQUEST_DOWNLOAD_BOARD_NO_WAIT"] = "BOARD_REQUEST_DOWNLOAD_BOARD_NO_WAIT";
    ClientRequestParameter["SUBSCRIBE_REQUEST_NO_GROUP_MEMBERS"] = "SUBSCRIBE_REQUEST_NO_GROUP_MEMBERS";
    ClientRequestParameter["SUBSCRIBE_REQUEST_NO_USER_BOARDS"] = "SUBSCRIBE_REQUEST_NO_USER_BOARDS";
    ClientRequestParameter["SUBSCRIBE_REQUEST_RESPONSE_FILTER_OFF"] = "SUBSCRIBE_REQUEST_RESPONSE_FILTER_OFF";
    ClientRequestParameter["CLIENT_CONNECTION_PUSH_NOTIFICATION_OFF"] = "CLIENT_CONNECTION_PUSH_NOTIFICATION_OFF";
    ClientRequestParameter["CLIENT_CONNECTION_SUBSCRIPTION_DATA_OFF"] = "CLIENT_CONNECTION_SUBSCRIPTION_DATA_OFF";
    ClientRequestParameter["BOARD_REQUEST_FILE_REPLY"] = "BOARD_REQUEST_FILE_REPLY";
    ClientRequestParameter["BOARD_REQUEST_SET_LAST_MODIFIED_TIME"] = "BOARD_REQUEST_SET_LAST_MODIFIED_TIME";
    ClientRequestParameter["BOARD_REQUEST_REASSIGN"] = "BOARD_REQUEST_REASSIGN";
    ClientRequestParameter["BOARD_REQUEST_REASSIGN_FROM_GROUP_ID"] = "BOARD_REQUEST_REASSIGN_FROM_GROUP_ID";
    ClientRequestParameter["BOARD_REQUEST_REASSIGN_TO_GROUP_ID"] = "BOARD_REQUEST_REASSIGN_TO_GROUP_ID";
    ClientRequestParameter["GROUP_REQUEST_READ_MEMBER_INTERNAL"] = "GROUP_REQUEST_READ_MEMBER_INTERNAL";
    ClientRequestParameter["GROUP_REQUEST_READ_MEMBER_LOCAL"] = "GROUP_REQUEST_READ_MEMBER_LOCAL";
    ClientRequestParameter["GROUP_REQUEST_READ_GROUP_INVITED"] = "GROUP_REQUEST_READ_GROUP_INVITED";
    ClientRequestParameter["GROUP_REQUEST_READ_GROUP_ADMIN"] = "GROUP_REQUEST_READ_GROUP_ADMIN";
    ClientRequestParameter["GROUP_REQUEST_READ_FILTER_ROLE"] = "GROUP_REQUEST_READ_FILTER_ROLE";
    ClientRequestParameter["GROUP_REQUEST_READ_INCLUDE_RELATION_USER"] = "GROUP_REQUEST_READ_INCLUDE_RELATION_USER";
    ClientRequestParameter["GROUP_REQUEST_READ_INCLUDE_SUGGESTED_USER"] = "GROUP_REQUEST_READ_INCLUDE_SUGGESTED_USER";
    ClientRequestParameter["GROUP_REQUEST_READ_DEACTIVED_USER"] = "GROUP_REQUEST_READ_DEACTIVED_USER";
    ClientRequestParameter["GROUP_REQUEST_READ_DELETED_USER"] = "GROUP_REQUEST_READ_DELETED_USER";
    ClientRequestParameter["GROUP_REQUEST_READ_MEMBER_OUTPUT_COUNT"] = "GROUP_REQUEST_READ_MEMBER_OUTPUT_COUNT";
    ClientRequestParameter["GROUP_REQUEST_READ_MEMBER_OUTPUT_CLIENT_TEAM"] = "GROUP_REQUEST_READ_MEMBER_OUTPUT_CLIENT_TEAM";
    ClientRequestParameter["GROUP_REQUEST_READ_MEMBER_OUTPUT_TEAM"] = "GROUP_REQUEST_READ_MEMBER_OUTPUT_TEAM";
    ClientRequestParameter["GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_INTERNAL"] = "GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_INTERNAL";
    ClientRequestParameter["GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_LOCAL"] = "GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_LOCAL";
    ClientRequestParameter["GROUP_REQUEST_OUTPUT_USER_ACTIVITIES"] = "GROUP_REQUEST_OUTPUT_USER_ACTIVITIES";
    ClientRequestParameter["GROUP_REQUEST_READ_MANAGEMENT_TEAM_MEMBERS"] = "GROUP_REQUEST_READ_MANAGEMENT_TEAM_MEMBERS";
    ClientRequestParameter["GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_REPORT"] = "GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_REPORT";
    ClientRequestParameter["GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_WITH_CLIENT_TEAMS"] = "GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_WITH_CLIENT_TEAMS";
    ClientRequestParameter["GROUP_REQUEST_READ_MEMBER_FOR_AUDIT"] = "GROUP_REQUEST_READ_MEMBER_FOR_AUDIT";
    ClientRequestParameter["GROUP_REQUEST_EXPORT_REPORT_FORMATTED_RUN_ON"] = "GROUP_REQUEST_EXPORT_REPORT_FORMATTED_RUN_ON";
    ClientRequestParameter["GROUP_REQUEST_EXPORT_REPORT_FORMATTED_FROM"] = "GROUP_REQUEST_EXPORT_REPORT_FORMATTED_FROM";
    ClientRequestParameter["GROUP_REQUEST_EXPORT_REPORT_FORMATTED_TO"] = "GROUP_REQUEST_EXPORT_REPORT_FORMATTED_TO";
    ClientRequestParameter["USER_REQUEST_SEARCH_FILTER_MIN"] = "USER_REQUEST_SEARCH_FILTER_MIN";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_NAME"] = "USER_REQUEST_SEARCH_BOARD_NAME";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_USER"] = "USER_REQUEST_SEARCH_BOARD_USER";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_COMMENT"] = "USER_REQUEST_SEARCH_BOARD_COMMENT";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_FILE"] = "USER_REQUEST_SEARCH_BOARD_FILE";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_FOLDER"] = "USER_REQUEST_SEARCH_BOARD_FOLDER";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_PAGE"] = "USER_REQUEST_SEARCH_BOARD_PAGE";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_PAGE_COMMENT"] = "USER_REQUEST_SEARCH_BOARD_PAGE_COMMENT";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_TODO"] = "USER_REQUEST_SEARCH_BOARD_TODO";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_TODO_COMMENT"] = "USER_REQUEST_SEARCH_BOARD_TODO_COMMENT";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_SESSION"] = "USER_REQUEST_SEARCH_BOARD_SESSION";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_SIGNATURE"] = "USER_REQUEST_SEARCH_BOARD_SIGNATURE";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_SIGNATURE_PAGE"] = "USER_REQUEST_SEARCH_BOARD_SIGNATURE_PAGE";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_TRANSACTION"] = "USER_REQUEST_SEARCH_BOARD_TRANSACTION";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_MENTION_COMMENT"] = "USER_REQUEST_SEARCH_BOARD_MENTION_COMMENT";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_APPROVAL"] = "USER_REQUEST_SEARCH_BOARD_APPROVAL";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_ACKNOWLEDGE"] = "USER_REQUEST_SEARCH_BOARD_ACKNOWLEDGE";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_FILE_REQUEST"] = "USER_REQUEST_SEARCH_BOARD_FILE_REQUEST";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_MEET_REQUEST"] = "USER_REQUEST_SEARCH_BOARD_MEET_REQUEST";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_FORM_REQUEST"] = "USER_REQUEST_SEARCH_BOARD_FORM_REQUEST";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_DOCUSIGN"] = "USER_REQUEST_SEARCH_BOARD_DOCUSIGN";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_WEBHOOK"] = "USER_REQUEST_SEARCH_BOARD_WEBHOOK";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_LAUNCH_WEB_APP"] = "USER_REQUEST_SEARCH_BOARD_LAUNCH_WEB_APP";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_INTEGRATION"] = "USER_REQUEST_SEARCH_BOARD_INTEGRATION";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_TODO_TRANSACTION"] = "USER_REQUEST_SEARCH_BOARD_TODO_TRANSACTION";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_WORKFLOW"] = "USER_REQUEST_SEARCH_BOARD_WORKFLOW";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_DECISION"] = "USER_REQUEST_SEARCH_BOARD_DECISION";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_AWAIT"] = "USER_REQUEST_SEARCH_BOARD_AWAIT";
    ClientRequestParameter["USER_REQUEST_SEARCH_BOARD_PDF_FORM"] = "USER_REQUEST_SEARCH_BOARD_PDF_FORM";
    ClientRequestParameter["USER_REQUEST_SEARCH_FILTER_MAX"] = "USER_REQUEST_SEARCH_FILTER_MAX";
    ClientRequestParameter["SERVER_REQUEST_INDEX_USER_INDEX"] = "SERVER_REQUEST_INDEX_USER_INDEX";
    ClientRequestParameter["SERVER_REQUEST_INDEX_ORG_INDEX"] = "SERVER_REQUEST_INDEX_ORG_INDEX";
    ClientRequestParameter["SERVER_REQUEST_INDEX_FILTER"] = "SERVER_REQUEST_INDEX_FILTER";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_NAME"] = "SERVER_REQUEST_INDEX_BOARD_NAME";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_USER"] = "SERVER_REQUEST_INDEX_BOARD_USER";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_COMMENT"] = "SERVER_REQUEST_INDEX_BOARD_COMMENT";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_FILE"] = "SERVER_REQUEST_INDEX_BOARD_FILE";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_FOLDER"] = "SERVER_REQUEST_INDEX_BOARD_FOLDER";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_PAGE"] = "SERVER_REQUEST_INDEX_BOARD_PAGE";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_PAGE_COMMENT"] = "SERVER_REQUEST_INDEX_BOARD_PAGE_COMMENT";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_TODO"] = "SERVER_REQUEST_INDEX_BOARD_TODO";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_TODO_COMMENT"] = "SERVER_REQUEST_INDEX_BOARD_TODO_COMMENT";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_SESSION"] = "SERVER_REQUEST_INDEX_BOARD_SESSION";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_SIGNATURE"] = "SERVER_REQUEST_INDEX_BOARD_SIGNATURE";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_SIGNATURE_PAGE"] = "SERVER_REQUEST_INDEX_BOARD_SIGNATURE_PAGE";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_TRANSACTION"] = "SERVER_REQUEST_INDEX_BOARD_TRANSACTION";
    ClientRequestParameter["SERVER_REQUEST_INDEX_BOARD_ACTIONITEM"] = "SERVER_REQUEST_INDEX_BOARD_ACTIONITEM";
    ClientRequestParameter["SERVER_REQUEST_INDEX_GLOBAL_USER_ORG_MAPPING"] = "SERVER_REQUEST_INDEX_GLOBAL_USER_ORG_MAPPING";
    ClientRequestParameter["USER_REQUEST_READ_START"] = "USER_REQUEST_READ_START";
    ClientRequestParameter["USER_REQUEST_READ_SIZE"] = "USER_REQUEST_READ_SIZE";
    ClientRequestParameter["USER_REQUEST_READ_PAGE_NUMBER"] = "USER_REQUEST_READ_PAGE_NUMBER";
    ClientRequestParameter["USER_REQUEST_READ_BEFORE"] = "USER_REQUEST_READ_BEFORE";
    ClientRequestParameter["USER_REQUEST_READ_AFTER"] = "USER_REQUEST_READ_AFTER";
    ClientRequestParameter["USER_REQUEST_READ_TIMELINE"] = "USER_REQUEST_READ_TIMELINE";
    ClientRequestParameter["USER_REQUEST_READ_SR_OPEN"] = "USER_REQUEST_READ_SR_OPEN";
    ClientRequestParameter["USER_REQUEST_READ_SR_COMPLETE"] = "USER_REQUEST_READ_SR_COMPLETE";
    ClientRequestParameter["USER_REQUEST_READ_ARCHIVED"] = "USER_REQUEST_READ_ARCHIVED";
    ClientRequestParameter["USER_REQUEST_READ_SUBSCRIPTION_CHANNEL"] = "USER_REQUEST_READ_SUBSCRIPTION_CHANNEL";
    ClientRequestParameter["USER_REQUEST_READ_INBOX"] = "USER_REQUEST_READ_INBOX";
    ClientRequestParameter["USER_REQUEST_LOOKUP_P2P_BOARD"] = "USER_REQUEST_LOOKUP_P2P_BOARD";
    ClientRequestParameter["USER_REQUEST_READ_UNREAD_BOARD"] = "USER_REQUEST_READ_UNREAD_BOARD";
    ClientRequestParameter["USER_REQUEST_READ_CONVERSATION_BOARD"] = "USER_REQUEST_READ_CONVERSATION_BOARD";
    ClientRequestParameter["USER_REQUEST_READ_PROJECT_BOARD"] = "USER_REQUEST_READ_PROJECT_BOARD";
    ClientRequestParameter["USER_REQUEST_READ_SOCIAL_BOARD"] = "USER_REQUEST_READ_SOCIAL_BOARD";
    ClientRequestParameter["USER_REQUEST_READ_SCHEDULE_BOARD"] = "USER_REQUEST_READ_SCHEDULE_BOARD";
    ClientRequestParameter["USER_REQUEST_USER_AGENT_EXT"] = "USER_REQUEST_USER_AGENT_EXT";
    ClientRequestParameter["BOARD_REQUEST_READ_SIZE_BEFORE"] = "BOARD_REQUEST_READ_SIZE_BEFORE";
    ClientRequestParameter["BOARD_REQUEST_READ_SIZE_AFTER"] = "BOARD_REQUEST_READ_SIZE_AFTER";
    ClientRequestParameter["BOARD_REQUEST_READ_FIRST_SUB_FOLDER_FILE"] = "BOARD_REQUEST_READ_FIRST_SUB_FOLDER_FILE";
    ClientRequestParameter["BOARD_REQUEST_READ_POSITION_COMMENT_FEED"] = "BOARD_REQUEST_READ_POSITION_COMMENT_FEED";
    ClientRequestParameter["BOARD_REQUEST_EDITOR_TYPE_INTERNAL_ONLY"] = "BOARD_REQUEST_EDITOR_TYPE_INTERNAL_ONLY";
    ClientRequestParameter["BOARD_REQUEST_REOPEN"] = "BOARD_REQUEST_REOPEN";
    ClientRequestParameter["BOARD_REQUEST_UPDATE_FEED"] = "BOARD_REQUEST_UPDATE_FEED";
    ClientRequestParameter["BOARD_REQUEST_KEEP_CURRENT_STATUS"] = "BOARD_REQUEST_KEEP_CURRENT_STATUS";
    ClientRequestParameter["BOARD_REQUEST_REOPEN_FEED"] = "BOARD_REQUEST_REOPEN_FEED";
    ClientRequestParameter["BOARD_REQUEST_REOPEN_EVENT"] = "BOARD_REQUEST_REOPEN_EVENT";
    ClientRequestParameter["BOARD_REQUEST_READY_FEED"] = "BOARD_REQUEST_READY_FEED";
    ClientRequestParameter["BOARD_REQUEST_RESET_REVIEWER_STATUS"] = "BOARD_REQUEST_RESET_REVIEWER_STATUS";
    ClientRequestParameter["BOARD_REQUEST_CREATE_FEED"] = "BOARD_REQUEST_CREATE_FEED";
    ClientRequestParameter["BOARD_REQUEST_CREATE_NO_OWNER"] = "BOARD_REQUEST_CREATE_NO_OWNER";
    ClientRequestParameter["BOARD_REQUEST_START_FLOW_WITHOUT_OWNER"] = "BOARD_REQUEST_START_FLOW_WITHOUT_OWNER";
    ClientRequestParameter["BOARD_REQUEST_RESET_CODE"] = "BOARD_REQUEST_RESET_CODE";
    ClientRequestParameter["BOARD_REQUEST_VIEW_TOKEN_CODE"] = "BOARD_REQUEST_VIEW_TOKEN_CODE";
    ClientRequestParameter["BOARD_REQUEST_MEET_REQUEST"] = "BOARD_REQUEST_MEET_REQUEST";
    ClientRequestParameter["BOARD_REQUEST_SET_RSVP_ACCEPTED"] = "BOARD_REQUEST_SET_RSVP_ACCEPTED";
    ClientRequestParameter["BOARD_REQUEST_CREATE_ATTACHMENT_FOLDER"] = "BOARD_REQUEST_CREATE_ATTACHMENT_FOLDER";
    ClientRequestParameter["BOARD_REQUEST_SET_ASSIGNEE_TO_OWNER"] = "BOARD_REQUEST_SET_ASSIGNEE_TO_OWNER";
    ClientRequestParameter["BOARD_REQUEST_COPY_WITH_COMPLETED_TRANSACTIONS"] = "BOARD_REQUEST_COPY_WITH_COMPLETED_TRANSACTIONS";
    ClientRequestParameter["BOARD_REQUEST_FILTER"] = "BOARD_REQUEST_FILTER";
    ClientRequestParameter["BOARD_REQUEST_FILTER_INCLUDE_TRANSACTION_TODO"] = "BOARD_REQUEST_FILTER_INCLUDE_TRANSACTION_TODO";
    ClientRequestParameter["BOARD_REQUEST_FILTER_INCLUDE_CUSTOM_FOLDER"] = "BOARD_REQUEST_FILTER_INCLUDE_CUSTOM_FOLDER";
    ClientRequestParameter["BOARD_REQUEST_IS_INSTANT_MEET"] = "BOARD_REQUEST_IS_INSTANT_MEET";
    ClientRequestParameter["SERVER_PROBE_SERVER_NAME"] = "SERVER_PROBE_SERVER_NAME";
    ClientRequestParameter["SERVER_OBJECT_READ_SERVER_NAME"] = "SERVER_OBJECT_READ_SERVER_NAME";
    ClientRequestParameter["SERVER_OBJECT_READ_LOG_SERVER_NAME"] = "SERVER_OBJECT_READ_LOG_SERVER_NAME";
    ClientRequestParameter["SERVER_OBJECT_FORWARD_REQUEST_TYPE"] = "SERVER_OBJECT_FORWARD_REQUEST_TYPE";
    ClientRequestParameter["SERVER_OBJECT_GROUP_ID"] = "SERVER_OBJECT_GROUP_ID";
    ClientRequestParameter["SERVER_OBJECT_READ_ID"] = "SERVER_OBJECT_READ_ID";
    ClientRequestParameter["SERVER_OBJECT_READ_TYPE"] = "SERVER_OBJECT_READ_TYPE";
    ClientRequestParameter["SERVER_OBJECT_READ_QUERY_STRING"] = "SERVER_OBJECT_READ_QUERY_STRING";
    ClientRequestParameter["SERVER_OBJECT_READ_SESSION_KEY"] = "SERVER_OBJECT_READ_SESSION_KEY";
    ClientRequestParameter["SERVER_OBJECT_READ_USER_WITH_EMAIL"] = "SERVER_OBJECT_READ_USER_WITH_EMAIL";
    ClientRequestParameter["SERVER_OBJECT_READ_DEVICE_TOKEN_MAPPING"] = "SERVER_OBJECT_READ_DEVICE_TOKEN_MAPPING";
    ClientRequestParameter["SERVER_OBJECT_READ_WITH_DETAIL"] = "SERVER_OBJECT_READ_WITH_DETAIL";
    ClientRequestParameter["SERVER_OBJECT_WRITE_ID"] = "SERVER_OBJECT_WRITE_ID";
    ClientRequestParameter["SERVER_OBJECT_HASH_ID"] = "SERVER_OBJECT_HASH_ID";
    ClientRequestParameter["SERVER_RESOURCE_DOWNLOAD_KEY"] = "SERVER_RESOURCE_DOWNLOAD_KEY";
    ClientRequestParameter["SERVER_RESOURCE_UPLOAD_KEY"] = "SERVER_RESOURCE_UPLOAD_KEY";
    ClientRequestParameter["SERVER_RESOURCE_DOWNLOAD_BUCKET"] = "SERVER_RESOURCE_DOWNLOAD_BUCKET";
    ClientRequestParameter["SERVER_RESOURCE_UPLOAD_BUCKET"] = "SERVER_RESOURCE_UPLOAD_BUCKET";
    ClientRequestParameter["SERVER_RESOURCE_DOWNLOAD_OBJECT_ID"] = "SERVER_RESOURCE_DOWNLOAD_OBJECT_ID";
    ClientRequestParameter["SERVER_RESOURCE_UPLOAD_OBJECT_ID"] = "SERVER_RESOURCE_UPLOAD_OBJECT_ID";
    ClientRequestParameter["SERVER_PREVIEW_RESOURCE_URL"] = "SERVER_PREVIEW_RESOURCE_URL";
    ClientRequestParameter["SERVER_PREVIEW_RESOURCE_HASH"] = "SERVER_PREVIEW_RESOURCE_HASH";
    ClientRequestParameter["SERVER_PREVIEW_RESOURCE_EXTENSION"] = "SERVER_PREVIEW_RESOURCE_EXTENSION";
    ClientRequestParameter["SERVER_REDO_JOB_ID"] = "SERVER_REDO_JOB_ID";
    ClientRequestParameter["SERVER_MIGRATE_ZONE"] = "SERVER_MIGRATE_ZONE";
    ClientRequestParameter["SERVER_JOB_DOMAIN"] = "SERVER_JOB_DOMAIN";
    ClientRequestParameter["SERVER_JOB_USER_ID"] = "SERVER_JOB_USER_ID";
    ClientRequestParameter["SERVER_JOB_FORWARDED_URI"] = "SERVER_JOB_FORWARDED_URI";
    ClientRequestParameter["SERVER_OBJECT_READ_PARAM_BEFORE"] = "SERVER_OBJECT_READ_PARAM_BEFORE";
    ClientRequestParameter["SERVER_OBJECT_READ_PARAM_AFTER"] = "SERVER_OBJECT_READ_PARAM_AFTER";
    ClientRequestParameter["SERVER_OBJECT_READ_BOARD_FEEDS"] = "SERVER_OBJECT_READ_BOARD_FEEDS";
    ClientRequestParameter["SERVER_OBJECT_READ_BOARD_FEEDS_PAGE"] = "SERVER_OBJECT_READ_BOARD_FEEDS_PAGE";
    ClientRequestParameter["SERVER_OBJECT_READ_BOARD_THREAD"] = "SERVER_OBJECT_READ_BOARD_THREAD";
    ClientRequestParameter["SERVER_OBJECT_READ_BOARD_FOLDER"] = "SERVER_OBJECT_READ_BOARD_FOLDER";
    ClientRequestParameter["SERVER_OBJECT_READ_BOARD_FILE"] = "SERVER_OBJECT_READ_BOARD_FILE";
    ClientRequestParameter["SERVER_UPLOAD_CRASH_REPORT_NAME"] = "SERVER_UPLOAD_CRASH_REPORT_NAME";
    ClientRequestParameter["SERVER_DOWNLOAD_CRASH_REPORT_NAME"] = "SERVER_DOWNLOAD_CRASH_REPORT_NAME";
    ClientRequestParameter["SERVER_REQUEST_SERVER_TOKEN"] = "SERVER_REQUEST_SERVER_TOKEN";
    ClientRequestParameter["SERVER_REQUEST_EXPIRES_AFTER"] = "SERVER_REQUEST_EXPIRES_AFTER";
    ClientRequestParameter["SERVER_REQUEST_SERVER_DOMAIN"] = "SERVER_REQUEST_SERVER_DOMAIN";
    ClientRequestParameter["GROUP_REQUEST_CANCEL_MESSAGE"] = "GROUP_REQUEST_CANCEL_MESSAGE";
    ClientRequestParameter["GROUP_REQUEST_INVITE_PREVIEW"] = "GROUP_REQUEST_INVITE_PREVIEW";
    ClientRequestParameter["GROUP_REQUEST_INVITE_UPDATE_EXISTING"] = "GROUP_REQUEST_INVITE_UPDATE_EXISTING";
    ClientRequestParameter["GROUP_REQUEST_STRIPE_SUBSCRIBE_TOKEN"] = "GROUP_REQUEST_STRIPE_SUBSCRIBE_TOKEN";
    ClientRequestParameter["GROUP_REQUEST_STRIPE_COUPON_CODE"] = "GROUP_REQUEST_STRIPE_COUPON_CODE";
    ClientRequestParameter["GROUP_REQUEST_READ_MEET"] = "GROUP_REQUEST_READ_MEET";
    ClientRequestParameter["GROUP_REQUEST_DELETE_BOARD"] = "GROUP_REQUEST_DELETE_BOARD";
    ClientRequestParameter["GROUP_REQUEST_READ_RELATED_USER_RELATION"] = "GROUP_REQUEST_READ_RELATED_USER_RELATION";
    ClientRequestParameter["GROUP_REQUEST_READ_RELATED_USER_FLOW_TEMPLATE"] = "GROUP_REQUEST_READ_RELATED_USER_FLOW_TEMPLATE";
    ClientRequestParameter["GROUP_REQUEST_READ_RELATED_USER_CONTENT_LIBRARY"] = "GROUP_REQUEST_READ_RELATED_USER_CONTENT_LIBRARY";
    ClientRequestParameter["GROUP_REQUEST_READ_RELATED_USER_FLOW_TEMPLATE_LIBRARY"] = "GROUP_REQUEST_READ_RELATED_USER_FLOW_TEMPLATE_LIBRARY";
    ClientRequestParameter["GROUP_REQUEST_NO_RELATION_BOARD"] = "GROUP_REQUEST_NO_RELATION_BOARD";
    ClientRequestParameter["GROUP_REQUEST_CREATE_RELATION_BOARD"] = "GROUP_REQUEST_CREATE_RELATION_BOARD";
    ClientRequestParameter["GROUP_REQUEST_CREATE_PENDING_RELATION"] = "GROUP_REQUEST_CREATE_PENDING_RELATION";
    ClientRequestParameter["GROUP_REQUEST_WELCOME_MESSAGE"] = "GROUP_REQUEST_WELCOME_MESSAGE";
    ClientRequestParameter["GROUP_REQUEST_JWT_EXPIRES_AFTER"] = "GROUP_REQUEST_JWT_EXPIRES_AFTER";
    ClientRequestParameter["GROUP_REQUEST_JWT_PAYLOAD"] = "GROUP_REQUEST_JWT_PAYLOAD";
    ClientRequestParameter["GROUP_REQUEST_BOX_PAYLOAD"] = "GROUP_REQUEST_BOX_PAYLOAD";
    ClientRequestParameter["GROUP_REQUEST_REFRESH_DEFAULT_MEET_PASSWORD"] = "GROUP_REQUEST_REFRESH_DEFAULT_MEET_PASSWORD";
    ClientRequestParameter["GROUP_REQUEST_CREATE_CREATOR_AS_GROUP_OWNER"] = "GROUP_REQUEST_CREATE_CREATOR_AS_GROUP_OWNER";
    ClientRequestParameter["GROUP_REQUEST_READ_CONTENT_LIBRARY_INCLUDE_DEFAULT"] = "GROUP_REQUEST_READ_CONTENT_LIBRARY_INCLUDE_DEFAULT";
    ClientRequestParameter["CLIENT_PARAM_REVISION"] = "CLIENT_PARAM_REVISION";
    ClientRequestParameter["CLIENT_PARAM_JWT"] = "CLIENT_PARAM_JWT";
    ClientRequestParameter["CLIENT_PARAM_FROM_BOARD_ID"] = "CLIENT_PARAM_FROM_BOARD_ID";
    ClientRequestParameter["CLIENT_PARAM_SET_USER_ID_TO_VARIABLE"] = "CLIENT_PARAM_SET_USER_ID_TO_VARIABLE";
    ClientRequestParameter["CLIENT_PARAM_CHECK_BOARD_ACCESS"] = "CLIENT_PARAM_CHECK_BOARD_ACCESS";
    ClientRequestParameter["CLIENT_PARAM_ORIGINAL_BOARD_ID"] = "CLIENT_PARAM_ORIGINAL_BOARD_ID";
    ClientRequestParameter["CLIENT_PARAM_PARENT_BOARD_ID"] = "CLIENT_PARAM_PARENT_BOARD_ID";
    ClientRequestParameter["CLIENT_PARAM_KEEP_BOARD_OWNER"] = "CLIENT_PARAM_KEEP_BOARD_OWNER";
    ClientRequestParameter["CLIENT_PARAM_EMAIL_SMS_SKIP_USER_ID"] = "CLIENT_PARAM_EMAIL_SMS_SKIP_USER_ID";
    ClientRequestParameter["CLIENT_PARAM_CUSTOM_DATA"] = "CLIENT_PARAM_CUSTOM_DATA";
    ClientRequestParameter["CLIENT_PARAM_REFERENCE_TYPE"] = "CLIENT_PARAM_REFERENCE_TYPE";
    ClientRequestParameter["CLIENT_PARAM_BROADCAST_TO_USER_ID"] = "CLIENT_PARAM_BROADCAST_TO_USER_ID";
    ClientRequestParameter["PRESENCE_PARAM_USER_ONLINE"] = "PRESENCE_PARAM_USER_ONLINE";
    ClientRequestParameter["PRESENCE_PARAM_USER_CLIENT"] = "PRESENCE_PARAM_USER_CLIENT";
    ClientRequestParameter["OUTPUT_INCLUDE_STRING"] = "OUTPUT_INCLUDE_STRING";
    ClientRequestParameter["BOARD_REQUEST_ACD_SESSION_END_FEED"] = "BOARD_REQUEST_ACD_SESSION_END_FEED";
    ClientRequestParameter["BOARD_REQUEST_MEETING_TRANSCRIPTION"] = "BOARD_REQUEST_MEETING_TRANSCRIPTION";
    ClientRequestParameter["BOARD_REQUEST_MEETING_SUMMARY"] = "BOARD_REQUEST_MEETING_SUMMARY";
    ClientRequestParameter["CLIENT_PARAM_TIMELINE_BOTTOM_FEED_TIMESTAMP"] = "CLIENT_PARAM_TIMELINE_BOTTOM_FEED_TIMESTAMP";
    ClientRequestParameter["USER_REQUEST_READ_TIMEZONE"] = "USER_REQUEST_READ_TIMEZONE";
    ClientRequestParameter["GROUP_REQUEST_REPORT_NAME"] = "GROUP_REQUEST_REPORT_NAME";
    ClientRequestParameter["USER_REQUEST_INCL_WORKSPACE_TAGS"] = "USER_REQUEST_INCL_WORKSPACE_TAGS";
    ClientRequestParameter["USER_REQUEST_INCL_ADDITIONAL_COLUMNS"] = "USER_REQUEST_INCL_ADDITIONAL_COLUMNS";
})(ClientRequestParameter || (ClientRequestParameter = {}));
