export var PublicViewTokenType;
(function (PublicViewTokenType) {
    PublicViewTokenType["EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL"] = "EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL";
    PublicViewTokenType["EMAIL_TOKEN_PICTURE_PAGE_THUMBNAIL"] = "EMAIL_TOKEN_PICTURE_PAGE_THUMBNAIL";
    PublicViewTokenType["EMAIL_TOKEN_PICTURE_USER_PICTURE"] = "EMAIL_TOKEN_PICTURE_USER_PICTURE";
    PublicViewTokenType["EMAIL_TOKEN_VIDEO_COMMENT_HASH"] = "EMAIL_TOKEN_VIDEO_COMMENT_HASH";
    PublicViewTokenType["EMAIL_TOKEN_VIDEO_COMMENT"] = "EMAIL_TOKEN_VIDEO_COMMENT";
    PublicViewTokenType["EMAIL_TOKEN_VERIFY_USER_EMAIL"] = "EMAIL_TOKEN_VERIFY_USER_EMAIL";
    PublicViewTokenType["EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL_INVIATION"] = "EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL_INVIATION";
    PublicViewTokenType["EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL_INVIATION_NON_REGISTERED"] = "EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL_INVIATION_NON_REGISTERED";
    PublicViewTokenType["EMAIL_TOKEN_PICTURE_USER_PICTURE_INVIATION"] = "EMAIL_TOKEN_PICTURE_USER_PICTURE_INVIATION";
    PublicViewTokenType["EMAIL_TOKEN_PICTURE_USER_PICTURE_INVIATION_NON_REGISTERED"] = "EMAIL_TOKEN_PICTURE_USER_PICTURE_INVIATION_NON_REGISTERED";
    PublicViewTokenType["PUBLIC_VIEW_TOKEN_BOARD"] = "PUBLIC_VIEW_TOKEN_BOARD";
    PublicViewTokenType["TRANSACTION_VIEW_TOKEN"] = "TRANSACTION_VIEW_TOKEN";
    PublicViewTokenType["BOARD_RESOURCE_VIEW_TOKEN"] = "BOARD_RESOURCE_VIEW_TOKEN";
    PublicViewTokenType["BOARD_PUBLIC_ACCESS_TOKEN"] = "BOARD_PUBLIC_ACCESS_TOKEN";
    PublicViewTokenType["GROUP_INVITATION_TOKEN"] = "GROUP_INVITATION_TOKEN";
    PublicViewTokenType["PARTNER_INVITATION_TOKEN"] = "PARTNER_INVITATION_TOKEN";
    PublicViewTokenType["BOARD_INVITATION_TOKEN"] = "BOARD_INVITATION_TOKEN";
    PublicViewTokenType["CONTACT_INVITATION_TOKEN"] = "CONTACT_INVITATION_TOKEN";
    PublicViewTokenType["SESSION_ROSTER_TOKEN"] = "SESSION_ROSTER_TOKEN";
    PublicViewTokenType["GROUP_USER_VIEW_TOKEN"] = "GROUP_USER_VIEW_TOKEN";
    PublicViewTokenType["GROUP_USER_INVITATION_TOKEN"] = "GROUP_USER_INVITATION_TOKEN";
})(PublicViewTokenType || (PublicViewTokenType = {}));
