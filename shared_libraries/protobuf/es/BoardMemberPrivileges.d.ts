export declare enum BoardMemberPrivileges {
    BOARD_DELETE_OTHERS_MSG = "BOARD_DELETE_OTHERS_MSG",
    BOARD_DELETE_OTHERS_COMMENT = "BOARD_DELETE_OTHERS_COMMENT",
    BOARD_DELETE_OTHERS_FILE = "BOARD_DELETE_OTHERS_FILE",
    BOARD_DELETE_OTHERS_ANNOTATION = "BOARD_DELETE_OTHERS_ANNOTATION",
    BOARD_HISTORY_FROM_JOIN = "BOARD_HISTORY_FROM_JOIN",
    BOARD_INVITE_BOARD_MEMBER = "BOARD_INVITE_BOARD_MEMBER",
    BOARD_SHARE_CONTENT = "BOARD_SHARE_CONTENT",
    BOARD_SEND_MSG = "BOARD_SEND_MSG",
    BOARD_ADD_COMMENT = "BOARD_ADD_COMMENT",
    BOARD_UPLOAD_FILE = "BOARD_UPLOAD_FILE",
    BOARD_SIGN_FILE = "BOARD_SIGN_FILE",
    BOARD_ADD_ANNOTATION = "BOARD_ADD_ANNOTATION",
    BOARD_COPY_TO_OTHER_BINDER = "BOARD_COPY_TO_OTHER_BINDER",
    BOARD_SAVE_TO_ALBUM = "BOARD_SAVE_TO_ALBUM"
}
//# sourceMappingURL=BoardMemberPrivileges.d.ts.map