export var WorkflowStepStatus;
(function (WorkflowStepStatus) {
    WorkflowStepStatus["WORKFLOW_STEP_STATUS_INITIAL"] = "WORKFLOW_STEP_STATUS_INITIAL";
    WorkflowStepStatus["WORKFLOW_STEP_STATUS_PREPARING"] = "WORKFLOW_STEP_STATUS_PREPARING";
    WorkflowStepStatus["WORKFLOW_STEP_STATUS_READY"] = "WORKFLOW_STEP_STATUS_READY";
    WorkflowStepStatus["WORKFLOW_STEP_STATUS_STARTED"] = "WORKFLOW_STEP_STATUS_STARTED";
    WorkflowStepStatus["WORKFLOW_STEP_STATUS_COMPLETED"] = "WORKFLOW_STEP_STATUS_COMPLETED";
    WorkflowStepStatus["WORKFLOW_STEP_STATUS_CANCELED"] = "WORKFLOW_STEP_STATUS_CANCELED";
    WorkflowStepStatus["WORKFLOW_STEP_STATUS_FAULTED"] = "WORKFLOW_STEP_STATUS_FAULTED";
    WorkflowStepStatus["WORKFLOW_STEP_STATUS_SKIPPED"] = "WORKFLOW_STEP_STATUS_SKIPPED";
    WorkflowStepStatus["WORKFLOW_STEP_STATUS_PRECONDITION_NOT_MET"] = "WORKFLOW_STEP_STATUS_PRECONDITION_NOT_MET";
})(WorkflowStepStatus || (WorkflowStepStatus = {}));
