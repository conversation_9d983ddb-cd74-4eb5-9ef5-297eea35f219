export var WorkflowActionType;
(function (WorkflowActionType) {
    WorkflowActionType["WORKFLOW_ACTION_TYPE_INVALID"] = "WORKFLOW_ACTION_TYPE_INVALID";
    WorkflowActionType["WORKFLOW_ACTION_TYPE_SMS"] = "WORKFLOW_ACTION_TYPE_SMS";
    WorkflowActionType["WORKFLOW_ACTION_TYPE_EMAIL"] = "WORKFLOW_ACTION_TYPE_EMAIL";
    WorkflowActionType["WORKFLOW_ACTION_TYPE_COMMENT"] = "WORKFLOW_ACTION_TYPE_COMMENT";
    WorkflowActionType["WORKFLOW_ACTION_TYPE_APPROVAL"] = "WORKFLOW_ACTION_TYPE_APPROVAL";
    WorkflowActionType["WORKFLOW_ACTION_TYPE_ACKNOWLEDGE"] = "WORKFLOW_ACTION_TYPE_ACKNOWLEDGE";
    WorkflowActionType["WORKFLOW_ACTION_TYPE_SIGNATURE"] = "WORK<PERSON>OW_ACTION_TYPE_SIGNATURE";
    WorkflowActionType["WORKFLOW_ACTION_TYPE_TODO"] = "WORKFLOW_ACTION_TYPE_TODO";
})(WorkflowActionType || (WorkflowActionType = {}));
