export declare enum TransactionType {
    TRANSACTION_TYPE_GENERIC = "TRANSACTION_TYPE_GENERIC",
    TRANSACTION_TYPE_APPROVAL = "TRANSACTION_TYPE_APPROVAL",
    TRANSACTION_TYPE_ACKNOWLEDGE = "TRANSACTION_TYPE_ACKNOWLEDGE",
    TRANSACTION_TYPE_FILE_REQUEST = "TRANSACTION_TYPE_FILE_REQUEST",
    TRANSACTION_TYPE_MEET_REQUEST = "TRANSACTION_TYPE_MEET_REQUEST",
    TRANSACTION_TYPE_FORM_REQUEST = "TRANSACTION_TYPE_FORM_REQUEST",
    TRANSACTION_TYPE_TIME_BOOKING = "TRANSACTION_TYPE_TIME_BOOKING",
    TRANSACTION_TYPE_PDF_FORM = "TRANSACTION_TYPE_PDF_FORM",
    TRANSACTION_TYPE_DOCUSIGN = "TRANSACTION_TYPE_DOCUSIGN",
    TRANSACTION_TYPE_WEBHOOK = "TRANSACTION_TYPE_WEBHOOK",
    TRANSACTION_TYPE_LAUNCH_WEB_APP = "TRANSACTION_TYPE_LAUNCH_WEB_APP",
    TRANSACTION_TYPE_INTEGRATION = "TRANSACTION_TYPE_INTEGRATION",
    TRANSACTION_TYPE_TODO = "TRANSACTION_TYPE_TODO",
    TRANSACTION_TYPE_DECISION = "TRANSACTION_TYPE_DECISION",
    TRANSACTION_TYPE_AWAIT = "TRANSACTION_TYPE_AWAIT"
}
//# sourceMappingURL=TransactionType.d.ts.map