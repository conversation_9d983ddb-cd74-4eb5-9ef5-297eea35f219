"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BoardResourceType = void 0;
var BoardResourceType;
(function (BoardResourceType) {
    BoardResourceType["BOARD_RESOURCE_NONE"] = "BOARD_RESOURCE_NONE";
    BoardResourceType["BOARD_RESOURCE_THUMBNAIL"] = "BOARD_RESOURCE_THUMBNAIL";
    BoardResourceType["BOARD_RESOURCE_BACKGROUND"] = "BOARD_RESOURCE_BACKGROUND";
    BoardResourceType["BOARD_RESOURCE_VECTOR"] = "BOARD_RESOURCE_VECTOR";
    BoardResourceType["BOARD_RESOURCE_EMBEDDED"] = "BOARD_RESOURCE_EMBEDDED";
    BoardResourceType["BOARD_RESOURCE_VECTOR_THUMBNAIL"] = "BOARD_RESOURCE_VECTOR_THUMBNAIL";
    BoardResourceType["BOARD_RESOURCE_BOARD_AS_PDF"] = "BOARD_RESOURCE_BOARD_AS_PDF";
    BoardResourceType["BOARD_RESOURCE_BOARD_AS_PPT"] = "BOARD_RESOURCE_BOARD_AS_PPT";
    BoardResourceType["BOARD_RESOURCE_RECORDING"] = "BOARD_RESOURCE_RECORDING";
    BoardResourceType["BOARD_RESOURCE_SESSION_AS_VIDEO"] = "BOARD_RESOURCE_SESSION_AS_VIDEO";
    BoardResourceType["BOARD_RESOURCE_COVER"] = "BOARD_RESOURCE_COVER";
    BoardResourceType["BOARD_RESOURCE_BANNER"] = "BOARD_RESOURCE_BANNER";
    BoardResourceType["BOARD_RESOURCE_AUDIO_RECORDING"] = "BOARD_RESOURCE_AUDIO_RECORDING";
    BoardResourceType["BOARD_RESOURCE_AVATAR"] = "BOARD_RESOURCE_AVATAR";
    BoardResourceType["BOARD_RESOURCE_SIGNATURE_AS_PDF"] = "BOARD_RESOURCE_SIGNATURE_AS_PDF";
    BoardResourceType["BOARD_RESOURCE_SESSION_AUDIO_SPEAKER"] = "BOARD_RESOURCE_SESSION_AUDIO_SPEAKER";
    BoardResourceType["BOARD_RESOURCE_SESSION_MEET_CHAT"] = "BOARD_RESOURCE_SESSION_MEET_CHAT";
    BoardResourceType["BOARD_RESOURCE_TRANSACTION_AS_PDF"] = "BOARD_RESOURCE_TRANSACTION_AS_PDF";
    BoardResourceType["BOARD_RESOURCE_TRANSACTION_AS_CSV"] = "BOARD_RESOURCE_TRANSACTION_AS_CSV";
    BoardResourceType["BOARD_RESOURCE_SESSION_TRANSCRIPTION"] = "BOARD_RESOURCE_SESSION_TRANSCRIPTION";
    BoardResourceType["BOARD_RESOURCE_SESSION_TRANSCRIPTION_VTT"] = "BOARD_RESOURCE_SESSION_TRANSCRIPTION_VTT";
    BoardResourceType["BOARD_RESOURCE_SESSION_SUMMARY"] = "BOARD_RESOURCE_SESSION_SUMMARY";
})(BoardResourceType || (exports.BoardResourceType = BoardResourceType = {}));
