"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ACDStatus = void 0;
var ACDStatus;
(function (ACDStatus) {
    ACDStatus["ACD_STATUS_INVALID"] = "ACD_STATUS_INVALID";
    ACDStatus["ACD_STATUS_INITIALIZED"] = "ACD_STATUS_INITIALIZED";
    ACDStatus["ACD_STATUS_QUEUED"] = "ACD_STATUS_QUEUED";
    ACDStatus["ACD_STATUS_CONNECTING"] = "ACD_STATUS_CONNECTING";
    ACDStatus["ACD_STATUS_CONNECTED"] = "ACD_STATUS_CONNECTED";
    ACDStatus["ACD_STATUS_CANCELLED"] = "ACD_STATUS_CANCELLED";
    ACDStatus["ACD_STATUS_NOANSWER"] = "ACD_STATUS_NOANSWER";
    ACDStatus["ACD_STATUS_DECLINED"] = "ACD_STATUS_DECLINED";
    ACDStatus["ACD_STATUS_ENDED"] = "ACD_STATUS_ENDED";
    ACDStatus["ACD_STATUS_FAILED"] = "ACD_STATUS_FAILED";
})(ACDStatus || (exports.ACDStatus = ACDStatus = {}));
