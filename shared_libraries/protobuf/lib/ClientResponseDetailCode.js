"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientResponseDetailCode = void 0;
var ClientResponseDetailCode;
(function (ClientResponseDetailCode) {
    ClientResponseDetailCode["DETAIL_CODE_NO_DETAILS"] = "DETAIL_CODE_NO_DETAILS";
    ClientResponseDetailCode["DETAIL_CODE_CLIENT_UPGRADE_RECOMMENDED"] = "DETAIL_CODE_CLIENT_UPGRADE_RECOMMENDED";
    ClientResponseDetailCode["EXCEED_USER_BOARDS_MAX"] = "EXCEED_USER_BOARDS_MAX";
    ClientResponseDetailCode["EXCEED_BOARD_PAGES_MAX"] = "EXCEED_BOARD_PAGES_MAX";
    ClientResponseDetailCode["EXCEED_BOARD_USERS_MAX"] = "EXCEED_BOARD_USERS_MAX";
    ClientResponseDetailCode["EXCEED_SESSION_USERS_MAX"] = "EXCEED_SESSION_USERS_MAX";
    ClientResponseDetailCode["EXCEED_GROUP_BOARDS_MAX"] = "EXCEED_GROUP_BOARDS_MAX";
    ClientResponseDetailCode["EXCEED_GROUP_USERS_MAX"] = "EXCEED_GROUP_USERS_MAX";
    ClientResponseDetailCode["EXCEED_UPLOAD_CLIENT_BODY_MAX"] = "EXCEED_UPLOAD_CLIENT_BODY_MAX";
    ClientResponseDetailCode["EXCEED_USER_CLOUD_MAX"] = "EXCEED_USER_CLOUD_MAX";
    ClientResponseDetailCode["EXCEED_NAME_LENGTH_MAX"] = "EXCEED_NAME_LENGTH_MAX";
    ClientResponseDetailCode["ERROR_USER_DISABLED"] = "ERROR_USER_DISABLED";
    ClientResponseDetailCode["ERROR_GROUP_SUBSCRIPTION_EXPIRED"] = "ERROR_GROUP_SUBSCRIPTION_EXPIRED";
    ClientResponseDetailCode["ERROR_SSO_ENFORCED"] = "ERROR_SSO_ENFORCED";
    ClientResponseDetailCode["ERROR_INVALID_BOARD_ID"] = "ERROR_INVALID_BOARD_ID";
    ClientResponseDetailCode["ERROR_VIRUS_DETECTED"] = "ERROR_VIRUS_DETECTED";
    ClientResponseDetailCode["ERROR_FILE_TYPE_NOT_SUPPORTED"] = "ERROR_FILE_TYPE_NOT_SUPPORTED";
    ClientResponseDetailCode["ERROR_PASSWORD_RULE_CONFLICT"] = "ERROR_PASSWORD_RULE_CONFLICT";
    ClientResponseDetailCode["ERROR_VERIFICATION_CODE_EXPIRED"] = "ERROR_VERIFICATION_CODE_EXPIRED";
    ClientResponseDetailCode["ERROR_BOARD_VIEW_TOKEN_EXPIRED"] = "ERROR_BOARD_VIEW_TOKEN_EXPIRED";
    ClientResponseDetailCode["ERROR_LOGIN_LOCKED"] = "ERROR_LOGIN_LOCKED";
    ClientResponseDetailCode["ERROR_USER_NOT_REGISTERED"] = "ERROR_USER_NOT_REGISTERED";
    ClientResponseDetailCode["ERROR_USER_NOT_GROUP_MEMBER"] = "ERROR_USER_NOT_GROUP_MEMBER";
    ClientResponseDetailCode["ERROR_USER_NOT_AUTHORIZED"] = "ERROR_USER_NOT_AUTHORIZED";
    ClientResponseDetailCode["ERROR_NOT_EMPTY"] = "ERROR_NOT_EMPTY";
    ClientResponseDetailCode["ERROR_USER_NOT_LOGIN"] = "ERROR_USER_NOT_LOGIN";
    ClientResponseDetailCode["ERROR_INVALID_USER_TYPE"] = "ERROR_INVALID_USER_TYPE";
    ClientResponseDetailCode["ERROR_2FA_REQUIRED"] = "ERROR_2FA_REQUIRED";
    ClientResponseDetailCode["EXCEED_FILE_SIZE_MAX"] = "EXCEED_FILE_SIZE_MAX";
    ClientResponseDetailCode["ERROR_INVALID_FILE_ENCODING"] = "ERROR_INVALID_FILE_ENCODING";
    ClientResponseDetailCode["EXCEED_FILE_LINES_MAX"] = "EXCEED_FILE_LINES_MAX";
    ClientResponseDetailCode["ERROR_INVALID_FILE_FORMAT"] = "ERROR_INVALID_FILE_FORMAT";
    ClientResponseDetailCode["ERROR_INVALID_FILE_HEADER"] = "ERROR_INVALID_FILE_HEADER";
    ClientResponseDetailCode["ERROR_INVALID_FIELD"] = "ERROR_INVALID_FIELD";
    ClientResponseDetailCode["ERROR_DUPLICATE_FIELD"] = "ERROR_DUPLICATE_FIELD";
    ClientResponseDetailCode["ERROR_EXPECTED_FIELD"] = "ERROR_EXPECTED_FIELD";
    ClientResponseDetailCode["AGENT_ERROR_INVALID_PASSCODE"] = "AGENT_ERROR_INVALID_PASSCODE";
    ClientResponseDetailCode["AGENT_ERROR_INVALID_TIMESTAMP"] = "AGENT_ERROR_INVALID_TIMESTAMP";
    ClientResponseDetailCode["AGENT_ERROR_INVALID_PATH"] = "AGENT_ERROR_INVALID_PATH";
    ClientResponseDetailCode["ERROR_INTEGRATION_INVALID_GROUP"] = "ERROR_INTEGRATION_INVALID_GROUP";
    ClientResponseDetailCode["ERROR_INTEGRATION_EXPIRED_GROUP_SUBSCRIPTION"] = "ERROR_INTEGRATION_EXPIRED_GROUP_SUBSCRIPTION";
    ClientResponseDetailCode["ERROR_INTEGRATION_INVALID_EXTERNAL_RESPONSE"] = "ERROR_INTEGRATION_INVALID_EXTERNAL_RESPONSE";
    ClientResponseDetailCode["ERROR_INTEGRATION_NOT_GROUP_MEMBER"] = "ERROR_INTEGRATION_NOT_GROUP_MEMBER";
    ClientResponseDetailCode["ERROR_INTEGRATION_EXCEED_GROUP_MEMBER_QUANTITY"] = "ERROR_INTEGRATION_EXCEED_GROUP_MEMBER_QUANTITY";
    ClientResponseDetailCode["ERROR_INTEGRATION_PENDING_GROUP_MEMBER"] = "ERROR_INTEGRATION_PENDING_GROUP_MEMBER";
    ClientResponseDetailCode["ERROR_SESSION_NOT_STARTED"] = "ERROR_SESSION_NOT_STARTED";
    ClientResponseDetailCode["ERROR_SESSION_ENDED"] = "ERROR_SESSION_ENDED";
    ClientResponseDetailCode["ERROR_SESSION_PASSWORD_PROTECTED"] = "ERROR_SESSION_PASSWORD_PROTECTED";
    ClientResponseDetailCode["ERROR_SESSION_WAITING_LIST_ENABLED"] = "ERROR_SESSION_WAITING_LIST_ENABLED";
    ClientResponseDetailCode["ERROR_SESSION_LOCKED"] = "ERROR_SESSION_LOCKED";
    ClientResponseDetailCode["ERROR_FLOW_STEP_INVALID_STATUS"] = "ERROR_FLOW_STEP_INVALID_STATUS";
})(ClientResponseDetailCode || (exports.ClientResponseDetailCode = ClientResponseDetailCode = {}));
