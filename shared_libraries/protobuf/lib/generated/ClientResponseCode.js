"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientResponseCode = void 0;
var ClientResponseCode;
(function (ClientResponseCode) {
    ClientResponseCode["RESPONSE_CONNECT_SUCCESS"] = "RESPONSE_CONNECT_SUCCESS";
    ClientResponseCode["RESPONSE_ERROR_UPGRADE_REQUIRED"] = "RESPONSE_ERROR_UPGRADE_REQUIRED";
    ClientResponseCode["RESPONSE_SUCCESS"] = "RESPONSE_SUCCESS";
    ClientResponseCode["RESPONSE_ACCEPTED"] = "RESPONSE_ACCEPTED";
    ClientResponseCode["RESPONSE_NO_CONTENT"] = "RESPONSE_NO_CONTENT";
    ClientResponseCode["RESPONSE_ERROR_STATUS_MOVED"] = "RESPONSE_ERROR_STATUS_MOVED";
    ClientResponseCode["RESPONSE_ERROR_X_ACCEL_REDIRECT"] = "RESPONSE_ERROR_X_ACCEL_REDIRECT";
    ClientResponseCode["RESPONSE_ERROR_TEMPORARY_REDIRECTION"] = "RESPONSE_ERROR_TEMPORARY_REDIRECTION";
    ClientResponseCode["RESPONSE_ERROR_INVALID_REQUEST"] = "RESPONSE_ERROR_INVALID_REQUEST";
    ClientResponseCode["RESPONSE_ERROR_INVALID_TOKEN"] = "RESPONSE_ERROR_INVALID_TOKEN";
    ClientResponseCode["RESPONSE_ERROR_PAYMENT_REQUIRED"] = "RESPONSE_ERROR_PAYMENT_REQUIRED";
    ClientResponseCode["RESPONSE_ERROR_PERMISSION"] = "RESPONSE_ERROR_PERMISSION";
    ClientResponseCode["RESPONSE_ERROR_NOT_FOUND"] = "RESPONSE_ERROR_NOT_FOUND";
    ClientResponseCode["RESPONSE_ERROR_INVALID_OBJECT"] = "RESPONSE_ERROR_INVALID_OBJECT";
    ClientResponseCode["RESPONSE_ERROR_TIMEOUT"] = "RESPONSE_ERROR_TIMEOUT";
    ClientResponseCode["RESPONSE_ERROR_CONFLICT"] = "RESPONSE_ERROR_CONFLICT";
    ClientResponseCode["RESPONSE_ERROR_PRECONDITION_FAILED"] = "RESPONSE_ERROR_PRECONDITION_FAILED";
    ClientResponseCode["RESPONSE_ERROR_EXCEED_LIMIT"] = "RESPONSE_ERROR_EXCEED_LIMIT";
    ClientResponseCode["RESPONSE_ERROR_TOO_MANY_REQUESTS"] = "RESPONSE_ERROR_TOO_MANY_REQUESTS";
    ClientResponseCode["RESPONSE_ERROR_FAILED"] = "RESPONSE_ERROR_FAILED";
    ClientResponseCode["RESPONSE_ERROR_BAD_GATEWAY"] = "RESPONSE_ERROR_BAD_GATEWAY";
    ClientResponseCode["RESPONSE_ERROR_SERVICE_UNAVAILABLE"] = "RESPONSE_ERROR_SERVICE_UNAVAILABLE";
    ClientResponseCode["RESPONSE_SUBSCRIPTION_DATA"] = "RESPONSE_SUBSCRIPTION_DATA";
    ClientResponseCode["RESPONSE_CONNECTION_TOKEN_VERIFIED"] = "RESPONSE_CONNECTION_TOKEN_VERIFIED";
    ClientResponseCode["RESPONSE_ERROR_DISCONNECTED"] = "RESPONSE_ERROR_DISCONNECTED";
})(ClientResponseCode || (exports.ClientResponseCode = ClientResponseCode = {}));
