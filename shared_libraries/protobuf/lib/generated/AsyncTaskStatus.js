"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AsyncTaskStatus = void 0;
var AsyncTaskStatus;
(function (AsyncTaskStatus) {
    AsyncTaskStatus["ASYNC_TASK_STATUS_INVALID"] = "ASYNC_TASK_STATUS_INVALID";
    AsyncTaskStatus["ASYNC_TASK_STATUS_QUEUED"] = "ASYNC_TASK_STATUS_QUEUED";
    AsyncTaskStatus["ASYNC_TASK_STATUS_IN_PROGRESS"] = "ASYNC_TASK_STATUS_IN_PROGRESS";
    AsyncTaskStatus["ASYNC_TASK_STATUS_SUCCESS"] = "ASYNC_TASK_STATUS_SUCCESS";
    AsyncTaskStatus["ASYNC_TASK_STATUS_FAILED"] = "ASYNC_TASK_STATUS_FAILED";
})(AsyncTaskStatus || (exports.AsyncTaskStatus = AsyncTaskStatus = {}));
