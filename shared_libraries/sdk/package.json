{"name": "@moxo/sdk", "version": "0.0.1", "module": "lib/index.js", "type": "module", "private": true, "types": "lib/index.d.ts", "files": ["lib", "es", "types"], "sideEffects": false, "license": "MIT", "scripts": {"build": "rm -rf lib/ && tsc -p tsconfig.json", "test": "cross-env NODE_ENV=test jest --config .jest.js", "dev": "vite", "preview": "vite preview"}, "devDependencies": {"@moxo/tsconfig": "workspace:*", "@types/lodash": "^4.14.165", "@types/superagent": "^8.1.9", "lodash": "^4.17.15", "type-fest": "^4.41.0", "typescript": "^5.8.3", "vite": "^6.3.5"}, "dependencies": {"@moxo/proto": "workspace:*", "@moxo/shared": "workspace:*", "@moxo/core": "workspace:*", "protobufjs": "^7.5.0", "superagent": "^10.2.2"}, "description": "moxo sdk"}