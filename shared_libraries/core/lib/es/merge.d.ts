import { ActionObject, Board, Group, User } from '@moxo/proto';
interface CacheObjectData {
    revision?: number;
    local_revision?: number;
    is_deleted?: boolean;
    sequence?: string;
    [key: string]: any;
}
export declare function mergeCacheObject(to: CacheObjectData, from: CacheObjectData, objType?: string): void;
export declare function mergeUserObject(to: User, from: User): void;
export declare function mergeBoardObject(to: Board, from: Board): void;
export declare function mergeGroupObject(to: Group, from: Group): void;
export declare function mergeSessionObject(to: ActionObject, from: ActionObject): void;
export {};
//# sourceMappingURL=merge.d.ts.map