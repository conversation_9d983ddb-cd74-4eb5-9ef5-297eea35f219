import { ProtoBufDefineJSON } from '@moxo/proto/ProtoBufDefineJSON';
import cloneDeep from 'lodash/cloneDeep';
import { ActionObject, Board, Group, User } from '@moxo/proto';

// 定义类型接口
interface ProtoBufField {
  type: string;
  rule?: string;
  id: number;
}

interface ProtoBufMessage {
  fields?: Record<string, ProtoBufField>;
  edition?: string;
  values?: Record<string, number>; // for enum types
  options?: any;
}

interface CacheObjectData {
  revision?: number;
  local_revision?: number;
  is_deleted?: boolean;
  sequence?: string;
  [key: string]: any;
}

const allMessages: Record<string, ProtoBufMessage> = ProtoBufDefineJSON.nested as Record<
  string,
  ProtoBufMessage
>;
const controlFields = new Set([
  'id',
  'session_key',
  'sequence',
  'client_uuid',
  'created_time',
  'updated_time',
  'timestamp',
  'revision',
  'local_revision',
  'previous_revision',
  'is_deleted',
]);
const internalTypes = new Set([
  'string',
  'bool',
  'int32',
  'uint32',
  'int64',
  'uint64',
  'float',
  'double',
  'bytes',
]);

export function mergeCacheObject(
  to: CacheObjectData,
  from: CacheObjectData,
  objType: string = 'CacheObject',
) {
  let msg: ProtoBufMessage | undefined = allMessages[objType];
  if (!msg) {
    return;
  }
  let fields: Record<string, ProtoBufField> | undefined = msg.fields;
  if (!fields) {
    return;
  }

  if (!from.revision || from.revision <= (to.revision || 0)) {
    return;
  }

  if (from.is_deleted && fields.hasOwnProperty('is_deleted')) {
    // delete old items
    for (let p in to) {
      if (!controlFields.has(p)) {
        delete to[p];
      }
    }
    Object.assign(to, cloneDeep(from));
    return;
  }

  let shouldMergeLocalField = false;
  if (
    from.local_revision &&
    fields.hasOwnProperty('local_revision') &&
    (!to.local_revision || from.local_revision > to.local_revision)
  ) {
    shouldMergeLocalField = true;
  }

  Object.keys(fields).forEach((key) => {
    let isControlField = controlFields.has(key);
    if (isControlField) {
      if (from.hasOwnProperty(key)) {
        to[key] = from[key];
      }
      return;
    }

    let field = fields[key];
    let isRepeatedField = field.rule === 'repeated' ? true : false;
    let isInternalType = internalTypes.has(field.type);

    if (!isRepeatedField && !from.hasOwnProperty(key)) {
      return;
    }

    if (isInternalType) {
      if (shouldMergeLocalField) {
        if (isRepeatedField) {
          to[key] = cloneDeep(from[key]);
        } else {
          to[key] = from[key];
        }
      }
    } else {
      let subMsg: ProtoBufMessage | undefined = allMessages[field.type];
      if (!subMsg || !subMsg.fields) return;

      let isSubContainer = subMsg.fields.hasOwnProperty('local_revision');
      let isRevisionData = subMsg.fields.hasOwnProperty('revision');
      let isBundleData = !isSubContainer && !isRevisionData;

      if (isBundleData) {
        if (shouldMergeLocalField) {
          to[key] = cloneDeep(from[key]);
        }
        return;
      }

      if (!isRepeatedField) {
        if (isSubContainer && to[key]) {
          mergeCacheObject(to[key], from[key], field.type);
        } else if (isRevisionData) {
          to[key] = cloneDeep(from[key]);
        }
      } else {
        let fromArray = from[key];
        // let toArray = to[key];
        if (!Array.isArray(fromArray)) return;
        if (!Array.isArray(to[key])) to[key] = [];

        let seqMap = new Map<string, number>(); // key: seq, val: index
        const toArray = to[key] as CacheObjectData[];
        for (let i = 0; i < toArray.length; i++) {
          let seq = toArray[i].sequence;
          if (seq) {
            seqMap.set(seq, i);
          }
        }

        let matchedItems: number[] = [];
        for (let i = 0; i < fromArray.length; i++) {
          let seq = (fromArray[i] as CacheObjectData).sequence;
          if (!seq) continue;

          if (seqMap.has(seq)) {
            matchedItems.push(seqMap.get(seq)!);
          } else {
            matchedItems.push(-1);
          }
        }

        for (let i = 0; i < matchedItems.length; i++) {
          if (matchedItems[i] === -1) {
            // new item
            (to[key] as any[]).push(cloneDeep(fromArray[i]));
          } else {
            // update item
            if (isSubContainer) {
              mergeCacheObject(
                toArray[matchedItems[i]],
                fromArray[i] as CacheObjectData,
                field.type,
              );
            } else if (isRevisionData) {
              toArray[matchedItems[i]] = cloneDeep(fromArray[i]);
            }
          }
        }
      }
    }
  });
}

export function mergeUserObject(to: User, from: User) {
  mergeCacheObject(to as CacheObjectData, from as CacheObjectData, 'User');
}

export function mergeBoardObject(to: Board, from: Board) {
  mergeCacheObject(to as CacheObjectData, from as CacheObjectData, 'Board');
}

export function mergeGroupObject(to: Group, from: Group) {
  mergeCacheObject(to as CacheObjectData, from as CacheObjectData, 'Group');
}

export function mergeSessionObject(to: ActionObject, from: ActionObject) {
  mergeCacheObject(to as CacheObjectData, from as CacheObjectData, 'ActionObject');
}
